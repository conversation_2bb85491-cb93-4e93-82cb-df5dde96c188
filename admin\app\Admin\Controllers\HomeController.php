<?php

namespace App\Admin\Controllers;

use App\Admin\Metrics\Examples;
use App\Http\Controllers\Controller;
use Dcat\Admin\Http\Controllers\Dashboard;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;

use Illuminate\Support\Facades\Redis;
use App\Models\SeoTotal;

class HomeController extends Controller
{
    public function index(Content $content)
    {
        return $content
            ->header('Dashboard')
            ->description('Description...')
            ->body(function (Row $row) {
                $row->column(12, function (Column $column) {
                    $column->row(new Examples\Total());
                });
            });
    }

    public function refresh()
    {
        $total_list = Redis::keys("total:*");
        $processed_count = 0;
        $error_count = 0;

        foreach ($total_list as $keyName) {
            try {
                $count = Redis::get($keyName);
                $info = explode(':', $keyName);

                // 确保键格式正确
                if (count($info) < 4) {
                    $error_count++;
                    continue;
                }

                $date = $info[1];
                $site = $info[2];
                $t = $info[3];

                // 验证数据有效性
                if (!is_numeric($count) || $count <= 0) {
                    Redis::del($keyName);
                    $error_count++;
                    continue;
                }

                // 🔧 修复：使用累加模式而不是覆盖模式
                $existing = SeoTotal::where(['date' => $date, 'site' => $site])->first();

                if ($existing) {
                    // 更新现有记录（累加）
                    if ($t == 1) {
                        $existing->jump += $count;
                    } else {
                        $existing->spider += $count;
                    }
                    $existing->save();
                } else {
                    // 创建新记录
                    $data = [
                        'date' => $date,
                        'site' => $site,
                        'jump' => $t == 1 ? $count : 0,
                        'spider' => $t == 0 ? $count : 0,
                    ];
                    SeoTotal::create($data);
                }

                // 🔧 修复：同步成功后立即删除Redis数据，避免重复计算
                Redis::del($keyName);
                $processed_count++;

            } catch (\Exception $e) {
                $error_count++;
            }
        }

        return response()->json([
            'success' => true,
            'message' => "统计数据刷新完成 - 成功: $processed_count, 错误: $error_count"
        ]);
    }
}
