<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SpiderLog;
use Carbon\Carbon;

class CleanSpiderLogs extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'spider:clean {--days=30 : 保留天数，超过此天数的日志将被删除}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理过期的蜘蛛爬行日志';

    /**
     * 创建命令
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $days = (int)$this->option('days');
        if($days <= 0) {
            $this->error('保留天数必须大于0');
            return 1;
        }
        
        $this->info("开始清理 {$days} 天前的蜘蛛爬行日志...");
        
        // 计算截止日期
        $cutoffDate = Carbon::now()->subDays($days)->startOfDay();
        
        // 删除截止日期之前的记录
        $count = SpiderLog::where('created_at', '<', $cutoffDate)->delete();
        
        $this->info("清理完成！已删除 {$count} 条过期记录。");
        
        return 0;
    }
} 