<?php

namespace App\Console\Commands;

use App\Models\SeoRegion;
use App\Models\SeoSite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;

class SeoRegionCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'seo:region 
                            {action : 操作类型 (create|migrate|validate|sync|stats|cleanup)}
                            {--region= : 地区代码}
                            {--from= : 源地区代码}
                            {--to= : 目标地区代码}
                            {--force : 强制执行}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '地区SEO数据管理工具';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $action = $this->argument('action');
        
        switch ($action) {
            case 'create':
                return $this->createRegionDirectories();
            case 'migrate':
                return $this->migrateRegionData();
            case 'validate':
                return $this->validateRegionData();
            case 'sync':
                return $this->syncRegionData();
            case 'stats':
                return $this->showRegionStats();
            case 'cleanup':
                return $this->cleanupRegionData();
            default:
                $this->error("未知操作: {$action}");
                return 1;
        }
    }

    /**
     * 创建地区目录结构
     */
    protected function createRegionDirectories()
    {
        $regionCode = $this->option('region');
        
        if (!$regionCode) {
            // 为所有启用的地区创建目录
            $regions = SeoRegion::where('status', 1)->get();
        } else {
            $regions = SeoRegion::where('code', $regionCode)->where('status', 1)->get();
        }
        
        if ($regions->isEmpty()) {
            $this->error('未找到有效的地区');
            return 1;
        }
        
        foreach ($regions as $region) {
            $this->info("为地区 {$region->code} ({$region->name}) 创建目录...");

            // 使用新的路径结构: app/{region_code}/data
            $dataPath = "app/{$region->code}/data";
            if (!File::exists($dataPath)) {
                File::makeDirectory($dataPath, 0755, true);
                $this->line("  ✓ 创建数据目录: {$dataPath}");
            } else {
                $this->line("  - 数据目录已存在: {$dataPath}");
            }

            // 使用新的路径结构: app/{region_code}/template
            $templatePath = "app/{$region->code}/template";
            if (!File::exists($templatePath)) {
                File::makeDirectory($templatePath, 0755, true);

                // 创建子目录
                File::makeDirectory("app/{$region->code}/template/page", 0755, true);
                File::makeDirectory("app/{$region->code}/template/home", 0755, true);
                
                $this->line("  ✓ 创建模板目录: {$templatePath}");
            } else {
                $this->line("  - 模板目录已存在: {$templatePath}");
            }
            
            // 如果是新地区且不是默认地区，从默认地区复制文件
            if ($region->code !== 'default' && !File::exists("{$dataPath}/keyword.txt")) {
                $this->copyFromDefault($region->code);
            }
        }
        
        $this->info('目录创建完成');
        return 0;
    }

    /**
     * 从默认地区复制文件 - 使用新路径结构
     */
    protected function copyFromDefault($regionCode)
    {
        // 新路径结构
        $defaultDataPath = "app/default/data";
        $defaultTemplatePath = "app/default/template";

        $regionDataPath = "app/{$regionCode}/data";
        $regionTemplatePath = "app/{$regionCode}/template";

        // 复制数据文件
        if (File::exists($defaultDataPath)) {
            $files = File::files($defaultDataPath);
            foreach ($files as $file) {
                $fileName = $file->getFilename();
                File::copy($file->getPathname(), "{$regionDataPath}/{$fileName}");
            }
            $this->line("  ✓ 从默认地区复制数据文件");
        }

        // 复制模板文件
        if (File::exists($defaultTemplatePath)) {
            $this->copyDirectory($defaultTemplatePath, $regionTemplatePath);
            $this->line("  ✓ 从默认地区复制模板文件");
        }
    }

    /**
     * 递归复制目录
     */
    protected function copyDirectory($source, $destination)
    {
        if (!File::exists($destination)) {
            File::makeDirectory($destination, 0755, true);
        }
        
        $files = File::allFiles($source);
        foreach ($files as $file) {
            $relativePath = $file->getRelativePathname();
            $destPath = "{$destination}/{$relativePath}";
            
            // 确保目标目录存在
            $destDir = dirname($destPath);
            if (!File::exists($destDir)) {
                File::makeDirectory($destDir, 0755, true);
            }
            
            File::copy($file->getPathname(), $destPath);
        }
    }

    /**
     * 迁移地区数据
     */
    protected function migrateRegionData()
    {
        $from = $this->option('from');
        $to = $this->option('to');
        
        if (!$from || !$to) {
            $this->error('请指定源地区和目标地区: --from=source --to=target');
            return 1;
        }
        
        $fromRegion = SeoRegion::where('code', $from)->first();
        $toRegion = SeoRegion::where('code', $to)->first();
        
        if (!$fromRegion || !$toRegion) {
            $this->error('源地区或目标地区不存在');
            return 1;
        }
        
        $this->info("开始迁移数据: {$from} -> {$to}");
        
        // 迁移网站数据
        $siteCount = SeoSite::where('region_code', $from)->count();
        if ($siteCount > 0) {
            if ($this->confirm("将 {$siteCount} 个网站从 {$from} 迁移到 {$to}？")) {
                SeoSite::where('region_code', $from)->update(['region_code' => $to]);
                $this->info("✓ 迁移了 {$siteCount} 个网站");
            }
        }
        
        // 迁移文件数据 - 使用新路径结构
        $fromDataPath = "app/{$from}/data";
        $toDataPath = "app/{$to}/data";

        if (File::exists($fromDataPath) && $this->confirm("迁移数据文件？")) {
            if (!File::exists($toDataPath)) {
                File::makeDirectory($toDataPath, 0755, true);
            }
            $this->copyDirectory($fromDataPath, $toDataPath);
            $this->info("✓ 迁移了数据文件");
        }

        $fromTemplatePath = "app/{$from}/template";
        $toTemplatePath = "app/{$to}/template";

        if (File::exists($fromTemplatePath) && $this->confirm("迁移模板文件？")) {
            if (!File::exists($toTemplatePath)) {
                File::makeDirectory($toTemplatePath, 0755, true);
            }
            $this->copyDirectory($fromTemplatePath, $toTemplatePath);
            $this->info("✓ 迁移了模板文件");
        }
        
        $this->info('数据迁移完成');
        return 0;
    }

    /**
     * 验证地区数据
     */
    protected function validateRegionData()
    {
        $regionCode = $this->option('region');
        
        if ($regionCode) {
            $regions = SeoRegion::where('code', $regionCode)->get();
        } else {
            $regions = SeoRegion::where('status', 1)->get();
        }
        
        $issues = [];

        foreach ($regions as $region) {
            $this->info("验证地区: {$region->code} ({$region->name})");

            // 检查目录 - 使用新路径结构
            $dataPath = "app/{$region->code}/data";
            $templatePath = "app/{$region->code}/template";
            
            if (!File::exists($dataPath)) {
                $issues[] = "地区 {$region->code} 缺少数据目录: {$dataPath}";
            }
            
            if (!File::exists($templatePath)) {
                $issues[] = "地区 {$region->code} 缺少模板目录: {$templatePath}";
            }
            
            // 检查必要文件
            $requiredDataFiles = ['keyword.txt', 'title.txt', 'juzi.txt'];
            foreach ($requiredDataFiles as $file) {
                $filePath = "{$dataPath}/{$file}";
                if (!File::exists($filePath)) {
                    $issues[] = "地区 {$region->code} 缺少数据文件: {$file}";
                } elseif (File::size($filePath) == 0) {
                    $issues[] = "地区 {$region->code} 数据文件为空: {$file}";
                }
            }
            
            // 检查模板文件
            $requiredTemplateDirs = ['page', 'home'];
            foreach ($requiredTemplateDirs as $dir) {
                $dirPath = "{$templatePath}/{$dir}";
                if (!File::exists($dirPath)) {
                    $issues[] = "地区 {$region->code} 缺少模板目录: {$dir}";
                } else {
                    $templates = File::files($dirPath);
                    if (empty($templates)) {
                        $issues[] = "地区 {$region->code} 模板目录为空: {$dir}";
                    }
                }
            }
            
            // 检查网站数据
            $siteCount = SeoSite::where('region_code', $region->code)->count();
            $activeSiteCount = SeoSite::where('region_code', $region->code)->where('state', 1)->count();
            
            $this->line("  网站总数: {$siteCount}, 启用: {$activeSiteCount}");
        }
        
        if (empty($issues)) {
            $this->info('✓ 所有地区数据验证通过');
            return 0;
        } else {
            $this->error('发现以下问题:');
            foreach ($issues as $issue) {
                $this->line("  ✗ {$issue}");
            }
            return 1;
        }
    }

    /**
     * 同步地区数据
     */
    protected function syncRegionData()
    {
        $from = $this->option('from');
        $to = $this->option('to');
        
        if (!$from || !$to) {
            $this->error('请指定源地区和目标地区: --from=source --to=target');
            return 1;
        }
        
        $this->info("同步地区数据: {$from} -> {$to}");

        // 使用新路径结构
        $fromDataPath = "app/{$from}/data";
        $toDataPath = "app/{$to}/data";

        if (!File::exists($fromDataPath)) {
            $this->error("源地区数据目录不存在: {$fromDataPath}");
            return 1;
        }

        if (!File::exists($toDataPath)) {
            File::makeDirectory($toDataPath, 0755, true);
        }

        // 同步数据文件
        $files = File::files($fromDataPath);
        foreach ($files as $file) {
            $fileName = $file->getFilename();
            $targetFile = "{$toDataPath}/{$fileName}";

            if (!File::exists($targetFile) || $this->option('force')) {
                File::copy($file->getPathname(), $targetFile);
                $this->line("  ✓ 同步文件: {$fileName}");
            }
        }

        // 同步模板文件
        $fromTemplatePath = "app/{$from}/template";
        $toTemplatePath = "app/{$to}/template";
        
        if (File::exists($fromTemplatePath)) {
            $this->copyDirectory($fromTemplatePath, $toTemplatePath);
            $this->line("  ✓ 同步模板文件");
        }
        
        $this->info('数据同步完成');
        return 0;
    }

    /**
     * 显示地区统计
     */
    protected function showRegionStats()
    {
        $regions = SeoRegion::with([])
            ->selectRaw('seo_regions.*, 
                (SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code) as site_count,
                (SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code AND seo_site.state = 1) as active_site_count')
            ->orderBy('priority')
            ->get();
        
        $this->info('地区统计信息:');
        $this->line('');
        
        $headers = ['地区代码', '地区名称', '状态', '总网站', '启用网站', '使用率', '数据目录', '模板目录'];
        $rows = [];
        
        $dataBasePath = env('APP_DATA', 'app/data');
        $templateBasePath = env('APP_TEMPLATE', 'app/template');
        
        foreach ($regions as $region) {
            $usage = $region->site_count > 0 ? round(($region->active_site_count / $region->site_count) * 100, 1) : 0;
            
            $dataExists = File::exists("{$dataBasePath}/{$region->code}") ? '✓' : '✗';
            $templateExists = File::exists("{$templateBasePath}/{$region->code}") ? '✓' : '✗';
            
            $rows[] = [
                $region->code,
                $region->name,
                $region->status ? '启用' : '禁用',
                $region->site_count,
                $region->active_site_count,
                "{$usage}%",
                $dataExists,
                $templateExists
            ];
        }
        
        $this->table($headers, $rows);
        
        // 总计
        $totalSites = $regions->sum('site_count');
        $totalActiveSites = $regions->sum('active_site_count');
        $totalRegions = $regions->where('status', 1)->count();
        
        $this->line('');
        $this->info("总计: {$totalRegions} 个启用地区, {$totalSites} 个网站, {$totalActiveSites} 个启用网站");
        
        return 0;
    }

    /**
     * 清理地区数据
     */
    protected function cleanupRegionData()
    {
        $this->info('清理无效的地区数据...');
        
        // 清理无效的网站地区引用
        $invalidSites = SeoSite::whereNotIn('region_code', function($query) {
            $query->select('code')->from('seo_regions')->where('status', 1);
        })->count();
        
        if ($invalidSites > 0) {
            if ($this->confirm("发现 {$invalidSites} 个网站引用了无效地区，是否将它们设置为默认地区？")) {
                SeoSite::whereNotIn('region_code', function($query) {
                    $query->select('code')->from('seo_regions')->where('status', 1);
                })->update(['region_code' => 'default']);
                
                $this->info("✓ 修复了 {$invalidSites} 个网站的地区引用");
            }
        }
        
        // 清理空的地区目录 - 使用新路径结构
        $activeCodes = SeoRegion::where('status', 1)->pluck('code')->toArray();

        // 检查app目录下的地区目录
        if (File::exists('app')) {
            $regionDirs = File::directories('app');
            foreach ($regionDirs as $dir) {
                $regionCode = basename($dir);
                if (!in_array($regionCode, $activeCodes)) {
                    if ($this->confirm("删除无效地区目录: {$regionCode}？")) {
                        File::deleteDirectory($dir);
                        $this->line("✓ 删除目录: {$dir}");
                    }
                }
            }
        }
        
        $this->info('清理完成');
        return 0;
    }
}
