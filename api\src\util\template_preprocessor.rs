use regex::Regex;
use lazy_static::lazy_static;

// 模板相关结构定义
#[derive(<PERSON><PERSON>, Debug)]
pub enum TagType {
    Static,
    Dynamic,
    Parameterized(String),
    Fixed,
    JsonSpecial,
}

#[derive(Clone, Debug)]
pub enum TemplateFragment {
    Static(String),
    Tag(String, TagType),
    JsonBlock {
        prefix: String,
        content: Vec<TemplateFragment>,
        suffix: String,
    },
}

// 预处理模板结构
#[derive(Clone, Debug)]
pub struct ProcessedTemplate {
    // 原始模板文本（保留用于兼容和调试）
    pub original: String,
    // 模板片段序列
    pub fragments: Vec<TemplateFragment>,
}

// 预处理模板函数
pub fn preprocess_template(template: &str) -> ProcessedTemplate {
    lazy_static! {
        static ref JSON_START_REGEX: Regex = Regex::new(r#"<script\s+type=["']application/ld\+json["']>"#).unwrap();
        static ref JSON_END_REGEX: Regex = Regex::new(r"</script>").unwrap();
    }

    let mut fragments = Vec::new();
    let mut cursor = 0;

    // 查找所有JSON块
    while let Some(start_match) = JSON_START_REGEX.find(&template[cursor..]) {
        let start_pos = cursor + start_match.start();
        let start_end = cursor + start_match.end();

        // 添加JSON块之前的静态内容
        if start_pos > cursor {
            add_static_and_tag_fragments(&template[cursor..start_pos], &mut fragments);
        }

        // 查找对应的结束标签
        if let Some(end_match) = JSON_END_REGEX.find(&template[start_end..]) {
            let end_start = start_end + end_match.start();
            let end_end = start_end + end_match.end();

            // 处理JSON块内容
            let json_content = &template[start_end..end_start];
            let mut json_fragments = Vec::new();
            add_static_and_tag_fragments(json_content, &mut json_fragments);

            // 创建JSON块片段
            fragments.push(TemplateFragment::JsonBlock {
                prefix: template[start_pos..start_end].to_string(),
                content: json_fragments,
                suffix: template[end_start..end_end].to_string(),
            });

            cursor = end_end;
        } else {
            // 没有找到结束标签，将剩余内容作为普通内容处理
            add_static_and_tag_fragments(&template[start_pos..], &mut fragments);
            break;
        }
    }

    // 处理剩余的内容
    if cursor < template.len() {
        add_static_and_tag_fragments(&template[cursor..], &mut fragments);
    }

    ProcessedTemplate {
        original: template.to_string(),
        fragments,
    }
}

fn add_static_and_tag_fragments(content: &str, fragments: &mut Vec<TemplateFragment>) {
    let mut cursor = 0;

    while let Some(tag_match) = TAG_REGEX.find(&content[cursor..]) {
        let tag_start = cursor + tag_match.start();
        let tag_end = cursor + tag_match.end();

        // 添加标签前的静态内容
        if tag_start > cursor {
            let static_content = &content[cursor..tag_start];
            if !static_content.is_empty() {
                fragments.push(TemplateFragment::Static(static_content.to_string()));
            }
        }

        // 添加标签
        let tag = &content[tag_start..tag_end];
        let tag_type = analyze_tag_type(tag);



        fragments.push(TemplateFragment::Tag(tag.to_string(), tag_type));

        cursor = tag_end;
    }

    // 添加最后的静态内容
    if cursor < content.len() {
        let static_content = &content[cursor..];
        if !static_content.is_empty() {
            fragments.push(TemplateFragment::Static(static_content.to_string()));
        }
    }
}

fn analyze_tag_type(tag: &str) -> TagType {
    // 检查是否是参数化标签
    if tag.starts_with("{随机数字") || tag.starts_with("{随机字母") {
        // 提取参数
        if let Some(start) = tag.find('(') {
            if let Some(end) = tag.rfind(')') {
                // 使用字符安全的方式提取参数
                let tag_chars: Vec<char> = tag.chars().collect();
                let start_char_pos = tag.chars().take(start + 1).count();
                let end_char_pos = tag.chars().take(end).count();
                if start_char_pos < end_char_pos && end_char_pos <= tag_chars.len() {
                    let param: String = tag_chars[start_char_pos..end_char_pos].iter().collect();
                    return TagType::Parameterized(param);
                }
            }
        }
        
        // 检查是否有数字参数（如 {随机数字5}）
        // 使用字符迭代器来安全处理中文字符，移除 { 和 }
        let tag_chars: Vec<char> = tag.chars().collect();
        if tag_chars.len() > 2 {
            let chars_without_braces: Vec<char> = tag_chars[1..tag_chars.len()-1].iter().cloned().collect();
            if let Some(digit_pos) = chars_without_braces.iter().position(|c| c.is_ascii_digit()) {
                let param: String = chars_without_braces[digit_pos..].iter().collect();
                return TagType::Parameterized(param);
            }
        }
        
        // 检查是否有范围参数（如 {随机数字100-1000}）
        if tag.contains('-') {
            // 使用字符迭代器来安全处理中文字符
            let tag_chars: Vec<char> = tag.chars().collect();
            if let Some(dash_pos) = tag_chars.iter().rposition(|&c| c == '-') {
                // 在破折号之前查找数字
                let before_dash_chars: Vec<char> = tag_chars[..dash_pos].iter().cloned().collect();
                if let Some(digit_pos) = before_dash_chars.iter().rposition(|c| c.is_ascii_digit()) {
                    // 从数字开始到标签结束（排除最后的}）
                    let param_chars: Vec<char> = tag_chars[digit_pos..tag_chars.len()-1].iter().cloned().collect();
                    let param: String = param_chars.iter().collect();
                    return TagType::Parameterized(param);
                }
            }
        }
    }

    // 检查是否是动态标签（需要特殊处理的标签）
    match tag {
        "{随机链接}" | "{外链}" | "{随机标题}" | "{随机句子}" | "{随机关键词}" |
        "{随机图片}" | "{随机字母}" | "{随机作者}" | "{随机视频}" | "{随机图标}" |
        "{栏目名}" | "{来源}" | "{城市}" => TagType::Dynamic,
        // 🔧 添加基础固定标签的分类
        "{固定城市}" | "{固定作者}" | "{固定图片}" | "{固定图标}" | "{当前域名}" => TagType::Fixed,
        _ => {
            // 🚀 检查是否是编号标签（懒加载）
            if tag.starts_with("{关键词") && tag.len() > 5 {
                // 检查关键词编号：1-10为静态，11+为动态
                if let Some(num_str) = tag.strip_prefix("{关键词").and_then(|s| s.strip_suffix("}")) {
                    if let Ok(num) = num_str.parse::<usize>() {
                        if num >= 1 && num <= 10 {
                            TagType::Static  // {关键词1-10} 预生成，静态
                        } else {
                            TagType::Dynamic  // {关键词11+} 懒加载，动态
                        }
                    } else {
                        TagType::Static
                    }
                } else {
                    TagType::Static
                }
            } else if (tag.starts_with("{标题") && tag.len() > 4) ||
                      (tag.starts_with("{描述") && tag.len() > 4) {
                TagType::Dynamic  // 标题和描述标签仍然是动态的
            } else if (tag.starts_with("{固定图片") && tag.len() > 6) ||
                      (tag.starts_with("{固定作者") && tag.len() > 6) ||
                      (tag.starts_with("{固定图标") && tag.len() > 6) {
                TagType::Fixed  // 🔧 修复：固定编号标签应该是Fixed类型
            } else {
                TagType::Static
            }
        }
    }
}

lazy_static! {
    static ref TAG_REGEX: Regex = Regex::new(r"\{[^{}]+\}").unwrap();
}
