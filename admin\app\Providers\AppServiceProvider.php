<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Blade;
use Carbon\Carbon;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // 注册时区转换Blade指令
        Blade::directive('apptime', function ($expression) {
            return "<?php echo to_app_timezone($expression)->format('Y-m-d H:i:s'); ?>";
        });
        
        // 注册日期格式化Blade指令
        Blade::directive('formatdate', function ($expression) {
            $args = explode(',', $expression);
            if (count($args) > 1) {
                return "<?php echo format_datetime({$args[0]}, {$args[1]}); ?>";
            }
            return "<?php echo format_datetime($expression); ?>";
        });
        
        // 注册时区信息Blade指令
        Blade::directive('timezone', function () {
            return "<?php echo config('app.timezone'); ?>";
        });
    }
}
