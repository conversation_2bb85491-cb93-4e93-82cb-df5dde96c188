<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SeoCache;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use App\Models\Mongo;

class SeoCacheController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SeoCache(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('host');
            $grid->column('uri');
            $grid->column('hash');
            // $grid->column('content');
            $grid->column('expired');
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SeoCache(), function (Show $show) {
            $show->field('id');
            $show->field('host');
            $show->field('uri');
            $show->field('hash');
            $show->field('content');
            $show->field('expired');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SeoCache(), function (Form $form) {
            $form->display('id');
            $form->display('host');
            $form->display('uri');
            $form->display('hash');
            $form->textarea('content');
            $form->text('expired');
        });
    }
}
