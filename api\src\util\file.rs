use std::path::Path;

use tokio;
use tokio::fs;
use tokio::fs::File;
use tokio::io::{self, AsyncReadExt, AsyncWriteExt, BufWriter};

pub async fn read_file(path: &str) -> Result<String, Box<dyn std::error::Error>> {
    let mut file = File::open(path).await?;
    let mut content = String::new();
    file.read_to_string(&mut content).await?;
    Ok(content)
}
pub async fn read_file_bytes(path: &str) -> Result<Vec<u8>, Box<dyn std::error::Error>> {
    let mut file = File::open(path).await?;
    let mut content = Vec::new();
    file.read_to_end(&mut content).await?;

    Ok(content)
}

pub async fn write_file_bytes(path: &String, contents: &[u8]) -> io::Result<()> {
    create_dir_if_not_exists(&path).await?;

    let file = File::create(path).await?;
    let mut writer = BufWriter::new(file);
    writer.write_all(contents).await?;
    writer.flush().await
}

pub async fn write_file(path: &String, contents: &[u8]) -> io::Result<()> {
    create_dir_if_not_exists(&path).await?;

    let file = File::create(path).await?;
    let mut writer = BufWriter::new(file);

    writer.write_all(contents).await?;
    writer.flush().await
}

async fn create_dir_if_not_exists(path: &str) -> Result<(), std::io::Error> {
    let path = Path::new(path);
    if let Some(parent) = path.parent() {
        if !parent.exists() {
            fs::create_dir_all(parent).await?;
        }
    }
    Ok(())
}

// // 压缩文件内容
// fn compress_content(content: &[u8]) -> Result<Vec<u8>, std::io::Error> {
//     let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
//     encoder.write_all(content)?;
//     Ok(encoder.finish()?)
// }

// // 解压缩文件内容
// fn decompress_content(compressed_content: &[u8]) -> Result<String, std::io::Error> {
//     let mut decoder = GzDecoder::new(compressed_content);
//     let mut content = String::new();
//     decoder.read_to_string(&mut content)?;
//     Ok(content)
// }
