use actix_web::{http::KeepAlive, middleware::Compress, web, App, HttpServer};

use crate::database::{mongo::MongoClient, mysql, redis::RedisClient, check_and_setup_database};
use crate::entities::{AppState, SiteConfig, SiteConfigs, SystemConfig, RegionManager, Region};
use crate::routes::{default, region, region_timezone, sitemap, robots};
use crate::util::region_timezone_context::RegionTimezoneContext;
use crate::{GLOBAL_CONFIG, /* GOOGLEIP, */ RESOURCE, TEMPLATE_CONFIG, GEOIP_CHECKER};
// use sqlx::MySql;
use std::time::Duration;
use sqlx::Row;
use regex;
// use std::thread;
use tokio::time as tokio_time;

pub async fn start() -> std::io::Result<()> {
    dotenvy::dotenv().ok();
    let _ = &*RESOURCE;
    let _ = &*GLOBAL_CONFIG;
    
    // 设置全局时区（从环境变量中读取）
    let timezone = &GLOBAL_CONFIG.time_zone;
    println!("应用使用时区: {}", timezone);
    if !crate::util::time::set_timezone(timezone) {
        eprintln!("警告: 无法设置时区 '{}', 使用默认UTC时区", timezone);
    } else {
        // 添加调试信息，确认时区设置成功
        let current_tz = crate::util::time::get_timezone();
        println!("时区设置成功: {}", current_tz);

        // 测试时间戳和格式化
        let now = crate::util::time::now();
        let ts = crate::util::time::timestamp();
        println!("当前时间: {}, 时间戳: {}", now.format("%Y-%m-%d %H:%M:%S"), ts);
    }
    
    // 初始化GeoIP检查器
    let _ = &*GEOIP_CHECKER;
    // 不再需要初始化GoogleIP检查器
    // let _ = &*GOOGLEIP;
    // let _ = &*DATA_LIST;

    // 不再需要启动IP刷新循环
    // tokio::spawn(async {
    //     GOOGLEIP.start_refresh_loop().await;
    // });
    let mysql_options = sqlx::mysql::MySqlPoolOptions::new()
        .max_connections(20) // 限制最大连接数
        .idle_timeout(Some(Duration::from_secs(300))) // 空闲连接超时时间
        .acquire_timeout(Duration::from_secs(30)) // 获取连接超时时间
        .min_connections(5); // 最小连接数
    
    let mysql_pool = mysql_options
        .connect(&mysql::connection_string())
        .await
        .expect("Database connection failed");
    
    // 测试数据库连接
    if let Err(e) = crate::database::check_schema::test_database_connection(&mysql_pool).await {
        eprintln!("数据库连接测试失败: {}", e);
        return Err(std::io::Error::new(std::io::ErrorKind::Other, "数据库连接失败"));
    }

    // 检查并设置数据库结构
    if let Err(e) = check_and_setup_database(&mysql_pool).await {
        eprintln!("数据库结构检查失败: {}", e);
        return Err(std::io::Error::new(std::io::ErrorKind::Other, "数据库结构检查失败"));
    }
    
    // 加载名为"dll"的域名配置模板
    match load_template_config(&mysql_pool, "dll").await {
        Ok(template) => {
            println!("成功加载域名配置模板: {}", template.domain);
            let mut template_lock = TEMPLATE_CONFIG.write().await;
            *template_lock = Some(template);
        },
        Err(e) => {
            eprintln!("加载域名配置模板失败: {}", e);
        }
    }
    
    // Redis connector
    let redis_pool = RedisClient::new().await.expect("Redis connection failed");

    let site_configs = SiteConfigs::new(&mysql_pool).await.unwrap();
    let mongodb = MongoClient::get_instance().await.expect("MongoDB connection failed");
    
    // 从数据库加载域名同步记录
    default::load_sync_records(&mysql_pool).await;
    
    // 启动时迁移旧格式缓存
    if let Err(e) = mongodb.migrate_legacy_cache_on_startup().await {
        eprintln!("迁移旧格式缓存失败: {:?}", e);
    }

    // 创建MongoDB缓存集合的TTL索引
    if let Err(e) = mongodb.create_ttl_index("cache", "expiredAt").await {
        eprintln!("创建MongoDB缓存TTL索引失败: {:?}", e);
    } else {
        println!("MongoDB缓存TTL索引创建成功");
    }
    
    // 从数据库加载系统配置，如果表不存在则使用默认值
    let system_config = SystemConfig::init(&mysql_pool).await;

    // 初始化地区管理器
    let default_region = std::env::var("DEFAULT_REGION").unwrap_or_else(|_| "default".to_string());
    let region_manager = RegionManager::new(mysql_pool.clone(), default_region.clone());
    if let Err(e) = region_manager.initialize().await {
        eprintln!("地区管理器初始化失败: {}", e);
        return Err(std::io::Error::new(std::io::ErrorKind::Other, "地区管理器初始化失败"));
    }

    // 多地区资源管理已简化，使用Resource::new_for_region()方式
    println!("默认地区资源加载成功: {}", default_region);

    // 初始化地区时区上下文
    let default_timezone = GLOBAL_CONFIG.time_zone.clone();
    let region_timezone_context = RegionTimezoneContext::new(default_timezone);

    // 从数据库加载地区时区信息
    match Region::get_active_regions(&mysql_pool).await {
        Ok(regions) => {
            region_timezone_context.initialize_from_regions(regions).await;
            println!("地区时区上下文初始化成功");
        }
        Err(e) => {
            eprintln!("加载地区时区信息失败: {}", e);
        }
    }

    // 创建原始app_state
    let app_state = AppState {
        mysql: mysql_pool.clone(),
        redis: redis_pool.clone(),
        mongo: (*mongodb).clone(),
        system_config,
        region_manager,
        region_timezone_context,
    };
    
    // 为Actix Web创建包装的Data
    let app_state_data = web::Data::new(app_state);
    let site_configs_data = web::Data::new(site_configs);
    
    // 启动检查未爬行域名的定时任务
    let mysql_for_task = mysql_pool.clone();
    let redis_for_task = redis_pool.clone();
    tokio::spawn(async move {
        let mut interval = tokio_time::interval(Duration::from_secs(3600)); // 每小时检查一次
        loop {
            interval.tick().await;
            if let Err(e) = check_inactive_domains(&mysql_for_task, &redis_for_task).await {
                eprintln!("检查未爬行域名失败: {}", e);
            }
        }
    });
    
    // 启动批量处理后台任务
    crate::routes::default::start_batch_processor(app_state_data.clone(), site_configs_data.clone()).await;

    let bind_addr = &GLOBAL_CONFIG.app_config.bind_addr;
    println!("start server: {}", bind_addr);
    // Init App
    HttpServer::new(move || {
        App::new().wrap(Compress::default()).configure(|cfg| {
            app_config(cfg, app_state_data.clone().get_ref().clone(), site_configs_data.clone().get_ref().clone());
        })
    })
    .keep_alive(KeepAlive::Timeout(Duration::from_secs(60)))
    .bind(bind_addr)?
    .run()
    .await
}

/// 检查长时间未爬行的域名并将其状态设置为未通过
async fn check_inactive_domains(mysql_pool: &sqlx::MySqlPool, redis_client: &RedisClient) -> Result<(), sqlx::Error> {
    // 检查是否开启了自动清理功能
    if !GLOBAL_CONFIG.app_features.auto_clean_inactive_domains {
        println!("未爬行域名自动清理功能已关闭");
        return Ok(());
    }

    println!("开始检查未爬行域名...");
    
    // 获取所有状态为1（通过）的域名
    let domains = sqlx::query("SELECT id, host FROM seo_site WHERE state = 1")
        .fetch_all(mysql_pool)
        .await?;
    
    let mut processed_count = 0;
    let mut in_grace_period_count = 0;
    let mut set_inactive_count = 0;
    
    // 获取当前时间
    let now = crate::util::time::now_utc().timestamp();
    let inactive_timeout = 12 * 3600; // 12小时，单位秒
    let grace_period = 24 * 3600; // 修改为24小时宽限期（新添加域名）
    
    // 处理每个域名
    for domain in domains {
        let host: String = domain.try_get("host")?;
        let id: i32 = domain.try_get("id")?;
        processed_count += 1;
        
        // 检查该域名是否处于宽限期内
        let grace_key = format!("grace_period:{}", host);
        let in_grace_period = redis_client.get(&grace_key).await.is_some();
        
        if !in_grace_period {
            // 如果域名不在宽限期内，检查是否需要设置宽限期
            // 检查该域名是否是新添加的（通过查看是否已有last_time记录）
            let last_time_key = format!("lasttime:{}", host);
            let has_last_time = redis_client.get(&last_time_key).await.is_some();
            let mysql_has_last_time = sqlx::query("SELECT last_time FROM seo_site WHERE id = ? AND last_time > 0")
                .bind(id)
                .fetch_optional(mysql_pool)
                .await?
                .is_some();
                
            if !has_last_time && !mysql_has_last_time {
                // 没有last_time记录，说明是新域名，设置宽限期
                // 设置宽限期，过期时间为48小时
                let _ = redis_client.set(&grace_key, &now.to_string(), Some(grace_period)).await;
                in_grace_period_count += 1;
                continue; // 跳过此域名的检查
            }
            
            // 检查该域名的lasttime记录
            let last_time = redis_client.get(&last_time_key).await;
            
            match last_time {
                None => {
                    // Redis中没有记录，检查MySQL中的last_time字段
                    let mysql_last_time = sqlx::query("SELECT last_time FROM seo_site WHERE id = ?")
                        .bind(id)
                        .fetch_optional(mysql_pool)
                        .await?;
                    
                    match mysql_last_time {
                        Some(row) => {
                            let last_timestamp: Option<i64> = row.try_get("last_time").unwrap_or(None);
                            match last_timestamp {
                                Some(timestamp) if timestamp > 0 => {
                                    if now - timestamp > inactive_timeout {
                                        let _ = sqlx::query("UPDATE seo_site SET state = 0 WHERE id = ?")
                                            .bind(id)
                                            .execute(mysql_pool)
                                            .await?;
                                        set_inactive_count += 1;
                                    }
                                },
                                _ => {
                                    // MySQL中last_time为空或0，设置为未通过
                                    let _ = sqlx::query("UPDATE seo_site SET state = 0 WHERE id = ?")
                                        .bind(id)
                                        .execute(mysql_pool)
                                        .await?;
                                    set_inactive_count += 1;
                                }
                            }
                        },
                        None => {
                            // 查询出错，设置为未通过
                            let _ = sqlx::query("UPDATE seo_site SET state = 0 WHERE id = ?")
                                .bind(id)
                                .execute(mysql_pool)
                                .await?;
                            set_inactive_count += 1;
                        }
                    }
                },
                Some(time_str) => {
                    // 有last_time记录，检查是否超过12小时
                    if let Ok(last_timestamp) = time_str.parse::<i64>() {
                        if now - last_timestamp > inactive_timeout {
                            let _ = sqlx::query("UPDATE seo_site SET state = 0 WHERE id = ?")
                                .bind(id)
                                .execute(mysql_pool)
                                .await?;
                            set_inactive_count += 1;
                        }
                    }
                }
            }
        } else {
            in_grace_period_count += 1;
        }
    }
    
    println!("未爬行域名检查完成：处理 {} 个域名，{} 个在宽限期内，{} 个标记为未通过", 
             processed_count, in_grace_period_count, set_inactive_count);
    Ok(())
}

/// 从数据库加载指定名称的域名配置模板
pub async fn load_template_config(pool: &sqlx::MySqlPool, template_name: &str) -> Result<SiteConfig, sqlx::Error> {
    let row = sqlx::query("SELECT * FROM seo_moban WHERE name = ?")
        .bind(template_name)
        .fetch_one(pool)
        .await?;
    
    
    // 获取链接规则
    let generate_rules: Vec<String> = row
        .try_get::<String, _>("link_rules")
        .unwrap_or_default()
        .split('\n')
        .map(|s| s.trim().to_string())
        .filter(|s| !s.is_empty())
        .collect();
    
    // 获取跳转规则（模板中可能没有这个字段，暂设为空）
    let jump_rules_str: Vec<String> = Vec::new();
    
    // 处理URI规则
    let mut uri_rules = Vec::new();
    for uri_rule in &generate_rules {
        let uri_rule = crate::util::string::url::replace_url_tags(uri_rule);
        match regex::Regex::new(&uri_rule) {
            Ok(re) => uri_rules.push(re),
            Err(e) => {
                eprintln!("Invalid regex pattern '{}': {}", uri_rule, e);
                continue;
            }
        }
    }
    
    // 处理跳转规则
    let mut jump_rules = Vec::new();
    for uri_rule in &jump_rules_str {
        let uri_rule = crate::util::string::url::replace_url_tags(uri_rule);
        match regex::Regex::new(&uri_rule) {
            Ok(re) => jump_rules.push(re),
            Err(e) => {
                eprintln!("Invalid regex pattern '{}': {}", uri_rule, e);
                continue;
            }
        }
    }
    
    // 将数据库行转换为SiteConfig
    let config = SiteConfig {
        domain: "".to_string(), // 提供一个空字符串作为domain值
        generate_rules,
        https: false, // 默认值
        cache: row.try_get("open_cache").unwrap_or(false),
        page: row.try_get("open_page").unwrap_or(false),
        home: row.try_get("open_home").unwrap_or(false),
        link: row.try_get("open_link").unwrap_or(false),
        region_code: row.try_get("region_code").unwrap_or_else(|_| "default".to_string()),
        state: true, // 默认启用
        uri_rules,
        jump_rules,
    };
    
    Ok(config)
}

pub fn app_config(cfg: &mut web::ServiceConfig, app_state: AppState, domains: SiteConfigs) {
    cfg.app_data(web::Data::new(app_state))
        .app_data(web::Data::new(domains))
        .service(default::app)
        .service(default::refresh)
        .service(default::clear_sync_cache)
        .service(default::clear_geoip_cache)
        .service(default::clear_sync_times)
        .service(default::mongo_cache_info)
        .service(default::jump_script)
        .service(sitemap::generate_sitemap)
        .service(robots::generate_robots)
        .configure(region::config_region_routes)
        .configure(region_timezone::config_region_timezone_routes);
}
