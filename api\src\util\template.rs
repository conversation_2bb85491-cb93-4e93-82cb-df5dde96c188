use crate::entities::{RequestInfo, SiteConfig, SiteConfigs, Resource, Data};
use crate::util::template_preprocessor::{ProcessedTemplate, TemplateFragment, TagType, preprocess_template};

use chrono::DateTime;
use chrono_tz::Tz;
use lazy_static::lazy_static;
use rand::prelude::*;
use std::collections::HashMap;
use std::iter;

use super::string::url::replace_with_resource;

lazy_static! {
    static ref TAG_REPLACE_ONE: HashMap<String, fn(&mut ThreadRng) -> String> = {
        let m: HashMap<String, fn(&mut ThreadRng) -> String> = HashMap::new();
        // TAG_REPLACE_ONE现在只保留不在标签映射中处理的特殊标签
        // 其他标签都在create_tag_replace_map中统一处理
        m
    };
}

// 添加新的替换函数，使用预处理的模板结构
pub async fn replace_with_processed(
    processed_template: &ProcessedTemplate,
    request_info: &RequestInfo,
    site_config: &SiteConfig,
    domains: &SiteConfigs,
    keyword: &str,
    region_resource: &Resource,  // 🔧 移除Option，强制使用地区资源
    region_timezone_context: &crate::util::region_timezone_context::RegionTimezoneContext,
) -> String {
    let mut rng = thread_rng();

    // 🔧 统一使用地区资源初始化数据对象
    let mut data = if keyword.is_empty() {
        let random_keyword = region_resource.get_random_data(&mut rng, "keyword");
        Data::new_with_keyword_search(&random_keyword, region_resource)
    } else {
        Data::new_with_keyword_search(keyword, region_resource)
    };

    // 创建标签替换映射（使用地区时区）
    let region_timezone = site_config.region_code.as_str();
    let region_timezone_str = if !region_timezone.is_empty() {
        // 从地区时区上下文获取时区
        region_timezone_context.get_region_timezone(region_timezone).await
    } else {
        "UTC".to_string()
    };
    let now = crate::util::time::now_for_region(&region_timezone_str);
    let tag_replace = create_tag_replace_map(&now, request_info, &mut data, &keyword, &mut rng, region_resource);
    
    // 🔧 添加懒加载固定标签缓存
    let mut lazy_fixed_tags: HashMap<String, String> = HashMap::new();

    // 使用预处理的片段快速构建结果
    let mut result = String::with_capacity(processed_template.original.len());

    // 处理所有片段
    for fragment in &processed_template.fragments {
        match fragment {
            TemplateFragment::Static(text) => {
                // 静态文本直接添加
                result.push_str(text);
            },
            TemplateFragment::Tag(tag, tag_type) => {
                // 根据标签类型处理
                match tag_type {
                    TagType::Fixed => {
                        // 从预定义映射中获取值
                        if let Some(value) = tag_replace.get(tag) {
                            result.push_str(value);
                        } else if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                            // 🔧 从懒加载缓存中获取
                            result.push_str(cached_value);
                        } else {
                            // 🔧 懒加载：检查是否是固定编号标签
                            let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "pic"))
                            } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "icon"))
                            } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "author"))
                            } else {
                                None
                            };

                            if let Some(value) = generated_value {
                                // 缓存生成的值，确保页面内一致
                                lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                result.push_str(&value);
                            } else {
                                // 如果不是支持的固定标签，保留原标签
                                result.push_str(tag);
                            }
                        }
                    },
                    TagType::Dynamic => {
                        // 调用动态生成函数
                        if let Some(&generator) = TAG_REPLACE_ONE.get(tag) {
                            let random_value = generator(&mut rng);
                            result.push_str(&random_value);
                        } else if tag == "{随机链接}" {
                            let uri_rule = site_config.get_random_uri_rule(&mut rng);
                            let url = replace_with_resource(&uri_rule, &mut rng, region_resource);
                            result.push_str(&url);
                        } else if tag == "{随机标题}" {
                            // 🔧 统一使用地区资源
                            result.push_str(&data.get_title(&mut rng, region_resource));
                        } else if tag == "{随机句子}" {
                            // 🔧 统一使用地区资源生成随机句子
                            result.push_str(&region_resource.get_random_data(&mut rng, "juzi"));
                        } else if tag == "{随机关键词}" {
                            // 🔧 统一使用地区资源生成随机关键词
                            result.push_str(&region_resource.get_random_data(&mut rng, "keyword"));
                        } else if tag == "{外链}" {
                            // 随机选择一个已通过的网站，获取其URL链接规则来配置
                            let url = domains.get_random_domain(&mut rng).await;
                            result.push_str(&url);
                        } else if tag == "{随机字母}" {
                            result.push_str(&random_letters(&mut rng, 6));
                        } else if tag == "{随机图片}" {
                            // 🔧 统一使用地区资源生成随机图片
                            result.push_str(&region_resource.get_random_data(&mut rng, "pic"));
                        } else if tag == "{随机图标}" {
                            // 🔧 统一使用地区资源生成随机图标
                            result.push_str(&region_resource.get_random_data(&mut rng, "icon"));
                        } else if tag == "{随机作者}" {
                            // 🔧 统一使用地区资源生成随机作者
                            result.push_str(&region_resource.get_random_data(&mut rng, "author"));
                        } else if tag == "{随机视频}" {
                            // 🔧 统一使用地区资源生成随机视频
                            result.push_str(&region_resource.get_random_data(&mut rng, "video"));
                        } else if tag == "{栏目名}" {
                            // 🔧 统一使用地区资源生成随机栏目名
                            result.push_str(&region_resource.get_random_data(&mut rng, "column"));
                        } else if tag == "{来源}" {
                            // 🔧 统一使用地区资源生成随机来源
                            result.push_str(&region_resource.get_random_data(&mut rng, "source"));
                        } else if tag == "{城市}" {
                            // 🔧 统一使用地区资源生成随机城市
                            result.push_str(&region_resource.get_random_data(&mut rng, "city"));
                        } else if tag.starts_with("{关键词") && tag.ends_with("}") {
                            // 🚀 懒加载：动态生成编号关键词标签
                            if let Some(num_str) = tag.strip_prefix("{关键词").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 11 && num <= 2000 {  // 🔧 修复：只处理11-2000，1-10已预生成
                                        result.push_str(&region_resource.get_random_data(&mut rng, "keyword"));
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{标题") && tag.ends_with("}") {
                            // 🚀 懒加载：动态生成编号标题标签
                            if let Some(num_str) = tag.strip_prefix("{标题").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 2 && num <= 2000 {  // 🔧 修复：支持2-2000所有编号
                                        // 获取对应的关键词（动态生成或从预设获取）
                                        let keyword_for_title = if num <= 10 {
                                            // 1-10从预设获取
                                            let keyword_key = format!("{{关键词{}}}", num);
                                            tag_replace.get(&keyword_key).cloned()
                                                .unwrap_or_else(|| region_resource.get_random_data(&mut rng, "keyword"))
                                        } else {
                                            // 11+动态生成
                                            region_resource.get_random_data(&mut rng, "keyword")
                                        };

                                        let title_content = format!("{} - {}", keyword_for_title, region_resource.get_random_data(&mut rng, "title"));
                                        result.push_str(&title_content);
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{描述") && tag.ends_with("}") {
                            // 🚀 懒加载：动态生成编号描述标签
                            if let Some(num_str) = tag.strip_prefix("{描述").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 2 && num <= 2000 {  // 🔧 修复：支持2-2000所有编号
                                        // 获取对应的关键词（动态生成或从预设获取）
                                        let keyword_for_desc = if num <= 10 {
                                            // 1-10从预设获取
                                            let keyword_key = format!("{{关键词{}}}", num);
                                            tag_replace.get(&keyword_key).cloned()
                                                .unwrap_or_else(|| region_resource.get_random_data(&mut rng, "keyword"))
                                        } else {
                                            // 11+动态生成
                                            region_resource.get_random_data(&mut rng, "keyword")
                                        };

                                        let desc_content = format!("{}。{}", keyword_for_desc, region_resource.get_random_data(&mut rng, "juzi"));
                                        result.push_str(&desc_content);
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }

                        // 🔧 移除：固定标签现在由 TagType::Fixed 处理，避免重复
                        } else {
                            // 未处理的动态标签，保留原样
                            result.push_str(tag);
                        }
                    },
                    TagType::JsonSpecial => {
                        // JSON中的特殊标签，直接替换，不检查前置字符
                        if let Some(value) = tag_replace.get(tag) {
                            result.push_str(value);
                        } else if let Some(&generator) = TAG_REPLACE_ONE.get(tag) {
                            let random_value = generator(&mut rng);
                            result.push_str(&random_value);
                        } else {
                            // 其他处理逻辑与Dynamic类似，但无需检查前置字符
                            if tag == "{随机链接}" {
                                let uri_rule = site_config.get_random_uri_rule(&mut rng);
                                let url = replace_with_resource(&uri_rule, &mut rng, region_resource);
                                result.push_str(&url);
                            } else if tag == "{随机标题}" {
                                // 🔧 统一使用地区资源
                                result.push_str(&data.get_title(&mut rng, region_resource));
                            } else if tag == "{随机句子}" {
                                // 🔧 统一使用地区资源生成随机句子
                                result.push_str(&region_resource.get_random_data(&mut rng, "juzi"));
                            } else if tag == "{随机字母}" {
                                result.push_str(&random_letters(&mut rng, 6));
                            } else if tag == "{随机图片}" {
                                // 🔧 统一使用地区资源生成随机图片
                                result.push_str(&region_resource.get_random_data(&mut rng, "pic"));
                            } else if tag == "{随机图标}" {
                                // 🔧 统一使用地区资源生成随机图标
                                result.push_str(&region_resource.get_random_data(&mut rng, "icon"));
                            } else if tag == "{随机作者}" {
                                // 🔧 统一使用地区资源生成随机作者
                                result.push_str(&region_resource.get_random_data(&mut rng, "author"));
                            } else if tag == "{随机视频}" {
                                // 🔧 统一使用地区资源生成随机视频
                                result.push_str(&region_resource.get_random_data(&mut rng, "video"));
                            } else if tag == "{栏目名}" {
                                // 🔧 统一使用地区资源生成随机栏目名
                                result.push_str(&region_resource.get_random_data(&mut rng, "column"));
                            } else if tag == "{来源}" {
                                // 🔧 统一使用地区资源生成随机来源
                                result.push_str(&region_resource.get_random_data(&mut rng, "source"));
                            } else if tag == "{城市}" {
                                // 🔧 统一使用地区资源生成随机城市
                                result.push_str(&region_resource.get_random_data(&mut rng, "city"));
                            } else {
                                // 未处理的标签，保留原样
                                result.push_str(tag);
                            }
                        }
                    },
                    TagType::Parameterized(param) => {
                        // 处理带参数的标签
                        if tag.starts_with("{随机数字") {
                            // 检查是否是范围格式，如"100-1000"
                            if param.contains('-') {
                                if let Some((min, max)) = param.split_once('-') {
                                    let min_val = min.trim().parse::<i32>().unwrap_or(1);
                                    let max_val = max.trim().parse::<i32>().unwrap_or(100);
                                    let num = rng.gen_range(min_val..=max_val);
                                    result.push_str(&num.to_string());
                                } else {
                                    // 如果参数格式错误，使用默认随机数
                                    let num = rng.gen_range(1..100);
                                    result.push_str(&num.to_string());
                                }
                            } else {
                                // 解析参数作为长度，默认为6
                                let length = param.parse::<usize>().unwrap_or(6);
                                result.push_str(&random_number(&mut rng, length));
                            }
                        } else if tag.starts_with("{随机字母") {
                            // 解析参数作为长度，默认为6
                            let length = param.parse::<usize>().unwrap_or(6);
                            result.push_str(&random_letters(&mut rng, length));
                        } else {
                            // 未知参数化标签，保留原样
                            result.push_str(tag);
                        }
                    },
                    TagType::Static => {
                        // 静态标签，从预定义映射中获取值
                        if let Some(value) = tag_replace.get(tag) {
                            result.push_str(value);

                        } else {
                            // 如果没有映射，保留原标签
                            result.push_str(tag);
                        }
                    }
                }
            },
            TemplateFragment::JsonBlock { prefix, content, suffix } => {
                // 处理JSON块
                result.push_str(prefix);
                
                // 递归处理JSON块内容
                let mut json_result = String::new();
                for fragment in content {
                    match fragment {
                        TemplateFragment::Static(text) => {
                            json_result.push_str(text);
                        },
                        TemplateFragment::Tag(tag, tag_type) => {
                            // 根据标签类型处理JSON块中的标签
                            match tag_type {
                                TagType::Fixed => {
                                    // JSON 块中的固定标签处理
                                    if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                                        json_result.push_str(cached_value);
                                    } else {
                                        let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                            Some(region_resource.get_random_data(&mut rng, "pic"))
                                        } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                            Some(region_resource.get_random_data(&mut rng, "icon"))
                                        } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                            Some(region_resource.get_random_data(&mut rng, "author"))
                                        } else {
                                            None
                                        };

                                        if let Some(value) = generated_value {
                                            lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                            json_result.push_str(&value);
                                        } else {
                                            json_result.push_str(tag);
                                        }
                                    }
                                },
                                TagType::Parameterized(param) => {
                                    // 处理参数化标签
                                    if tag.starts_with("{随机数字") {
                                        // 检查是否是范围格式
                                        if param.contains('-') {
                                            if let Some((min, max)) = param.split_once('-') {
                                                let min_val = min.trim().parse::<i32>().unwrap_or(1);
                                                let max_val = max.trim().parse::<i32>().unwrap_or(100);
                                                let num = rng.gen_range(min_val..=max_val);
                                                json_result.push_str(&num.to_string());
                                            } else {
                                                let num = rng.gen_range(1..100);
                                                json_result.push_str(&num.to_string());
                                            }
                                        } else {
                                            let length = param.parse::<usize>().unwrap_or(6);
                                            json_result.push_str(&random_number(&mut rng, length));
                                        }
                                    } else if tag.starts_with("{随机字母") {
                                        let length = param.parse::<usize>().unwrap_or(6);
                                        json_result.push_str(&random_letters(&mut rng, length));
                                    } else {
                                        // 其他参数化标签
                                        json_result.push_str(tag);
                                    }
                                },
                                TagType::Dynamic => {
                                    // 处理动态标签
                                    if let Some(&generator) = TAG_REPLACE_ONE.get(tag) {
                                        let random_value = generator(&mut rng);
                                        json_result.push_str(&random_value);
                                    } else if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                        // 🔧 修复：JSON块中的固定图片标签
                                        if let Some(num_str) = tag.strip_prefix("{固定图片").and_then(|s| s.strip_suffix("}")) {
                                            if let Ok(num) = num_str.parse::<usize>() {
                                                if num >= 1 && num <= 100 {
                                                    json_result.push_str(&region_resource.get_random_data(&mut rng, "pic"));
                                                } else {
                                                    json_result.push_str(tag);
                                                }
                                            } else {
                                                json_result.push_str(tag);
                                            }
                                        } else {
                                            json_result.push_str(tag);
                                        }
                                    } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                        // 🔧 修复：JSON块中的固定图标标签
                                        if let Some(num_str) = tag.strip_prefix("{固定图标").and_then(|s| s.strip_suffix("}")) {
                                            if let Ok(num) = num_str.parse::<usize>() {
                                                if num >= 1 && num <= 100 {
                                                    json_result.push_str(&region_resource.get_random_data(&mut rng, "icon"));
                                                } else {
                                                    json_result.push_str(tag);
                                                }
                                            } else {
                                                json_result.push_str(tag);
                                            }
                                        } else {
                                            json_result.push_str(tag);
                                        }
                                    } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                        // 🔧 修复：JSON块中的固定作者标签
                                        if let Some(num_str) = tag.strip_prefix("{固定作者").and_then(|s| s.strip_suffix("}")) {
                                            if let Ok(num) = num_str.parse::<usize>() {
                                                if num >= 1 && num <= 100 {
                                                    json_result.push_str(&region_resource.get_random_data(&mut rng, "author"));
                                                } else {
                                                    json_result.push_str(tag);
                                                }
                                            } else {
                                                json_result.push_str(tag);
                                            }
                                        } else {
                                            json_result.push_str(tag);
                                        }
                                    } else {
                                        // 未处理的动态标签，保留原样
                                        json_result.push_str(tag);
                                    }
                                },
                                TagType::JsonSpecial => {
                                    // 处理JSON特殊标签
                                    if let Some(value) = tag_replace.get(tag) {
                                        json_result.push_str(value);
                                    } else {
                                        json_result.push_str(tag);
                                    }
                                },
                                _ => {
                                    // 从映射中获取值或保留原标签
                                    if let Some(value) = tag_replace.get(tag) {
                                        json_result.push_str(value);
                                    } else {
                                        json_result.push_str(tag);
                                    }
                                }
                            }
                        },
                        _ => {} // 忽略嵌套的JSON块
                    }
                }
                
                result.push_str(&json_result);
                result.push_str(suffix);
            }
        }
    }

    // 处理占位符替换
    if result.contains("PLACEHOLDER_RANDOM_LINK") {
        let uri_rule = site_config.get_random_uri_rule(&mut rng);
        let url = replace_with_resource(&uri_rule, &mut rng, region_resource);
        result = result.replace("PLACEHOLDER_RANDOM_LINK", &url);
    }

    if result.contains("PLACEHOLDER_EXTERNAL_LINK") {
        // 随机选择一个已通过的网站，获取其URL链接规则来配置
        let external_url = domains.get_random_domain(&mut rng).await;
        result = result.replace("PLACEHOLDER_EXTERNAL_LINK", &external_url);
    }

    result
}






// 修改replace函数，直接使用随机模板
pub async fn replace(
    template: &str,
    request_info: &RequestInfo,
    site_config: &SiteConfig,
    domains: &SiteConfigs,
    keyword: &str,
) -> String {
    // 🔧 统一使用地区资源管理器，移除回退机制
    let region_resource = crate::RESOURCE_MANAGER.get_resource(&site_config.region_code);
    let default_timezone_context = crate::util::region_timezone_context::RegionTimezoneContext::new("UTC".to_string());

    // 直接使用地区模板处理系统
    replace_with_region_resource(template, request_info, site_config, domains, keyword, region_resource, &default_timezone_context).await
}



pub async fn replace_cache(template: &str, domains: &SiteConfigs, keyword: &str) -> String {
    // 🔧 统一使用地区资源处理系统，移除全局资源依赖
    // 使用默认地区资源
    let region_resource = crate::RESOURCE_MANAGER.get_resource("default");

    // 直接调用地区缓存处理函数
    replace_cache_with_region(template, domains, keyword, region_resource).await
}

// 🔧 统一使用地区资源的标签替换映射创建函数
fn create_tag_replace_map(
    now: &DateTime<Tz>,
    request_info: &RequestInfo,
    data: &mut Data,
    keyword: &str,
    rng: &mut ThreadRng,
    region_resource: &Resource,  // 🔧 移除Option，强制使用地区资源
) -> HashMap<String, String> {
    let mut tag_replace = HashMap::new();

    // 🔧 直接使用地区资源，移除回退机制
    let resource_ref = region_resource;

    // 生成大关键词
    let big_keyword = resource_ref.get_random_data(rng, "bigkeyword");

    // 基于大关键词生成相关的大标题和大描述
    let big_title = format!("{} - {}", big_keyword, resource_ref.get_random_data(rng, "title"));

    // 使用关键词相关的句子作为描述，不添加固定模板
    let base_description = resource_ref.get_random_data(rng, "juzi");
    let big_description = format!("{}。{}", big_keyword, base_description);
    
    // 插入到替换映射中
    tag_replace.insert("{大关键词}".to_string(), big_keyword);
    tag_replace.insert("{大标题}".to_string(), big_title);
    tag_replace.insert("{大描述}".to_string(), big_description);
    
    tag_replace.insert("{时间}".to_string(), now.format("%Y-%m-%d").to_string());
    tag_replace.insert(
        "{时间1}".to_string(),
        now.format("%Y-%m-%d %H:%M").to_string(),
    );
    tag_replace.insert("{AMP时间}".to_string(), now.to_rfc3339());
    tag_replace.insert("{副标题}".to_string(), data.get_title(rng, resource_ref));
    tag_replace.insert("{当前链接}".to_string(), request_info.get_current_url());
    tag_replace.insert("{当前域名}".to_string(), request_info.host.to_string());
    tag_replace.insert("{域名}".to_string(), request_info.host.to_string());
    tag_replace.insert("{协议}".to_string(), request_info.protocol.to_string());

    // 🔧 添加固定标签（页面内保持一致）
    tag_replace.insert("{固定城市}".to_string(), resource_ref.get_random_data(rng, "city"));
    tag_replace.insert("{固定作者}".to_string(), resource_ref.get_random_data(rng, "author"));
    tag_replace.insert("{固定图片}".to_string(), resource_ref.get_random_data(rng, "pic"));
    tag_replace.insert("{固定图标}".to_string(), resource_ref.get_random_data(rng, "icon"));

    // 获取关键词
    let current_keyword = if keyword.is_empty() {
        resource_ref.get_random_data(rng, "keyword")
    } else {
        keyword.to_string()
    };

    // 添加链接相关标签 - 注意：这里无法获取site_config，所以先设置占位符
    // 实际的{随机链接}处理在动态标签处理中进行
    tag_replace.insert("{随机链接}".to_string(), "PLACEHOLDER_RANDOM_LINK".to_string());

    // 添加外链标签 - 注意：这里无法获取domains，所以先设置占位符
    // 实际的{外链}处理在动态标签处理中进行
    tag_replace.insert("{外链}".to_string(), "PLACEHOLDER_EXTERNAL_LINK".to_string());

    // 所有随机标签都改为动态标签，每次调用都重新生成
    // 不再在这里添加固定的随机标签，它们都在模板处理时动态生成

    // 添加时间相关标签
    let naive_date = now.naive_local().date();
    let random_days = rng.gen_range(0..61);
    let date = naive_date - chrono::Duration::days(random_days);
    tag_replace.insert("{随机日期}".to_string(), date.format("%Y-%m-%d").to_string());

    let random_hour: usize = rng.gen_range(0..24);
    tag_replace.insert("{随机小时}".to_string(), format!("{:02}", random_hour));

    let random_minute: usize = rng.gen_range(0..60);
    tag_replace.insert("{随机分钟}".to_string(), format!("{:02}", random_minute));

    // 🔧 统一使用地区资源生成标题和描述
    if !keyword.is_empty() {
        let mut data_with_keyword = Data::new_with_resource(&current_keyword, resource_ref);
        tag_replace.insert("{标题}".to_string(), data_with_keyword.get_title(rng, resource_ref));
        tag_replace.insert("{描述}".to_string(), data_with_keyword.get_juzi(rng, resource_ref));
    } else {
        // 如果没有关键词，使用原有的data对象
        tag_replace.insert("{标题}".to_string(), data.get_title(rng, resource_ref));
        tag_replace.insert("{描述}".to_string(), data.get_juzi(rng, resource_ref));
    }



    // 🚀 性能优化：只预生成基础标签，编号标签改为懒加载
    // 只生成 {关键词1} 作为基础
    tag_replace.insert("{关键词1}".to_string(), current_keyword.clone());

    // 预生成少量常用的关键词（1-10）
    for i in 2..=10 {
        tag_replace.insert(
            format!("{{关键词{}}}", i),
            resource_ref.get_random_data(rng, "keyword"),
        );
    }

    // 预生成少量大关键词（1-10）
    for i in 1..=10 {
        tag_replace.insert(
            format!("{{大关键词{}}}", i),
            resource_ref.get_random_data(rng, "bigkeyword"),
        );
    }

    // 🔧 修复：懒加载预生成固定编号标签（根据模板中实际使用的标签）
    // 这里可以根据需要预生成常用的固定标签，或者在处理时按需生成

    tag_replace
}

// 专门为地区资源创建标签映射
fn create_tag_replace_map_for_region(
    now: &DateTime<Tz>,
    request_info: &RequestInfo,
    data: &mut Data,
    keyword: &str,
    rng: &mut ThreadRng,
    region_resource: &Resource,
) -> HashMap<String, String> {
    let mut tag_replace = HashMap::new();

    // 生成大关键词
    let big_keyword = region_resource.get_random_data(rng, "bigkeyword");

    // 基于大关键词生成相关的大标题和大描述
    let big_title = format!("{} - {}", big_keyword, region_resource.get_random_data(rng, "title"));

    // 使用关键词相关的句子作为描述
    let base_description = region_resource.get_random_data(rng, "juzi");
    let big_description = format!("{}。{}", big_keyword, base_description);

    // 插入到替换映射中
    tag_replace.insert("{大关键词}".to_string(), big_keyword);
    tag_replace.insert("{大标题}".to_string(), big_title);
    tag_replace.insert("{大描述}".to_string(), big_description);

    tag_replace.insert("{时间}".to_string(), now.format("%Y-%m-%d").to_string());
    tag_replace.insert(
        "{时间1}".to_string(),
        now.format("%Y-%m-%d %H:%M").to_string(),
    );
    tag_replace.insert("{AMP时间}".to_string(), now.to_rfc3339());
    tag_replace.insert("{副标题}".to_string(), data.get_title(rng, region_resource));
    tag_replace.insert("{当前链接}".to_string(), request_info.get_current_url());
    tag_replace.insert("{当前域名}".to_string(), request_info.host.to_string());
    tag_replace.insert("{域名}".to_string(), request_info.host.to_string());
    tag_replace.insert("{协议}".to_string(), request_info.protocol.to_string());

    // 🔧 添加固定标签（页面内保持一致，使用地区资源）
    tag_replace.insert("{固定城市}".to_string(), region_resource.get_random_data(rng, "city"));
    tag_replace.insert("{固定作者}".to_string(), region_resource.get_random_data(rng, "author"));
    tag_replace.insert("{固定图片}".to_string(), region_resource.get_random_data(rng, "pic"));
    tag_replace.insert("{固定图标}".to_string(), region_resource.get_random_data(rng, "icon"));

    // 获取关键词
    let current_keyword = if keyword.is_empty() {
        region_resource.get_random_data(rng, "keyword")
    } else {
        keyword.to_string()
    };

    // 添加链接相关标签 - 注意：这里无法获取site_config，所以先设置占位符
    // 实际的{随机链接}处理在动态标签处理中进行
    tag_replace.insert("{随机链接}".to_string(), "PLACEHOLDER_RANDOM_LINK".to_string());

    // 添加外链标签 - 注意：这里无法获取domains，所以先设置占位符
    // 实际的{外链}处理在动态标签处理中进行
    tag_replace.insert("{外链}".to_string(), "PLACEHOLDER_EXTERNAL_LINK".to_string());

    // 所有随机标签都改为动态标签，每次调用都重新生成
    // 不再在这里添加固定的随机标签，它们都在模板处理时动态生成

    // 如果有关键词，使用关键词生成标题和描述
    if !keyword.is_empty() {
        let mut data_with_keyword = Data::new_with_keyword_search(&current_keyword, region_resource);
        tag_replace.insert("{标题}".to_string(), data_with_keyword.get_title(rng, region_resource));
        tag_replace.insert("{描述}".to_string(), data_with_keyword.get_juzi(rng, region_resource));
    } else {
        // 如果没有关键词，使用原有的data对象
        tag_replace.insert("{标题}".to_string(), data.get_title(rng, region_resource));
        tag_replace.insert("{描述}".to_string(), data.get_juzi(rng, region_resource));
    }

    // 🚀 性能优化：只预生成基础标签，编号标签改为懒加载
    // 只生成 {关键词1} 作为基础
    tag_replace.insert("{关键词1}".to_string(), current_keyword.clone());

    // 预生成少量常用的关键词（1-10）
    for i in 2..=10 {
        tag_replace.insert(
            format!("{{关键词{}}}", i),
            region_resource.get_random_data(rng, "keyword"),
        );
    }

    // 预生成少量大关键词（1-10）
    for i in 1..=10 {
        tag_replace.insert(
            format!("{{大关键词{}}}", i),
            region_resource.get_random_data(rng, "bigkeyword"),
        );
    }

    // 注意：{标题2-100} {描述2-100} 现在通过动态生成
    // 在模板处理时按需生成，不在这里预生成

    // 🔧 修复：懒加载预生成固定编号标签（根据模板中实际使用的标签）
    // 这里可以根据需要预生成常用的固定标签，或者在处理时按需生成

    tag_replace
}





// 删除了未使用的随机函数，所有标签现在都在标签映射中处理
fn random_number(rng: &mut ThreadRng, n: usize) -> String {
    let rest_digits: String = iter::repeat(())
        .map(|()| rng.gen_range(0..10).to_string())
        .take(n)
        .collect();
    rest_digits
}





fn random_letters(rng: &mut ThreadRng, n: usize) -> String {
    let letters: String = iter::repeat(())
        .map(|()| {
            let chars = b"abcdefghijklmnopqrstuvwxyz";
            chars[rng.gen_range(0..chars.len())] as char
        })
        .take(n)
        .collect();
    letters
}

// 所有随机函数已移除，功能已整合到标签映射中
// fn flood_domain(rng: &mut ThreadRng, request_info: &RequestInfo) -> String {
//     format!(
//         "{}://{}.{}{}",
//         request_info.protocol,
//         random_letters(rng, 1),
//         request_info.host,
//         request_info.port
//     )
// }

/// 使用地区资源替换模板内容
pub async fn replace_with_region_resource(
    template: &str,
    request_info: &RequestInfo,
    site_config: &SiteConfig,
    domains: &SiteConfigs,
    keyword: &str,
    region_resource: &Resource,
    region_timezone_context: &crate::util::region_timezone_context::RegionTimezoneContext,
) -> String {
    let mut rng = thread_rng();

    // 初始化数据对象（使用地区资源）
    let mut data = if keyword.is_empty() {
        let random_keyword = region_resource.get_random_data(&mut rng, "keyword");
        Data::new_with_resource(&random_keyword, region_resource)
    } else {
        Data::new_with_resource(keyword, region_resource)
    };

    // 设置请求相关数据
    data.set_request_data(request_info, site_config);

    // 创建完整的标签替换映射（使用地区资源和地区时区）
    let region_timezone_str = region_timezone_context.get_region_timezone(&site_config.region_code).await;
    let now = crate::util::time::now_for_region(&region_timezone_str);
    let tag_replace = create_tag_replace_map_for_region(&now, request_info, &mut data, keyword, &mut rng, region_resource);

    // 使用预处理的模板结构进行快速替换
    let processed = preprocess_template(template);

    // 🔧 添加懒加载固定标签缓存
    let mut lazy_fixed_tags: HashMap<String, String> = HashMap::new();

    let mut result = String::with_capacity(processed.original.len());

    // 处理所有片段
    for fragment in &processed.fragments {
        match fragment {
            TemplateFragment::Static(text) => {
                result.push_str(text);
            },
            TemplateFragment::Tag(tag, tag_type) => {
                match tag_type {
                    TagType::Parameterized(param) => {
                        if tag.starts_with("{随机数字") {
                            if tag.contains('-') {
                                // 处理随机数字的范围格式
                                if let Some((min, max)) = param.split_once('-') {
                                    let min_val = min.trim().parse::<i32>().unwrap_or(1);
                                    let max_val = max.trim().parse::<i32>().unwrap_or(100);
                                    let num = rng.gen_range(min_val..=max_val);
                                    result.push_str(&num.to_string());
                                } else {
                                    let num = rng.gen_range(1..100);
                                    result.push_str(&num.to_string());
                                }
                            } else {
                                let length = param.parse::<usize>().unwrap_or(3);
                                result.push_str(&random_number(&mut rng, length));
                            }
                        } else if tag.starts_with("{随机字母") {
                            let length = param.parse::<usize>().unwrap_or(5);
                            result.push_str(&random_letters(&mut rng, length));
                        } else {
                            result.push_str(tag);
                        }
                    },
                    TagType::Fixed => {
                        // 🔧 添加固定标签处理（与 replace_with_processed 相同的逻辑）
                        if let Some(value) = tag_replace.get(tag) {
                            result.push_str(value);
                        } else if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                            result.push_str(cached_value);
                        } else {
                            let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "pic"))
                            } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "icon"))
                            } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                Some(region_resource.get_random_data(&mut rng, "author"))
                            } else {
                                None
                            };

                            if let Some(value) = generated_value {
                                lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                result.push_str(&value);
                            } else {
                                result.push_str(tag);
                            }
                        }
                    },
                    TagType::Dynamic => {
                        if let Some(&generator) = TAG_REPLACE_ONE.get(tag) {
                            let random_value = generator(&mut rng);
                            result.push_str(&random_value);
                        } else if tag == "{随机链接}" {
                            // 使用网站配置的URL规则生成随机链接，使用地区资源
                            let uri_rule = site_config.get_random_uri_rule(&mut rng);
                            let url = replace_with_resource(&uri_rule, &mut rng, region_resource);
                            result.push_str(&url);
                        } else if tag == "{随机关键词}" {
                            // 每次调用都重新生成随机关键词
                            result.push_str(&region_resource.get_random_data(&mut rng, "keyword"));
                        } else if tag == "{随机句子}" {
                            // 每次调用都重新生成随机句子
                            result.push_str(&region_resource.get_random_data(&mut rng, "juzi"));
                        } else if tag == "{随机图片}" {
                            // 每次调用都重新生成随机图片，使用地区资源
                            result.push_str(&region_resource.get_random_data(&mut rng, "pic"));
                        } else if tag == "{随机图标}" {
                            // 每次调用都重新生成随机图标，使用地区资源
                            result.push_str(&region_resource.get_random_data(&mut rng, "icon"));
                        } else if tag == "{随机字母}" {
                            result.push_str(&random_letters(&mut rng, 6));
                        } else if tag == "{随机作者}" {
                            result.push_str(&region_resource.get_random_data(&mut rng, "author"));
                        } else if tag == "{随机视频}" {
                            result.push_str(&region_resource.get_random_data(&mut rng, "video"));

                        } else if tag == "{栏目名}" {
                            result.push_str(&region_resource.get_random_data(&mut rng, "column"));
                        } else if tag == "{来源}" {
                            result.push_str(&region_resource.get_random_data(&mut rng, "source"));
                        } else if tag == "{城市}" {
                            result.push_str(&region_resource.get_random_data(&mut rng, "city"));
                        } else if tag == "{外链}" {
                            // 随机选择一个已通过的网站，获取其URL链接规则来配置
                            let external_url = domains.get_random_domain(&mut rng).await;
                            result.push_str(&external_url);
                        } else if tag.starts_with("{关键词") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的编号关键词标签
                            if let Some(num_str) = tag.strip_prefix("{关键词").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 11 && num <= 2000 {  // 🔧 修复：只处理11-2000，1-10已预生成
                                        result.push_str(&region_resource.get_random_data(&mut rng, "keyword"));
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{标题") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的编号标题标签
                            if let Some(num_str) = tag.strip_prefix("{标题").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 2 && num <= 2000 {
                                        // 获取对应的关键词
                                        let keyword_for_title = if num <= 10 {
                                            let keyword_key = format!("{{关键词{}}}", num);
                                            tag_replace.get(&keyword_key).cloned()
                                                .unwrap_or_else(|| region_resource.get_random_data(&mut rng, "keyword"))
                                        } else {
                                            region_resource.get_random_data(&mut rng, "keyword")
                                        };

                                        let title_content = format!("{} - {}", keyword_for_title, region_resource.get_random_data(&mut rng, "title"));
                                        result.push_str(&title_content);
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{描述") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的编号描述标签
                            if let Some(num_str) = tag.strip_prefix("{描述").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 2 && num <= 2000 {
                                        // 获取对应的关键词
                                        let keyword_for_desc = if num <= 10 {
                                            let keyword_key = format!("{{关键词{}}}", num);
                                            tag_replace.get(&keyword_key).cloned()
                                                .unwrap_or_else(|| region_resource.get_random_data(&mut rng, "keyword"))
                                        } else {
                                            region_resource.get_random_data(&mut rng, "keyword")
                                        };

                                        let desc_content = format!("{}。{}", keyword_for_desc, region_resource.get_random_data(&mut rng, "juzi"));
                                        result.push_str(&desc_content);
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{固定图片") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的固定图片标签
                            if let Some(num_str) = tag.strip_prefix("{固定图片").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 1 && num <= 100 {
                                        result.push_str(&region_resource.get_random_data(&mut rng, "pic"));
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的固定图标标签
                            if let Some(num_str) = tag.strip_prefix("{固定图标").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 1 && num <= 100 {
                                        result.push_str(&region_resource.get_random_data(&mut rng, "icon"));
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                            // 🔧 修复：地区模板中的固定作者标签
                            if let Some(num_str) = tag.strip_prefix("{固定作者").and_then(|s| s.strip_suffix("}")) {
                                if let Ok(num) = num_str.parse::<usize>() {
                                    if num >= 1 && num <= 100 {
                                        result.push_str(&region_resource.get_random_data(&mut rng, "author"));
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                result.push_str(tag);
                            }
                        } else {
                            result.push_str(tag);
                        }
                    },
                    _ => {
                        if let Some(value) = tag_replace.get(tag) {
                            if value == "PLACEHOLDER_RANDOM_LINK" {
                                // 处理{随机链接}占位符，使用地区资源
                                let uri_rule = site_config.get_random_uri_rule(&mut rng);
                                let url = replace_with_resource(&uri_rule, &mut rng, region_resource);
                                result.push_str(&url);
                            } else if value == "PLACEHOLDER_EXTERNAL_LINK" {
                                // 处理{外链}占位符 - 这里无法访问domains，暂时使用简单实现
                                // 注意：这里应该使用domains.get_random_domain()，但在replace_cache中无法访问
                                let external_url = format!("https://example{}.com", random_number(&mut rng, 2));
                                result.push_str(&external_url);
                            } else {
                                result.push_str(value);
                            }
                        } else {
                            result.push_str(tag);
                        }
                    }
                }
            },
            TemplateFragment::JsonBlock { prefix, content, suffix } => {
                result.push_str(prefix);

                let mut json_result = String::new();
                for fragment in content {
                    match fragment {
                        TemplateFragment::Static(text) => {
                            json_result.push_str(text);
                        },
                        TemplateFragment::Tag(tag, tag_type) => {
                            if let Some(value) = tag_replace.get(tag) {
                                json_result.push_str(value);
                            } else {
                                // 处理懒加载的固定标签
                                match tag_type {
                                    TagType::Fixed => {
                                        if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                                            json_result.push_str(cached_value);
                                        } else {
                                            let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(&mut rng, "pic"))
                                            } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(&mut rng, "icon"))
                                            } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(&mut rng, "author"))
                                            } else {
                                                None
                                            };

                                            if let Some(value) = generated_value {
                                                lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                                json_result.push_str(&value);
                                            } else {
                                                json_result.push_str(tag);
                                            }
                                        }
                                    },
                                    _ => {
                                        json_result.push_str(tag);
                                    }
                                }
                            }
                        },
                        _ => {}
                    }
                }

                result.push_str(&json_result);
                result.push_str(suffix);
            }
        }
    }

    result
}

/// 使用地区资源替换数据标签


// 添加缺失的模板处理函数
pub fn replace_number_tags(content: &str, rng: &mut ThreadRng) -> String {
    use regex::Regex;
    use lazy_static::lazy_static;
    use rand::Rng;

    lazy_static! {
        // 支持 {随机数字5} 格式（指定位数）
        static ref NUMBER_TAG_REGEX: Regex = Regex::new(r"\{随机数字(\d+)\}").unwrap();
        // 支持 {随机数字100-1000} 格式（指定范围）
        static ref RANGE_NUMBER_TAG_REGEX: Regex = Regex::new(r"\{随机数字(\d+)-(\d+)\}").unwrap();
    }

    let mut result = content.to_string();

    // 处理范围格式的数字标签
    for cap in RANGE_NUMBER_TAG_REGEX.captures_iter(content) {
        if let (Some(min_str), Some(max_str)) = (cap.get(1), cap.get(2)) {
            if let (Ok(min), Ok(max)) = (min_str.as_str().parse::<i32>(), max_str.as_str().parse::<i32>()) {
                if min < max {
                    let tag = &cap[0];
                    let replacement = rng.gen_range(min..=max).to_string();
                    result = result.replace(tag, &replacement);
                }
            }
        }
    }

    // 处理位数格式的数字标签
    for cap in NUMBER_TAG_REGEX.captures_iter(content) {
        if let Some(num_str) = cap.get(1) {
            if let Ok(num) = num_str.as_str().parse::<usize>() {
                let tag = &cap[0];
                let replacement = crate::util::random::random_numbers(rng, num);
                result = result.replace(tag, &replacement);
            }
        }
    }

    result
}

pub fn replace_letter_tags(content: &str, rng: &mut ThreadRng) -> String {
    use regex::Regex;
    use lazy_static::lazy_static;

    lazy_static! {
        static ref LETTER_TAG_REGEX: Regex = Regex::new(r"\{随机字母(\d+)\}").unwrap();
    }

    let mut result = content.to_string();

    for cap in LETTER_TAG_REGEX.captures_iter(content) {
        if let Some(num_str) = cap.get(1) {
            if let Ok(num) = num_str.as_str().parse::<usize>() {
                let tag = &cap[0];
                let replacement = crate::util::random::random_letters(rng, num);
                result = result.replace(tag, &replacement);
            }
        }
    }

    result
}

pub fn replace_time_tags(content: &str, rng: &mut ThreadRng) -> String {
    use regex::Regex;
    use lazy_static::lazy_static;

    lazy_static! {
        static ref TIME_TAG_REGEX: Regex = Regex::new(r"\{随机时间(\d+)\}").unwrap();
    }

    let mut result = content.to_string();

    for cap in TIME_TAG_REGEX.captures_iter(content) {
        if let Some(num_str) = cap.get(1) {
            if let Ok(num) = num_str.as_str().parse::<usize>() {
                let tag = &cap[0];
                let replacement = crate::util::random::random_time(rng, num);
                result = result.replace(tag, &replacement);
            }
        }
    }

    result
}

/// 替换域名和链接标签
pub async fn replace_domain_tags(
    content: &str,
    request_info: &RequestInfo,
    _site_config: &SiteConfig,
    _domains: &SiteConfigs,
) -> String {
    let mut result = content.to_string();

    // 替换域名标签
    if result.contains("{域名}") {
        result = result.replace("{域名}", &request_info.host);
    }

    // 替换协议标签
    if result.contains("{协议}") {
        result = result.replace("{协议}", &request_info.protocol);
    }

    // 替换当前链接标签
    if result.contains("{当前链接}") {
        result = result.replace("{当前链接}", &request_info.get_current_url());
    }

    // 这里可以添加更多的域名相关标签替换逻辑

    result
}

// 支持地区资源的缓存替换函数
pub async fn replace_cache_with_region(template: &str, _domains: &SiteConfigs, keyword: &str, region_resource: &Resource) -> String {
    let _start_time = std::time::Instant::now();
    let rng = &mut rand::thread_rng();

    // 尝试创建一个临时的ProcessedTemplate
    let processed = preprocess_template(template);

    // 创建完整的标签替换映射，使用地区资源
    let mut data = if keyword.is_empty() {
        let random_keyword = region_resource.get_random_data(rng, "keyword");
        Data::new_with_resource(&random_keyword, region_resource)
    } else {
        Data::new_with_resource(keyword, region_resource)
    };

    // 使用统一的标签映射创建函数（使用UTC作为默认时区）
    let now = crate::util::time::now_for_region("UTC");
    let tag_replace = create_tag_replace_map(&now, &RequestInfo::default(), &mut data, keyword, rng, region_resource);

    // 🔧 添加懒加载固定标签缓存
    let mut lazy_fixed_tags: HashMap<String, String> = HashMap::new();

    // 使用预处理的片段快速构建结果
    let mut result = String::with_capacity(processed.original.len());

    // 处理所有片段
    for fragment in &processed.fragments {
        match fragment {
            TemplateFragment::Static(text) => {
                // 静态文本直接添加
                result.push_str(text);
            },
            TemplateFragment::Tag(tag, tag_type) => {
                // 从映射中获取值或处理特殊标签
                if let Some(value) = tag_replace.get(tag) {
                    result.push_str(value);
                } else {
                    // 处理参数化标签和动态标签
                    match tag_type {
                        TagType::Fixed => {
                            // 固定标签懒加载处理
                            if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                                result.push_str(cached_value);
                            } else {
                                let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                    Some(region_resource.get_random_data(rng, "pic"))
                                } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                    Some(region_resource.get_random_data(rng, "icon"))
                                } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                    Some(region_resource.get_random_data(rng, "author"))
                                } else {
                                    None
                                };

                                if let Some(value) = generated_value {
                                    lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                    result.push_str(&value);
                                } else {
                                    result.push_str(tag);
                                }
                            }
                        },
                    TagType::Parameterized(param) => {
                        if tag.starts_with("{随机数字") {
                            // 检查是否是范围格式
                            if param.contains('-') {
                                if let Some((min, max)) = param.split_once('-') {
                                    let min_val = min.trim().parse::<i32>().unwrap_or(1);
                                    let max_val = max.trim().parse::<i32>().unwrap_or(100);
                                    let num = rng.gen_range(min_val..=max_val);
                                    result.push_str(&num.to_string());
                                } else {
                                    let num = rng.gen_range(1..100);
                                    result.push_str(&num.to_string());
                                }
                            } else {
                                let length = param.parse::<usize>().unwrap_or(6);
                                result.push_str(&random_number(rng, length));
                            }
                        } else if tag.starts_with("{随机字母") {
                            let length = param.parse::<usize>().unwrap_or(6);
                            result.push_str(&random_letters(rng, length));
                        } else {
                            result.push_str(tag);
                        }
                    },
                        TagType::Dynamic => {
                            if tag == "{随机关键词}" {
                                result.push_str(&region_resource.get_random_data(rng, "keyword"));
                            } else if tag.starts_with("{关键词") && tag.ends_with("}") {
                                // 编号关键词标签处理
                                if let Some(num_str) = tag.strip_prefix("{关键词").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 11 && num <= 2000 {
                                            result.push_str(&region_resource.get_random_data(rng, "keyword"));
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else if tag.starts_with("{标题") && tag.ends_with("}") {
                                // 编号标题标签处理
                                if let Some(num_str) = tag.strip_prefix("{标题").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 2 && num <= 2000 {
                                            let keyword_for_title = if num <= 10 {
                                                let keyword_key = format!("{{关键词{}}}", num);
                                                tag_replace.get(&keyword_key).cloned()
                                                    .unwrap_or_else(|| region_resource.get_random_data(rng, "keyword"))
                                            } else {
                                                region_resource.get_random_data(rng, "keyword")
                                            };
                                            let title_content = format!("{} - {}", keyword_for_title, region_resource.get_random_data(rng, "title"));
                                            result.push_str(&title_content);
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else if tag.starts_with("{描述") && tag.ends_with("}") {
                                // 编号描述标签处理
                                if let Some(num_str) = tag.strip_prefix("{描述").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 2 && num <= 2000 {
                                            let keyword_for_desc = if num <= 10 {
                                                let keyword_key = format!("{{关键词{}}}", num);
                                                tag_replace.get(&keyword_key).cloned()
                                                    .unwrap_or_else(|| region_resource.get_random_data(rng, "keyword"))
                                            } else {
                                                region_resource.get_random_data(rng, "keyword")
                                            };
                                            let desc_content = format!("{}。{}", keyword_for_desc, region_resource.get_random_data(rng, "juzi"));
                                            result.push_str(&desc_content);
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                // 固定图片标签处理（缓存函数中动态处理）
                                if let Some(num_str) = tag.strip_prefix("{固定图片").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 1 && num <= 100 {
                                            result.push_str(&region_resource.get_random_data(rng, "pic"));
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                // 固定图标标签处理（缓存函数中动态处理）
                                if let Some(num_str) = tag.strip_prefix("{固定图标").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 1 && num <= 100 {
                                            result.push_str(&region_resource.get_random_data(rng, "icon"));
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                // 固定作者标签处理（缓存函数中动态处理）
                                if let Some(num_str) = tag.strip_prefix("{固定作者").and_then(|s| s.strip_suffix("}")) {
                                    if let Ok(num) = num_str.parse::<usize>() {
                                        if num >= 1 && num <= 100 {
                                            result.push_str(&region_resource.get_random_data(rng, "author"));
                                        } else {
                                            result.push_str(tag);
                                        }
                                    } else {
                                        result.push_str(tag);
                                    }
                                } else {
                                    result.push_str(tag);
                                }
                            } else {
                                // 其他动态标签保留原样
                                result.push_str(tag);
                            }
                        },
                        _ => {
                            // 其他标签保留原样
                            result.push_str(tag);
                        }
                    }
                }
            },
            TemplateFragment::JsonBlock { prefix, content, suffix } => {
                // 处理 JSON 块：前缀 + 递归处理内容 + 后缀
                result.push_str(prefix);
                for inner_fragment in content {
                    match inner_fragment {
                        TemplateFragment::Static(text) => {
                            result.push_str(text);
                        },
                        TemplateFragment::Tag(tag, tag_type) => {
                            if let Some(value) = tag_replace.get(tag) {
                                result.push_str(value);
                            } else {
                                // 简化的 JSON 块标签处理
                                match tag_type {
                                    TagType::Fixed => {
                                        // JSON 块中的固定标签处理（与主处理逻辑一致）
                                        if let Some(cached_value) = lazy_fixed_tags.get(tag) {
                                            result.push_str(cached_value);
                                        } else {
                                            let generated_value = if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(rng, "pic"))
                                            } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(rng, "icon"))
                                            } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                                Some(region_resource.get_random_data(rng, "author"))
                                            } else {
                                                None
                                            };

                                            if let Some(value) = generated_value {
                                                lazy_fixed_tags.insert(tag.to_string(), value.clone());
                                                result.push_str(&value);
                                            } else {
                                                result.push_str(tag);
                                            }
                                        }
                                    },
                                    TagType::Parameterized(param) => {
                                        if tag.starts_with("{随机数字") {
                                            if param.contains('-') {
                                                if let Some((min, max)) = param.split_once('-') {
                                                    let min_val = min.trim().parse::<i32>().unwrap_or(1);
                                                    let max_val = max.trim().parse::<i32>().unwrap_or(100);
                                                    let num = rng.gen_range(min_val..=max_val);
                                                    result.push_str(&num.to_string());
                                                } else {
                                                    let num = rng.gen_range(1..100);
                                                    result.push_str(&num.to_string());
                                                }
                                            } else {
                                                let length = param.parse::<usize>().unwrap_or(6);
                                                result.push_str(&random_number(rng, length));
                                            }
                                        } else if tag.starts_with("{随机字母") {
                                            let length = param.parse::<usize>().unwrap_or(6);
                                            result.push_str(&random_letters(rng, length));
                                        } else {
                                            result.push_str(tag);
                                        }
                                    },
                                    TagType::Dynamic => {
                                        if tag == "{随机关键词}" {
                                            result.push_str(&region_resource.get_random_data(rng, "keyword"));
                                        } else if tag.starts_with("{固定图片") && tag.ends_with("}") {
                                            // JSON块中的固定图片标签
                                            if let Some(num_str) = tag.strip_prefix("{固定图片").and_then(|s| s.strip_suffix("}")) {
                                                if let Ok(num) = num_str.parse::<usize>() {
                                                    if num >= 1 && num <= 100 {
                                                        result.push_str(&region_resource.get_random_data(rng, "pic"));
                                                    } else {
                                                        result.push_str(tag);
                                                    }
                                                } else {
                                                    result.push_str(tag);
                                                }
                                            } else {
                                                result.push_str(tag);
                                            }
                                        } else if tag.starts_with("{固定图标") && tag.ends_with("}") {
                                            // JSON块中的固定图标标签
                                            if let Some(num_str) = tag.strip_prefix("{固定图标").and_then(|s| s.strip_suffix("}")) {
                                                if let Ok(num) = num_str.parse::<usize>() {
                                                    if num >= 1 && num <= 100 {
                                                        result.push_str(&region_resource.get_random_data(rng, "icon"));
                                                    } else {
                                                        result.push_str(tag);
                                                    }
                                                } else {
                                                    result.push_str(tag);
                                                }
                                            } else {
                                                result.push_str(tag);
                                            }
                                        } else if tag.starts_with("{固定作者") && tag.ends_with("}") {
                                            // JSON块中的固定作者标签
                                            if let Some(num_str) = tag.strip_prefix("{固定作者").and_then(|s| s.strip_suffix("}")) {
                                                if let Ok(num) = num_str.parse::<usize>() {
                                                    if num >= 1 && num <= 100 {
                                                        result.push_str(&region_resource.get_random_data(rng, "author"));
                                                    } else {
                                                        result.push_str(tag);
                                                    }
                                                } else {
                                                    result.push_str(tag);
                                                }
                                            } else {
                                                result.push_str(tag);
                                            }
                                        } else {
                                            result.push_str(tag);
                                        }
                                    },
                                    _ => {
                                        result.push_str(tag);
                                    }
                                }
                            }
                        },
                        _ => {
                            // 其他片段类型，保留原样
                        }
                    }
                }
                result.push_str(suffix);
            }
        }
    }

    result
}
