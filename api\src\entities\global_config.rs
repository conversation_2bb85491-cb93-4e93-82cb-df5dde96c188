// use crate::database::redis::RedisClient;
// use serde::{Deserialize, Serialize};
// use sqlx::{MySql, Pool};
// #[derive(sqlx::FromRow, Debug, Clone, Serialize, Deserialize)]

use std::env;

#[derive(Clone, Debug)]
pub struct GlobalConfig {
    pub database: Database,
    pub redis: Redis,
    pub time_zone: String,
    pub app_config: AppConfig,
    pub cache_config: CacheConfig,
    pub mongo: Mongo,
    pub app_features: AppFeatures,
}

#[derive(Clone, Debug)]
pub struct Database {
    pub host: String,
    pub port: String,
    pub db: String,
    pub username: String,
    pub password: String,
    pub min_connections: u32,
    pub max_connections: u32,
    pub max_lifetime: u64,
}
#[derive(Clone, Debug)]
pub struct Redis {
    pub host: String,
    pub port: String,
    pub db: String,
    pub username: String,
    pub password: String,
    pub max_pool: usize,
}

#[derive(<PERSON><PERSON>, Debug)]
pub struct Mongo {
    pub host: String,
    pub port: String,
    pub db: String,
    pub username: String,
    pub password: String,
    pub min_pool: u32,
    pub max_pool: u32,
    pub timeout: u64,
    pub cache_expiration_days: u64,
}


#[derive(Clone, Debug)]
pub struct AppConfig {
    pub bind_addr: String,
    pub allowed_spiders: Vec<String>,
}
#[derive(Clone, Debug)]
pub struct CacheConfig {
    pub first_cache_expiration: u64,
    pub cache_expiration: u64,
}

#[derive(Clone, Debug)]
pub struct AppFeatures {
    pub auto_clean_inactive_domains: bool,
}

impl GlobalConfig {
    pub fn new() -> GlobalConfig {
        let database = Database {
            host: env_var("DATABASE_HOST"),
            port: env_var("DATABASE_PORT"),
            db: env_var("DATABASE_DB"),
            username: env_var_default("DATABASE_USERNAME", ""),
            password: env_var_default("DATABASE_PASSWORD", ""),
            min_connections: env_var_parse("DATABASE_MIN_CONNECTIONS"),
            max_connections: env_var_parse("DATABASE_MAX_CONNECTIONS"),
            max_lifetime: env_var_parse("DATABASE_MAX_LIFETIME"),
        };

        let redis = Redis {
            host: env_var("REDIS_HOST"),
            port: env_var("REDIS_PORT"),
            db: env_var_default("REDIS_DB", "0"),
            username: env_var_default("REDIS_USERNAME", ""),
            password: env_var_default("REDIS_PASSWORD", ""),
            max_pool: env_var_parse("REDIS_MAX_POOL_SIZE"),
        };

        let mongo = Mongo {
            host: env_var("MONGO_HOST"),
            port: env_var("MONGO_PORT"),
            db: env_var_default("MONGO_DB", ""),
            username: env_var_default("MONGO_USERNAME", ""),
            password: env_var_default("MONGO_PASSWORD", ""),
            max_pool: env_var_parse("MONGO_MAX_POOL_SIZE"),
            min_pool: env_var_parse("MONGO_MIN_POOL_SIZE"),
            timeout: env_var_parse("MONGO_TIMEOUT"),
            cache_expiration_days: env_var_parse("MONGO_CACHE_EXPIRATION_DAYS"),
        };

        let time_zone = env_var("TIME_ZONE");



        let app_config = AppConfig {
            bind_addr: env_var("BIND_ADDR"),
            allowed_spiders: parse_allowed_spiders(&env_var_default("ALLOWED_SPIDERS", "google")),
        };

        let cache_config = CacheConfig {
            first_cache_expiration: env_var_parse("FIRST_CACHE_EXPIRATION"),
            cache_expiration: env_var_parse("CACHE_EXPIRATION"),
        };

        let app_features = AppFeatures {
            auto_clean_inactive_domains: env_var_parse_bool_default("AUTO_CLEAN_INACTIVE_DOMAINS", true),
        };

        GlobalConfig {
            mongo,
            database,
            redis,
            time_zone,
            app_config,
            cache_config,
            app_features,
        }
    }

    /// 检查用户代理是否为允许的蜘蛛
    pub fn is_allowed_spider(&self, user_agent: &str) -> bool {
        let user_agent_lower = user_agent.to_lowercase();
        self.app_config.allowed_spiders.iter().any(|spider| {
            user_agent_lower.contains(spider)
        })
    }

    /// 检查来路是否包含允许的搜索引擎
    pub fn is_from_allowed_search_engine(&self, referer: &str) -> bool {
        let referer_lower = referer.to_lowercase();
        self.app_config.allowed_spiders.iter().any(|spider| {
            referer_lower.contains(spider)
        })
    }
}

fn env_var(key: &str) -> String {
    env::var(key).unwrap_or_else(|_| panic!("{} is not set", key))
}

fn env_var_parse<T: std::str::FromStr>(key: &str) -> T {
    env_var(key)
        .parse()
        .unwrap_or_else(|_| panic!("Environment variable '{}' has an invalid value", key))
}
fn env_var_default(key: &str, default: &str) -> String {
    env::var(key).unwrap_or_else(|_| default.to_string())
}

fn env_var_parse_bool_default(key: &str, default: bool) -> bool {
    match env::var(key) {
        Ok(val) => {
            match val.to_lowercase().as_str() {
                "true" | "1" | "yes" | "y" | "on" => true,
                "false" | "0" | "no" | "n" | "off" => false,
                _ => default,
            }
        },
        Err(_) => default,
    }
}

fn _env_var_as_or_default<T: std::str::FromStr>(key: &str, default: T) -> T {
    env_var(key).parse().unwrap_or(default)
}

/// 解析ALLOWED_SPIDERS配置，支持逗号分隔的蜘蛛名称
fn parse_allowed_spiders(spiders_str: &str) -> Vec<String> {
    spiders_str
        .split(',')
        .map(|s| s.trim().to_lowercase())
        .filter(|s| !s.is_empty())
        .collect()
}
