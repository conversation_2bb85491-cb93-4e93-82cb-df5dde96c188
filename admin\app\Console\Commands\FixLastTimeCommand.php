<?php

namespace App\Console\Commands;

use App\Models\SeoSite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class FixLastTimeCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:last-time';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复最后爬取时间';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始修复最后爬取时间...');
        
        try {
            // 获取所有网站
            $sites = SeoSite::all();
            $this->info('找到 ' . count($sites) . ' 个网站');
            
            $updatedCount = 0;
            $noTimeCount = 0;
            
            foreach ($sites as $site) {
                $this->info('处理网站: ' . $site->host);
                
                // 从Redis获取最后爬取时间
                $lastTime = Redis::get('lasttime:' . $site->host);
                
                if ($lastTime) {
                    // 更新到数据库
                    $site->last_time = $lastTime;
                    $site->save();
                    
                    $this->info('更新最后爬取时间: ' . date('Y-m-d H:i:s', $lastTime));
                    $updatedCount++;
                } else {
                    $this->warn('未找到爬取时间: ' . $site->host);
                    $noTimeCount++;
                }
            }
            
            $this->info('修复完成。');
            $this->info('更新网站数: ' . $updatedCount);
            $this->info('无爬取时间网站数: ' . $noTimeCount);
            
            return 0;
        } catch (\Exception $e) {
            $this->error('修复失败: ' . $e->getMessage());
            return 1;
        }
    }
} 