/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.8.0 (2021-05-06)
 */
!function(){"use strict";var e,n=function(r){return function(n){return t=typeof(e=n),(null===e?"null":"object"==t&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":t)===r;var e,t}},t=function(e){return function(n){return typeof n===e}},p=n("string"),g=n("object"),a=n("array"),r=t("boolean"),i=(e=undefined,function(n){return e===n}),c=function(n){return!(null===(e=n)||e===undefined);var e},l=t("function"),f=t("number"),C=function(){},b=function(n){return function(){return n}},d=function(n){return n};function S(r){for(var o=[],n=1;n<arguments.length;n++)o[n-1]=arguments[n];return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];var t=o.concat(n);return r.apply(null,t)}}var o,u,s,h=function(e){return function(n){return!e(n)}},m=b(!1),T=b(!0),v=function(){return w},w=(o=function(n){return n.isNone()},{fold:function(n,e){return n()},is:m,isSome:m,isNone:T,getOr:s=function(n){return n},getOrThunk:u=function(n){return n()},getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:b(null),getOrUndefined:b(undefined),or:s,orThunk:u,map:v,each:C,bind:v,exists:m,forall:T,filter:v,equals:o,equals_:o,toArray:function(){return[]},toString:b("none()")}),y=function(t){var n=b(t),e=function(){return o},r=function(n){return n(t)},o={fold:function(n,e){return e(t)},is:function(n){return t===n},isSome:T,isNone:m,getOr:n,getOrThunk:n,getOrDie:n,getOrNull:n,getOrUndefined:n,or:e,orThunk:e,map:function(n){return y(n(t))},each:function(n){n(t)},bind:r,exists:r,forall:r,filter:function(n){return n(t)?o:w},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(n){return n.is(t)},equals_:function(n,e){return n.fold(m,function(n){return e(t,n)})}};return o},x={some:y,none:v,from:function(n){return null===n||n===undefined?w:y(n)}},R=Array.prototype.slice,D=Array.prototype.indexOf,O=Array.prototype.push,A=function(n,e){return t=n,r=e,-1<D.call(t,r);var t,r},k=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return!0}return!1},I=function(n,e){for(var t=[],r=0;r<n;r++)t.push(e(r));return t},B=function(n,e){for(var t=n.length,r=new Array(t),o=0;o<t;o++){var u=n[o];r[o]=e(u,o)}return r},E=function(n,e){for(var t=0,r=n.length;t<r;t++){e(n[t],t)}},P=function(n,e){for(var t=[],r=0,o=n.length;r<o;r++){var u=n[r];e(u,r)&&t.push(u)}return t},M=function(n,e,t){return function(n,e){for(var t=n.length-1;0<=t;t--){e(n[t],t)}}(n,function(n){t=e(t,n)}),t},N=function(n,e,t){return E(n,function(n){t=e(t,n)}),t},j=function(n,e){return function(n,e,t){for(var r=0,o=n.length;r<o;r++){var u=n[r];if(e(u,r))return x.some(u);if(t(u,r))break}return x.none()}(n,e,m)},W=function(n,e){for(var t=0,r=n.length;t<r;t++){if(e(n[t],t))return x.some(t)}return x.none()},L=function(n){for(var e=[],t=0,r=n.length;t<r;++t){if(!a(n[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+n);O.apply(e,n[t])}return e},z=function(n,e){return L(B(n,e))},_=function(n,e){for(var t=0,r=n.length;t<r;++t){if(!0!==e(n[t],t))return!1}return!0},F=function(n,e){for(var t={},r=0,o=n.length;r<o;r++){var u=n[r];t[String(u)]=e(u,r)}return t},H=function(n){return[n]},q=function(n,e){return 0<=e&&e<n.length?x.some(n[e]):x.none()},V=function(n){return q(n,0)},U=function(n){return q(n,n.length-1)},K=function(n,e){for(var t=0;t<n.length;t++){var r=e(n[t],t);if(r.isSome())return r}return x.none()},$=function(){return($=Object.assign||function(n){for(var e,t=1,r=arguments.length;t<r;t++)for(var o in e=arguments[t])Object.prototype.hasOwnProperty.call(e,o)&&(n[o]=e[o]);return n}).apply(this,arguments)};function G(){for(var n=0,e=0,t=arguments.length;e<t;e++)n+=arguments[e].length;for(var r=Array(n),o=0,e=0;e<t;e++)for(var u=arguments[e],i=0,c=u.length;i<c;i++,o++)r[o]=u[i];return r}var X,Y=function(t){var r,o=!1;return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return o||(o=!0,r=t.apply(null,n)),r}},J=function(n,e){var t=function(n,e){for(var t=0;t<n.length;t++){var r=n[t];if(r.test(e))return r}return undefined}(n,e);if(!t)return{major:0,minor:0};var r=function(n){return Number(e.replace(t,"$"+n))};return Z(r(1),r(2))},Q=function(){return Z(0,0)},Z=function(n,e){return{major:n,minor:e}},nn={nu:Z,detect:function(n,e){var t=String(e).toLowerCase();return 0===n.length?Q():J(n,t)},unknown:Q},en=function(n,e){var t=String(e).toLowerCase();return j(n,function(n){return n.search(t)})},tn=function(n,t){return en(n,t).map(function(n){var e=nn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},rn=function(n,t){return en(n,t).map(function(n){var e=nn.detect(n.versionRegexes,t);return{current:n.name,version:e}})},on=function(n,e,t){return""===e||n.length>=e.length&&n.substr(t,t+e.length)===e},un=function(n,e){return-1!==n.indexOf(e)},cn=function(n,e){return on(n,e,0)},an=function(n,e){return on(n,e,n.length-e.length)},ln=(X=/^\s+|\s+$/g,function(n){return n.replace(X,"")}),fn=function(n){return 0<n.length},sn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,dn=function(e){return function(n){return un(n,e)}},mn=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return un(n,"edge/")&&un(n,"chrome")&&un(n,"safari")&&un(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,sn],search:function(n){return un(n,"chrome")&&!un(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return un(n,"msie")||un(n,"trident")}},{name:"Opera",versionRegexes:[sn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:dn("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:dn("firefox")},{name:"Safari",versionRegexes:[sn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(un(n,"safari")||un(n,"mobile/"))&&un(n,"applewebkit")}}],gn=[{name:"Windows",search:dn("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return un(n,"iphone")||un(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:dn("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:dn("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:dn("linux"),versionRegexes:[]},{name:"Solaris",search:dn("sunos"),versionRegexes:[]},{name:"FreeBSD",search:dn("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:dn("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],pn={browsers:b(mn),oses:b(gn)},hn="Firefox",vn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isEdge:r("Edge"),isChrome:r("Chrome"),isIE:r("IE"),isOpera:r("Opera"),isFirefox:r(hn),isSafari:r("Safari")}},bn={unknown:function(){return vn({current:undefined,version:nn.unknown()})},nu:vn,edge:b("Edge"),chrome:b("Chrome"),ie:b("IE"),opera:b("Opera"),firefox:b(hn),safari:b("Safari")},wn="Windows",yn="Android",Cn="Solaris",xn="FreeBSD",Sn="ChromeOS",Tn=function(n){var e=n.current,t=n.version,r=function(n){return function(){return e===n}};return{current:e,version:t,isWindows:r(wn),isiOS:r("iOS"),isAndroid:r(yn),isOSX:r("OSX"),isLinux:r("Linux"),isSolaris:r(Cn),isFreeBSD:r(xn),isChromeOS:r(Sn)}},Rn={unknown:function(){return Tn({current:undefined,version:nn.unknown()})},nu:Tn,windows:b(wn),ios:b("iOS"),android:b(yn),linux:b("Linux"),osx:b("OSX"),solaris:b(Cn),freebsd:b(xn),chromeos:b(Sn)},Dn=function(n,e){var t,r,o,u,i,c,a,l,f,s,d,m,g=pn.browsers(),p=pn.oses(),h=tn(g,n).fold(bn.unknown,bn.nu),v=rn(p,n).fold(Rn.unknown,Rn.nu);return{browser:h,os:v,deviceType:(r=h,o=n,u=e,i=(t=v).isiOS()&&!0===/ipad/i.test(o),c=t.isiOS()&&!i,a=t.isiOS()||t.isAndroid(),l=a||u("(pointer:coarse)"),f=i||!c&&a&&u("(min-device-width:768px)"),s=c||a&&!f,d=r.isSafari()&&t.isiOS()&&!1===/safari/i.test(o),m=!s&&!f&&!d,{isiPad:b(i),isiPhone:b(c),isTablet:b(f),isPhone:b(s),isTouch:b(l),isAndroid:t.isAndroid,isiOS:t.isiOS,isWebView:b(d),isDesktop:b(m)})}},On=function(n){return window.matchMedia(n).matches},An=Y(function(){return Dn(navigator.userAgent,On)}),kn=function(){return An()},In=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:n}},Bn={fromHtml:function(n,e){var t=(e||document).createElement("div");if(t.innerHTML=n,!t.hasChildNodes()||1<t.childNodes.length)throw console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return In(t.childNodes[0])},fromTag:function(n,e){var t=(e||document).createElement(n);return In(t)},fromText:function(n,e){var t=(e||document).createTextNode(n);return In(t)},fromDom:In,fromPoint:function(n,e,t){return x.from(n.dom.elementFromPoint(e,t)).map(In)}},En=function(n,e){var t=n.dom;if(1!==t.nodeType)return!1;var r=t;if(r.matches!==undefined)return r.matches(e);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(e);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(e);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(e);throw new Error("Browser lacks native selectors")},Pn=function(n){return 1!==n.nodeType&&9!==n.nodeType&&11!==n.nodeType||0===n.childElementCount},Mn=function(n,e){return n.dom===e.dom},Nn=function(n,e){return t=n.dom,r=e.dom,o=t,u=r,i=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(o.compareDocumentPosition(u)&i);var t,r,o,u,i},jn=function(n,e){return kn().browser.isIE()?Nn(n,e):(t=e,r=n.dom,o=t.dom,r!==o&&r.contains(o));var t,r,o},Wn=En,Ln=Object.keys,zn=Object.hasOwnProperty,_n=function(n,e){for(var t=Ln(n),r=0,o=t.length;r<o;r++){var u=t[r];e(n[u],u)}},Fn=function(n,t){return Hn(n,function(n,e){return{k:e,v:t(n,e)}})},Hn=function(n,r){var o={};return _n(n,function(n,e){var t=r(n,e);o[t.k]=t.v}),o},qn=function(n,e){var t,r,o,u,i={};return t=e,u=i,r=function(n,e){u[e]=n},o=C,_n(n,function(n,e){(t(n,e)?r:o)(n,e)}),i},Vn=function(n,t){var r=[];return _n(n,function(n,e){r.push(t(n,e))}),r},Un=function(n){return Ln(n).length},Kn=function(n,e){return $n(n,e)?x.from(n[e]):x.none()},$n=function(n,e){return zn.call(n,e)},Gn=function(n,e){return $n(n,e)&&n[e]!==undefined&&null!==n[e]},Xn=["tfoot","thead","tbody","colgroup"],Yn=function(n,e,t){return{element:n,rowspan:e,colspan:t}},Jn=function(n,e,t){return{element:n,cells:e,section:t}},Qn=function(n,e,t){return{element:n,isNew:e,isLocked:t}},Zn=function(n,e){return{cells:n,section:e}},ne=("undefined"!=typeof window||Function("return this;")(),function(n){return n.dom.nodeName.toLowerCase()}),ee=function(n){return n.dom.nodeType},te=function(e){return function(n){return ee(n)===e}},re=function(n){return 8===ee(n)||"#comment"===ne(n)},oe=te(1),ue=te(3),ie=te(9),ce=te(11),ae=function(e){return function(n){return oe(n)&&ne(n)===e}},le=function(n){return Bn.fromDom(n.dom.ownerDocument)},fe=function(n){return ie(n)?n:le(n)},se=function(n){return x.from(n.dom.parentNode).map(Bn.fromDom)},de=function(n,e){for(var t=l(e)?e:m,r=n.dom,o=[];null!==r.parentNode&&r.parentNode!==undefined;){var u=r.parentNode,i=Bn.fromDom(u);if(o.push(i),!0===t(i))break;r=u}return o},me=function(n){return x.from(n.dom.previousSibling).map(Bn.fromDom)},ge=function(n){return x.from(n.dom.nextSibling).map(Bn.fromDom)},pe=function(n){return B(n.dom.childNodes,Bn.fromDom)},he=function(n,e){var t=n.dom.childNodes;return x.from(t[e]).map(Bn.fromDom)},ve=l(Element.prototype.attachShadow)&&l(Node.prototype.getRootNode),be=b(ve),we=ve?function(n){return Bn.fromDom(n.dom.getRootNode())}:fe,ye=function(n){var e,t=we(n);return ce(e=t)&&c(e.dom.host)?x.some(t):x.none()},Ce=function(n){return Bn.fromDom(n.dom.host)},xe=function(n){return c(n.dom.shadowRoot)},Se=function(n){var e=ue(n)?n.dom.parentNode:n.dom;if(e===undefined||null===e||null===e.ownerDocument)return!1;var t,r,o=e.ownerDocument;return ye(Bn.fromDom(e)).fold(function(){return o.body.contains(e)},(t=Se,r=Ce,function(n){return t(r(n))}))},Te=function(n){var e=n.dom.body;if(null===e||e===undefined)throw new Error("Body is not available yet");return Bn.fromDom(e)},Re=function(n,e){var t=[];return E(pe(n),function(n){e(n)&&(t=t.concat([n])),t=t.concat(Re(n,e))}),t},De=function(n,e,t){return r=function(n){return En(n,e)},P(de(n,t),r);var r},Oe=function(n,e){return t=function(n){return En(n,e)},P(pe(n),t);var t},Ae=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,Pn(o)?[]:B(o.querySelectorAll(t),Bn.fromDom);var t,r,o};function ke(n,e,t,r,o){return n(t,r)?x.some(t):l(o)&&o(t)?x.none():e(t,r,o)}var Ie,Be,Ee,Pe=function(n,e,t){for(var r=n.dom,o=l(t)?t:m;r.parentNode;){r=r.parentNode;var u=Bn.fromDom(r);if(e(u))return x.some(u);if(o(u))break}return x.none()},Me=function(n,e,t){return Pe(n,function(n){return En(n,e)},t)},Ne=function(n,e){return t=function(n){return En(n,e)},j(n.dom.childNodes,function(n){return t(Bn.fromDom(n))}).map(Bn.fromDom);var t},je=function(n,e){return t=e,o=(r=n)===undefined?document:r.dom,Pn(o)?x.none():x.from(o.querySelector(t)).map(Bn.fromDom);var t,r,o},We=function(n,e,t){return ke(En,Me,n,e,t)},Le=function(n,e,t){if(!(p(t)||r(t)||f(t)))throw console.error("Invalid call to Attribute.set. Key ",e,":: Value ",t,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(e,t+"")},ze=function(n,e,t){Le(n.dom,e,t)},_e=function(n,e){var t=n.dom;_n(e,function(n,e){Le(t,e,n)})},Fe=function(n,e){var t=n.dom.getAttribute(e);return null===t?undefined:t},He=function(n,e){return x.from(Fe(n,e))},qe=function(n,e){n.dom.removeAttribute(e)},Ve=function(n){return N(n.dom.attributes,function(n,e){return n[e.name]=e.value,n},{})},Ue=function(n){return n.style!==undefined&&l(n.style.getPropertyValue)},Ke=function(n,e,t){if(!p(t))throw console.error("Invalid call to CSS.set. Property ",e,":: Value ",t,":: Element ",n),new Error("CSS value must be a string: "+t);Ue(n)&&n.style.setProperty(e,t)},$e=function(n,e,t){var r=n.dom;Ke(r,e,t)},Ge=function(n,e){var t=n.dom;_n(e,function(n,e){Ke(t,e,n)})},Xe=function(n,e){var t=n.dom,r=window.getComputedStyle(t).getPropertyValue(e);return""!==r||Se(n)?r:Ye(t,e)},Ye=function(n,e){return Ue(n)?n.style.getPropertyValue(e):""},Je=function(n,e){var t=n.dom,r=Ye(t,e);return x.from(r).filter(function(n){return 0<n.length})},Qe=function(n,e){var t,r,o=n.dom;r=e,Ue(t=o)&&t.style.removeProperty(r),He(n,"style").map(ln).is("")&&qe(n,"style")},Ze=function(n,e,t){return void 0===t&&(t=0),He(n,e).map(function(n){return parseInt(n,10)}).getOr(t)},nt=function(n,e){return Ze(n,e,1)},et=function(n){return 1<nt(n,"colspan")},tt=function(n){return 1<nt(n,"rowspan")},rt=function(n,e){return parseInt(Xe(n,e),10)},ot=b(10),ut=b(10),it=function(n,e){return ct(n,e,T)},ct=function(n,e,t){return z(pe(n),function(n){return En(n,e)?t(n)?[n]:[]:ct(n,e,t)})},at=function(n,e){return function(n,e,t){if(void 0===t&&(t=m),t(e))return x.none();if(A(n,ne(e)))return x.some(e);return Me(e,n.join(","),function(n){return En(n,"table")||t(n)})}(["td","th"],n,e)},lt=function(n){return it(n,"th,td")},ft=function(n){return En(n,"colgroup")?Oe(n,"col"):z(mt(n),function(n){return Oe(n,"col")})},st=function(n,e){return We(n,"table",e)},dt=function(n){return it(n,"tr")},mt=function(n){return st(n).fold(b([]),function(n){return Oe(n,"colgroup")})},gt=function(n,t){return B(n,function(n){if("colgroup"===ne(n)){var e=B(ft(n),function(n){var e=Ze(n,"span",1);return Yn(n,1,e)});return Jn(n,e,"colgroup")}e=B(lt(n),function(n){var e=Ze(n,"rowspan",1),t=Ze(n,"colspan",1);return Yn(n,e,t)});return Jn(n,e,t(n))})},pt=function(n){return se(n).map(function(n){var e=ne(n);return A(Xn,e)?e:"tbody"}).getOr("tbody")},ht=function(n){var e=dt(n),t=G(mt(n),e);return gt(t,pt)},vt=function(n,e,t){var r=n.cells,o=r.slice(0,e),u=r.slice(e),i=o.concat(t).concat(u);return yt(n,i)},bt=function(n,e,t){return vt(n,e,[t])},wt=function(n,e,t){n.cells[e]=t},yt=function(n,e){return Zn(e,n.section)},Ct=function(n,e){var t=n.cells,r=B(t,e);return Zn(r,n.section)},xt=function(n,e){return n.cells[e]},St=function(n,e){return xt(n,e).element},Tt=function(n){return n.cells.length},Rt=function(n){var e=function(n,e){for(var t=[],r=[],o=0,u=n.length;o<u;o++){var i=n[o];(e(i,o)?t:r).push(i)}return{pass:t,fail:r}}(n,function(n){return"colgroup"===n.section});return{rows:e.fail,cols:e.pass}},Dt="data-snooker-locked-cols",Ot=function(n){return He(n,Dt).bind(function(n){return x.from(n.match(/\d+/g))}).map(function(n){return F(n,T)})},At=function(n){var e,t,r,o=N(Rt(n).rows,function(t,n){return E(n.cells,function(n,e){n.isLocked&&(t[e]=!0)}),t},{}),u=Vn(o,function(n,e){return parseInt(e,10)});return e=u,(r=R.call(e,0)).sort(t),r},kt=function(n,e){return n+","+e},It=function(n,e){var t=z(n.all,function(n){return n.cells});return P(t,e)},Bt=function(n){var l={},e=[],t={},f=V(n).map(function(n){return n.element}).bind(st).bind(Ot).getOr({}),r=0,s=0,d=0;return E(n,function(n){var a,o,u;"colgroup"===n.section?(o={},u=0,E(n.cells,function(t){var r=t.colspan;I(r,function(n){var e=u+n;o[e]={element:t.element,colspan:r,column:e}}),u+=r}),t=o):(a=[],E(n.cells,function(n){for(var e=0;l[kt(d,e)]!==undefined;)e++;for(var t=Gn(f,e.toString()),r={element:n.element,rowspan:n.rowspan,colspan:n.colspan,row:d,column:e,isLocked:t},o=0;o<n.colspan;o++)for(var u=0;u<n.rowspan;u++){var i=e+o,c=kt(d+u,i);l[c]=r,s=Math.max(s,i+1)}a.push(r)}),r++,e.push(Jn(n.element,a,n.section)),d++)}),{grid:{rows:r,columns:s},access:l,all:e,columns:t}},Et={fromTable:function(n){var e=ht(n);return Bt(e)},generate:Bt,getAt:function(n,e,t){var r=n.access[kt(e,t)];return r!==undefined?x.some(r):x.none()},findItem:function(n,e,t){var r=It(n,function(n){return t(e,n.element)});return 0<r.length?x.some(r[0]):x.none()},filterItems:It,justCells:function(n){return z(n.all,function(n){return n.cells})},justColumns:function(n){return e=n.columns,Vn(e,function(n){return n});var e},hasColumns:function(n){return 0<Ln(n.columns).length},getColumnAt:function(n,e){return x.from(n.columns[e])}},Pt=function(n,e){var t=e.column,r=e.column+e.colspan-1,o=e.row,u=e.row+e.rowspan-1;return t<=n.finishCol&&r>=n.startCol&&o<=n.finishRow&&u>=n.startRow},Mt=function(n,e){return e.column>=n.startCol&&e.column+e.colspan-1<=n.finishCol&&e.row>=n.startRow&&e.row+e.rowspan-1<=n.finishRow},Nt=function(n,e,t){var r=Et.findItem(n,e,Mn),o=Et.findItem(n,t,Mn);return r.bind(function(r){return o.map(function(n){return e=r,t=n,{startRow:Math.min(e.row,t.row),startCol:Math.min(e.column,t.column),finishRow:Math.max(e.row+e.rowspan-1,t.row+t.rowspan-1),finishCol:Math.max(e.column+e.colspan-1,t.column+t.colspan-1)};var e,t})})},jt=function(e,n,t){return Nt(e,n,t).bind(function(n){return function(n,e){for(var t=!0,r=S(Mt,e),o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++)t=t&&Et.getAt(n,o,u).exists(r);return t?x.some(e):x.none()}(e,n)})},Wt=function(t,n,e){return Nt(t,n,e).map(function(n){var e=Et.filterItems(t,S(Pt,n));return B(e,function(n){return n.element})})},Lt=function(n,e){return Et.findItem(n,e,function(n,e){return jn(e,n)}).map(function(n){return n.element})},zt=function(i,c,a){return st(i).bind(function(n){var r,e,o,u,t=Ft(n);return r=t,e=i,o=c,u=a,Et.findItem(r,e,Mn).bind(function(n){var e=0<o?n.row+n.rowspan-1:n.row,t=0<u?n.column+n.colspan-1:n.column;return Et.getAt(r,e+o,t+u).map(function(n){return n.element})})})},_t=function(n,e,t,r,o){var u=Ft(n),i=Mn(n,t)?x.some(e):Lt(u,e),c=Mn(n,o)?x.some(r):Lt(u,r);return i.bind(function(e){return c.bind(function(n){return Wt(u,e,n)})})},Ft=Et.fromTable,Ht=function(e,t){se(e).each(function(n){n.dom.insertBefore(t.dom,e.dom)})},qt=function(n,e){ge(n).fold(function(){se(n).each(function(n){Ut(n,e)})},function(n){Ht(n,e)})},Vt=function(e,t){he(e,0).fold(function(){Ut(e,t)},function(n){e.dom.insertBefore(t.dom,n.dom)})},Ut=function(n,e){n.dom.appendChild(e.dom)},Kt=function(n,e){Ht(n,e),Ut(e,n)},$t=function(r,o){E(o,function(n,e){var t=0===e?r:o[e-1];qt(t,n)})},Gt=function(e,n){E(n,function(n){Ut(e,n)})},Xt=function(n){n.dom.textContent="",E(pe(n),function(n){Yt(n)})},Yt=function(n){var e=n.dom;null!==e.parentNode&&e.parentNode.removeChild(e)},Jt=function(n){var e,t=pe(n);0<t.length&&(e=n,E(t,function(n){Ht(e,n)})),Yt(n)},Qt=(Ie=ue,Be="text",{get:function(n){if(!Ie(n))throw new Error("Can only get "+Be+" value of a "+Be+" node");return Ee(n).getOr("")},getOption:Ee=function(n){return Ie(n)?x.from(n.dom.nodeValue):x.none()},set:function(n,e){if(!Ie(n))throw new Error("Can only set raw "+Be+" value of a "+Be+" node");n.dom.nodeValue=e}}),Zt=function(n){return Qt.get(n)},nr=function(n){return Qt.getOption(n)},er=function(n,e){return Qt.set(n,e)},tr=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function rr(){return{up:b({selector:Me,closest:We,predicate:Pe,all:de}),down:b({selector:Ae,predicate:Re}),styles:b({get:Xe,getRaw:Je,set:$e,remove:Qe}),attrs:b({get:Fe,set:ze,remove:qe,copyTo:function(n,e){var t=Ve(n);_e(e,t)}}),insert:b({before:Ht,after:qt,afterAll:$t,append:Ut,appendAll:Gt,prepend:Vt,wrap:Kt}),remove:b({unwrap:Jt,remove:Yt}),create:b({nu:Bn.fromTag,clone:function(n){return Bn.fromDom(n.dom.cloneNode(!1))},text:Bn.fromText}),query:b({comparePosition:function(n,e){return n.dom.compareDocumentPosition(e.dom)},prevSibling:me,nextSibling:ge}),property:b({children:pe,name:ne,parent:se,document:function(n){return fe(n).dom},isText:ue,isComment:re,isElement:oe,isSpecial:function(n){var e=ne(n);return A(["script","noscript","iframe","noframes","noembed","title","style","textarea","xmp"],e)},getText:Zt,setText:er,isBoundary:function(n){return!!oe(n)&&("body"===ne(n)||A(tr,ne(n)))},isEmptyTag:function(n){return!!oe(n)&&A(["br","img","hr","input"],ne(n))},isNonEditable:function(n){return oe(n)&&"false"===Fe(n,"contenteditable")}}),eq:Mn,is:Wn}}var or,ur,ir,cr,ar=function(r,o,n,e){var t=o(r,n);return M(e,function(n,e){var t=o(r,e);return lr(r,n,t)},t)},lr=function(e,n,t){return n.bind(function(n){return t.filter(S(e.eq,n))})},fr=function(n,e,t){return 0<t.length?ar(n,e,(r=t)[0],r.slice(1)):x.none();var r},sr=function(t,n,e,r){void 0===r&&(r=m);var o=[n].concat(t.up().all(n)),u=[e].concat(t.up().all(e)),i=function(e){return W(e,r).fold(function(){return e},function(n){return e.slice(0,n+1)})},c=i(o),a=i(u),l=j(c,function(n){return k(a,(e=n,S(t.eq,e)));var e});return{firstpath:c,secondpath:a,shared:l}},dr=rr(),mr=function(t,n){return fr(dr,function(n,e){return t(e)},n)},gr=function(n){return Me(n,"table")},pr=function(l,f,s){var d=function(e){return function(n){return s!==undefined&&s(n)||Mn(n,e)}};return Mn(l,f)?x.some({boxes:x.some([l]),start:l,finish:f}):gr(l).bind(function(a){return gr(f).bind(function(u){if(Mn(a,u))return x.some({boxes:(o=l,i=f,c=Ft(a),Wt(c,o,i)),start:l,finish:f});if(jn(a,u)){var n=0<(e=De(f,"td,th",d(a))).length?e[e.length-1]:f;return x.some({boxes:_t(a,l,a,f,u),start:l,finish:n})}if(jn(u,a)){var e,t=0<(e=De(l,"td,th",d(u))).length?e[e.length-1]:l;return x.some({boxes:_t(u,l,a,f,u),start:l,finish:t})}return sr(dr,l,f,r).shared.bind(function(n){return We(n,"table",s).bind(function(n){var e=De(f,"td,th",d(n)),t=0<e.length?e[e.length-1]:f,r=De(l,"td,th",d(n)),o=0<r.length?r[r.length-1]:l;return x.some({boxes:_t(n,l,a,f,u),start:o,finish:t})})});var r,o,i,c})})},hr=function(n,e){var t=Ae(n,e);return 0<t.length?x.some(t):x.none()},vr=function(n,e,r){return je(n,e).bind(function(t){return je(n,r).bind(function(e){return mr(gr,[t,e]).map(function(n){return{first:t,last:e,table:n}})})})},br=function(n,e,t,r,o){return u=o,j(n,function(n){return En(n,u)}).bind(function(n){return zt(n,e,t).bind(function(n){return t=r,Me(e=n,"table").bind(function(n){return je(n,t).bind(function(n){return pr(n,e).bind(function(e){return e.boxes.map(function(n){return{boxes:n,start:e.start,finish:e.finish}})})})});var e,t})});var u},wr=hr,yr=function(o,n,e){return vr(o,n,e).bind(function(i){var n=function(n){return Mn(o,n)},e="thead,tfoot,tbody,table",t=Me(i.first,e,n),r=Me(i.last,e,n);return t.bind(function(u){return r.bind(function(n){return Mn(u,n)?(e=i.table,t=i.first,r=i.last,o=Ft(e),jt(o,t,r)):x.none();var e,t,r,o})})})},Cr=function(i){if(!a(i))throw new Error("cases must be an array");if(0===i.length)throw new Error("there must be at least one case");var c=[],t={};return E(i,function(n,r){var e=Ln(n);if(1!==e.length)throw new Error("one and only one name per case");var o=e[0],u=n[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!a(u))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=t.length;if(e!==u.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+u.length+" ("+u+"), got "+e);return{fold:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];if(n.length!==i.length)throw new Error("Wrong number of arguments to fold. Expected "+i.length+", got "+n.length);return n[r].apply(null,t)},match:function(n){var e=Ln(n);if(c.length!==e.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+e.join(","));if(!_(c,function(n){return A(e,n)}))throw new Error("Not all branches were specified when using match. Specified: "+e.join(", ")+"\nRequired: "+c.join(", "));return n[o].apply(null,t)},log:function(n){console.log(n,{constructors:c,constructor:o,params:t})}}}}),t},xr=Cr([{none:[]},{multiple:["elements"]},{single:["element"]}]),Sr=function(n,e,t,r){return n.fold(e,t,r)},Tr=xr.none,Rr=xr.multiple,Dr=xr.single,Or=tinymce.util.Tools.resolve("tinymce.PluginManager"),Ar=function(n,e){return Bn.fromDom(n.dom.cloneNode(e))},kr=function(n){return Ar(n,!1)},Ir=function(n){return Ar(n,!0)},Br=function(n,e){var t,r,o,u,i=(t=n,r=e,o=Bn.fromTag(r),u=Ve(t),_e(o,u),o),c=pe(Ir(n));return Gt(i,c),i},Er=function(n){for(var e=[],t=function(n){e.push(n)},r=0;r<n.length;r++)n[r].each(t);return e},Pr=function(n,e){return n?x.some(e):x.none()},Mr=function(r,o){var n=function(n){var e=o(n);if(e<=0||null===e){var t=Xe(n,r);return parseFloat(t)||0}return e},u=function(o,n){return N(n,function(n,e){var t=Xe(o,e),r=t===undefined?0:parseInt(t,10);return isNaN(r)?n:n+r},0)};return{set:function(n,e){if(!f(e)&&!e.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+e);var t=n.dom;Ue(t)&&(t.style[r]=e+"px")},get:n,getOuter:n,aggregate:u,max:function(n,e,t){var r=u(n,t);return r<e?e-r:0}}},Nr=Mr("width",function(n){return n.dom.offsetWidth}),jr=function(n){return Nr.get(n)},Wr=function(n){return Nr.getOuter(n)},Lr=function(t,r){void 0===r&&(r=T);var n=t.grid,e=I(n.columns,d),o=I(n.rows,d);return B(e,function(e){return zr(function(){return z(o,function(n){return Et.getAt(t,n,e).filter(function(n){return n.column===e}).toArray()})},function(n){return 1===n.colspan&&r(n.element)},function(){return Et.getAt(t,0,e)})})},zr=function(n,e,t){var r=n();return j(r,e).orThunk(function(){return x.from(r[0]).orThunk(t)}).map(function(n){return n.element})},_r=function(t){var n=t.grid,e=I(n.rows,d),r=I(n.columns,d);return B(e,function(e){return zr(function(){return z(r,function(n){return Et.getAt(t,e,n).filter(function(n){return n.row===e}).fold(b([]),function(n){return[n]})})},function(n){return 1===n.rowspan},function(){return Et.getAt(t,e,0)})})},Fr=function(r,o){if(o<0||o>=r.length-1)return x.none();var n=r[o].fold(function(){var n,e,t=(n=r.slice(0,o),(e=R.call(n,0)).reverse(),e);return K(t,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return x.some({value:n,delta:0})}),e=r[o+1].fold(function(){var n=r.slice(o+1);return K(n,function(n,e){return n.map(function(n){return{value:n,delta:e+1}})})},function(n){return x.some({value:n,delta:1})});return n.bind(function(t){return e.map(function(n){var e=n.delta+t.delta;return Math.abs(n.value-t.value)/e})})},Hr=function(e,t){return function(n){return"rtl"===qr(n)?t:e}},qr=function(n){return"rtl"===Xe(n,"direction")?"rtl":"ltr"},Vr=Mr("height",function(n){var e=n.dom;return Se(n)?e.getBoundingClientRect().height:e.offsetHeight}),Ur=function(n){return Vr.get(n)},Kr=function(n){return Vr.getOuter(n)},$r=function(t,r){return{left:t,top:r,translate:function(n,e){return $r(t+n,r+e)}}},Gr=$r,Xr=function(n,e){return n!==undefined?n:e!==undefined?e:0},Yr=function(n){var e=n.dom.ownerDocument,t=e.body,r=e.defaultView,o=e.documentElement;if(t===n.dom)return Gr(t.offsetLeft,t.offsetTop);var u=Xr(null==r?void 0:r.pageYOffset,o.scrollTop),i=Xr(null==r?void 0:r.pageXOffset,o.scrollLeft),c=Xr(o.clientTop,t.clientTop),a=Xr(o.clientLeft,t.clientLeft);return Jr(n).translate(i-a,u-c)},Jr=function(n){var e,t=n.dom,r=t.ownerDocument.body;return r===t?Gr(r.offsetLeft,r.offsetTop):Se(n)?(e=t.getBoundingClientRect(),Gr(e.left,e.top)):Gr(0,0)},Qr=function(n,e){return{row:n,y:e}},Zr=function(n,e){return{col:n,x:e}},no=function(n){return Yr(n).left+Wr(n)},eo=function(n){return Yr(n).left},to=function(n,e){return Zr(n,eo(e))},ro=function(n,e){return Zr(n,no(e))},oo=function(n){return Yr(n).top},uo=function(n,e){return Qr(n,oo(e))},io=function(n,e){return Qr(n,oo(e)+Kr(e))},co=function(t,e,r){if(0===r.length)return[];var n=B(r.slice(1),function(n,e){return n.map(function(n){return t(e,n)})}),o=r[r.length-1].map(function(n){return e(r.length-1,n)});return n.concat([o])},ao={delta:d,positions:function(n){return co(uo,io,n)},edge:oo},lo=Hr({delta:d,edge:eo,positions:function(n){return co(to,ro,n)}},{delta:function(n){return-n},edge:no,positions:function(n){return co(ro,to,n)}}),fo={delta:function(n,e){return lo(e).delta(n,e)},positions:function(n,e){return lo(e).positions(n,e)},edge:function(n){return lo(n).edge(n)}},so={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},mo=(ur="[eE][+-]?[0-9]+",cr=["Infinity",(or="[0-9]+")+"\\."+(ir=function(n){return"(?:"+n+")?"})(or)+ir(ur),"\\."+or+ir(ur),or+ir(ur)].join("|"),new RegExp("^([+-]?(?:"+cr+"))(.*)$")),go=function(n,o){return x.from(mo.exec(n)).bind(function(n){var e,t=Number(n[1]),r=n[2];return e=r,k(o,function(n){return k(so[n],function(n){return e===n})})?x.some({value:t,unit:r}):x.none()})},po=function(){var n=kn().browser;return n.isIE()||n.isEdge()},ho=function(n,e,t){return r=Xe(n,e),o=t,u=parseFloat(r),isNaN(u)?o:u;var r,o,u},vo=function(n){return po()?(t=(e=n).dom.getBoundingClientRect().height,"border-box"===Xe(e,"box-sizing")?t:t-ho(e,"padding-top",0)-ho(e,"padding-bottom",0)-(ho(e,"border-top-width",0)+ho(e,"border-bottom-width",0))):ho(n,"height",Ur(n));var e,t},bo=function(n){return po()?(t=(e=n).dom.getBoundingClientRect().width,"border-box"===Xe(e,"box-sizing")?t:t-ho(e,"padding-left",0)-ho(e,"padding-right",0)-(ho(e,"border-left-width",0)+ho(e,"border-right-width",0))):ho(n,"width",jr(n));var e,t},wo=/(\d+(\.\d+)?)%/,yo=/(\d+(\.\d+)?)px|em/,Co=function(n,e){var t,r=(t=n,x.from(t.dom.offsetParent).map(Bn.fromDom).getOr(Te(le(n))));return e(n)/e(r)*100},xo=function(n,e){$e(n,"width",e+"px")},So=function(n,e){$e(n,"width",e+"%")},To=function(n,e){$e(n,"height",e+"px")},Ro=function(n,e,t,r){var o,u,i,c,a,l=parseInt(n,10);return an(n,"%")&&"table"!==ne(e)?(u=l,i=t,c=r,a=st(o=e).map(function(n){var e=i(n);return Math.floor(u/100*e)}).getOr(u),c(o,a),a):l},Do=function(n){var e,t=Je(e=n,"height").getOrThunk(function(){return vo(e)+"px"});return t?Ro(t,n,Ur,To):Ur(n)},Oo=function(n){return Je(n,"width").fold(function(){return x.from(Fe(n,"width"))},function(n){return x.some(n)})},Ao=function(n,e){return n/e.pixelWidth()*100},ko=function(e,t){return Oo(e).fold(function(){var n=jr(e);return Ao(n,t)},function(n){return function(n,e,t){var r=wo.exec(e);if(null!==r)return parseFloat(r[1]);var o=bo(n);return Ao(o,t)}(e,n,t)})},Io=function(e,t){return Oo(e).fold(function(){return bo(e)},function(n){return function(n,e,t){var r=yo.exec(e);if(null!==r)return parseInt(r[1],10);var o=wo.exec(e);if(null===o)return bo(n);var u=parseFloat(o[1]);return u/100*t.pixelWidth()}(e,n,t)})},Bo=function(n){return t="rowspan",Do(e=n)/nt(e,t);var e,t},Eo=function(n,e,t){$e(n,"width",e+t)},Po=function(n){return Co(n,jr)+"%"},Mo=b(wo),No=b(yo),jo=ae("col"),Wo=function(n,e,t){return Je(n,e).fold(function(){return t(n)+"px"},function(n){return n})},Lo=function(n,e){return Wo(n,"width",function(n){return jo(n)?jr(n):Io(n,e)})},zo=function(n){return Wo(n,"height",Bo)},_o=function(n,e){return Je(n,e).isSome()},Fo=function(n,e,t,r,o,u){return n.filter(r).fold(function(){return u(Fr(t,e))},function(n){return o(n)})},Ho=function(n,e,i,c,a){var t,l=Lr(n,function(n){return _o(n,"width")}),r=Et.hasColumns(n)?(t=n,B(Et.justColumns(t),function(n){return x.from(n.element)})):l,f=[x.some(fo.edge(e))].concat(B(fo.positions(l,e),function(n){return n.map(function(n){return n.x})})),s=h(et);return B(r,function(n,u){return Fo(n,u,f,s,function(n){if(!jo(r=n)||_o(r,"width"))return i(n,a);var e,t,r,o=(e=l[u],t=d,e!==undefined&&null!==e?t(e):x.none());return Fo(o,u,f,s,function(n){return c(x.some(jr(n)))},c)},c)})},qo=function(n){return n.map(function(n){return n+"px"}).getOr("")},Vo=function(n,e,t){return Ho(n,e,Io,function(n){return n.getOrThunk(t.minCellWidth)},t)},Uo=function(n,e,t,r,o){var u=_r(n),i=[x.some(t.edge(e))].concat(B(t.positions(u,e),function(n){return n.map(function(n){return n.y})}));return B(u,function(n,e){return Fo(n,e,i,h(tt),r,o)})},Ko=function(n){var e=n;return{get:function(){return e},set:function(n){e=n}}},$o=function(t){var n=function(){return jr(t)},e=b(0);return{width:n,pixelWidth:n,getWidths:function(n,e){return Vo(n,t,e)},getCellDelta:e,singleColumnWidth:b([0]),minCellWidth:e,setElementWidth:C,adjustTableWidth:C,isRelative:!0,label:"none"}},Go=function(n,r){var o=Ko(parseFloat(n)),u=Ko(jr(r));return{width:o.get,pixelWidth:u.get,getWidths:function(n,e){return Ho(n,r,ko,function(n){return n.fold(function(){return t.minCellWidth()},function(n){return n/t.pixelWidth()*100})},t=e);var t},getCellDelta:function(n){return n/u.get()*100},singleColumnWidth:function(n,e){return[100-n]},minCellWidth:function(){return ot()/u.get()*100},setElementWidth:So,adjustTableWidth:function(n){var e=o.get(),t=e+n/100*e;So(r,t),o.set(t),u.set(jr(r))},isRelative:!0,label:"percent"}},Xo=function(n,t){var r=Ko(n),o=r.get;return{width:o,pixelWidth:o,getWidths:function(n,e){return Vo(n,t,e)},getCellDelta:d,singleColumnWidth:function(n,e){return[Math.max(ot(),n+e)-n]},minCellWidth:ot,setElementWidth:xo,adjustTableWidth:function(n){var e=o()+n;xo(t,e),r.set(e)},isRelative:!1,label:"pixel"}},Yo=function(e){return Oo(e).fold(function(){return $o(e)},function(n){return function(n,e){var t=Mo().exec(e);if(null!==t)return Go(t[1],n);var r=No().exec(e);if(null!==r){var o=parseInt(r[1],10);return Xo(o,n)}var u=jr(n);return Xo(u,n)}(e,n)})},Jo=Xo,Qo=Go,Zo=function(n,e,t,r){for(var o,u,i,c=e.grid.columns,a=e.grid.rows,l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){l<t.minRow||l>t.maxRow||s<t.minCol||s>t.maxCol||(Et.getAt(e,l,s).filter(r).isNone()?(o=f,0,u=n[l].element,i=Bn.fromTag("td"),Ut(i,Bn.fromTag("br")),(o?Ut:Vt)(u,i)):f=!0)}},nu=function(n,e){var t,u,r,i,c,a,l,f,s,o=function(n){return En(n.element,e)},d=Ir(n),m=ht(d),g=Yo(n),p=Et.generate(m),h=(u=o,r=(t=p).grid.columns,i=t.grid.rows,c=r,l=a=0,f=[],s=[],_n(t.access,function(n){var e,t,r,o;f.push(n),u(n)&&(s.push(n),t=(e=n.row)+n.rowspan-1,o=(r=n.column)+n.colspan-1,e<i?i=e:a<t&&(a=t),r<c?c=r:l<o&&(l=o))}),{minRow:i,minCol:c,maxRow:a,maxCol:l,allCells:f,selectedCells:s}),v="th:not("+e+"),td:not("+e+")",b=ct(d,"th,td",function(n){return En(n,v)});return E(b,Yt),Zo(m,p,h,o),function(n,e,t,r){_n(t.columns,function(n){(n.column<e.minCol||n.column>e.maxCol)&&Yt(n.element)});var o=P(it(n,"tr"),function(n){return 0===n.dom.childElementCount});E(o,Yt),e.minCol!==e.maxCol&&e.minRow!==e.maxRow||E(it(n,"th,td"),function(n){qe(n,"rowspan"),qe(n,"colspan")}),qe(n,Dt),qe(n,"data-snooker-col-series"),Yo(n).adjustTableWidth(r)}(d,h,p,function(n,e,t,r){if(0===r.minCol&&e.grid.columns===r.maxCol+1)return 0;var o=Vo(e,n,t),u=N(o,function(n,e){return n+e},0),i=N(o.slice(r.minCol,r.maxCol+1),function(n,e){return n+e},0)/u*t.pixelWidth()-t.pixelWidth();return t.getCellDelta(i)}(n,Et.fromTable(n),g,h)),d},eu=function(n){return"img"===ne(n)?1:nr(n).fold(function(){return pe(n).length},function(n){return n.length})},tu=["img","br"],ru=function(n){return nr(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()||A(tu,ne(n))},ou=function(n){return o=ru,(u=function(n){for(var e=0;e<n.childNodes.length;e++){var t=Bn.fromDom(n.childNodes[e]);if(o(t))return x.some(t);var r=u(n.childNodes[e]);if(r.isSome())return r}return x.none()})(n.dom);var o,u},uu=function(n){return iu(n,ru)},iu=function(n,u){var i=function(n){for(var e=pe(n),t=e.length-1;0<=t;t--){var r=e[t];if(u(r))return x.some(r);var o=i(r);if(o.isSome())return o}return x.none()};return i(n)},cu={scope:["row","col"]},au=function(){var n=Bn.fromTag("td");return Ut(n,Bn.fromTag("br")),n},lu=function(){return Bn.fromTag("col")},fu=function(){return Bn.fromTag("colgroup")},su=function(n,e,t){var r=Br(n,e);return _n(t,function(n,e){null===n?qe(r,e):ze(r,e,n)}),r},du=function(n){return n},mu=function(n){return function(){return Bn.fromTag("tr",n.dom)}},gu=function(f,n,s){var d=function(n,e){var t,r,o,u;t=n.element,r=e,o=t.dom,u=r.dom,Ue(o)&&Ue(u)&&(u.style.cssText=o.style.cssText),Qe(e,"height"),1!==n.colspan&&Qe(e,"width")};return{col:function(n){var e=le(n.element),t=Bn.fromTag(ne(n.element),e.dom);return d(n,t),f(n.element,t),t},colgroup:fu,row:mu(n),cell:function(n){var r,o,u,i,c,e=le(n.element),t=Bn.fromTag(ne(n.element),e.dom),a=s.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),l=0<a.length?(r=n.element,o=t,u=a,ou(r).map(function(n){var e=u.join(","),t=De(n,e,function(n){return Mn(n,r)});return M(t,function(n,e){var t=kr(e);return qe(t,"contenteditable"),Ut(n,t),t},o)}).getOr(o)):t;return Ut(l,Bn.fromTag("br")),d(n,t),i=n.element,c=t,_n(cu,function(e,t){return He(i,t).filter(function(n){return A(e,n)}).each(function(n){return ze(c,t,n)})}),f(n.element,t),t},replace:su,gap:au}},pu=function(n){return{col:lu,colgroup:fu,row:mu(n),cell:au,replace:du,gap:au}},hu=function(n){return B(n,Bn.fromDom)},vu=function(n){return n.nodeName.toLowerCase()},bu=function(n){return Bn.fromDom(n.getBody())},wu=function(n){return n.getBoundingClientRect().width},yu=function(n){return n.getBoundingClientRect().height},Cu=function(e){return function(n){return Mn(n,bu(e))}},xu=function(n){return/^\d+(\.\d+)?$/.test(n)?n+"px":n},Su=function(n){qe(n,"data-mce-style");var e=function(n){return qe(n,"data-mce-style")};E(lt(n),e),E(ft(n),e)},Tu=function(n,e){var t=n.dom.getStyle(e,"width")||n.dom.getAttrib(e,"width");return x.from(t).filter(fn)},Ru=function(n){return/^(\d+(\.\d+)?)%$/.test(n)},Du=function(n){return Bn.fromDom(n.selection.getStart())},Ou=function(n){return Sr(n.get(),b([]),d,H)},Au="data-mce-selected",ku="data-mce-first-selected",Iu="data-mce-last-selected",Bu={selected:Au,selectedSelector:"td[data-mce-selected],th[data-mce-selected]",firstSelected:ku,firstSelectedSelector:"td[data-mce-first-selected],th[data-mce-first-selected]",lastSelected:Iu,lastSelectedSelector:"td[data-mce-last-selected],th[data-mce-last-selected]"},Eu=function(n,e,t){return{element:t,mergable:(u=e,i=Bu,Sr(n.get(),x.none,function(e){return e.length<=1?x.none():yr(u,i.firstSelectedSelector,i.lastSelectedSelector).map(function(n){return{bounds:n,cells:e}})},x.none)),unmergable:(r=function(n,e){return He(n,e).exists(function(n){return 1<parseInt(n,10)})},0<(o=Ou(n)).length&&_(o,function(n){return r(n,"rowspan")||r(n,"colspan")})?x.some(o):x.none()),selection:Ou(n)};var r,o,u,i},Pu=function(s,n,d,m){s.on("BeforeGetContent",function(t){!0===t.selection&&Sr(n.get(),C,function(n){t.preventDefault(),st(n[0]).map(function(n){var e=nu(n,"[data-mce-selected]");return Su(e),[e]}).each(function(n){var e;t.content="text"===t.format?B(n,function(n){return n.dom.innerText}).join(""):(e=s,B(n,function(n){return e.selection.serializer.serialize(n.dom,{})}).join(""))})},C)}),s.on("BeforeSetContent",function(f){!0===f.selection&&!0===f.paste&&x.from(s.dom.getParent(s.selection.getStart(),"th,td")).each(function(n){var l=Bn.fromDom(n);st(l).each(function(e){var n,t,r,o,u,i,c,a=P((n=f.content,(r=(t||document).createElement("div")).innerHTML=n,pe(Bn.fromDom(r))),function(n){return"meta"!==ne(n)});1===a.length&&(c=a[0],"table"===ne(c))&&(f.preventDefault(),o=Bn.fromDom(s.getDoc()),u=pu(o),i={element:l,clipboard:a[0],generators:u},d.pasteCells(e,i).each(function(n){s.selection.setRng(n.rng),s.focus(),m.clear(e)}))})})})},Mu=Cr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),Nu=$({},Mu),ju=function(n,e,r,o,u){var t,i,c=n.slice(0),a=(i=e,0===(t=n).length?Nu.none():1===t.length?Nu.only(0):0===i?Nu.left(0,1):i===t.length-1?Nu.right(i-1,i):0<i&&i<t.length-1?Nu.middle(i-1,i,i+1):Nu.none()),l=b(B(c,b(0)));return a.fold(l,function(n){return o.singleColumnWidth(c[n],r)},function(n,e){return u.calcLeftEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)},function(n,e,t){return u.calcMiddleDeltas(c,n,e,t,r,o.minCellWidth(),o.isRelative)},function(n,e){return u.calcRightEdgeDeltas(c,n,e,r,o.minCellWidth(),o.isRelative)})},Wu=function(n,e,t){for(var r=0,o=n;o<e;o++)r+=t[o]!==undefined?t[o]:0;return r},Lu=function(n,e){return Et.hasColumns(n)?(u=n,i=e,c=Et.justColumns(u),B(c,function(n,e){return{element:n.element,width:i[e],colspan:n.colspan}})):(t=n,r=e,o=Et.justCells(t),B(o,function(n){var e=Wu(n.column,n.column+n.colspan,r);return{element:n.element,width:e,colspan:n.colspan}}));var t,r,o,u,i,c},zu=function(n,e,t){var r=Lu(n,e);E(r,function(n){t.setElementWidth(n.element,n.width)})},_u=function(n,e,t,r,o){var u=Et.fromTable(n),i=o.getCellDelta(e),c=o.getWidths(u,o),a=t===u.grid.columns-1,l=r.clampTableDelta(c,t,i,o.minCellWidth(),a),f=ju(c,t,l,o,r),s=B(f,function(n,e){return n+c[e]});zu(u,s,o),r.resizeTable(o.adjustTableWidth,l,a)},Fu=function(n,t,r,e){var o,u,i,c,a=Et.fromTable(n),l=Uo(a,n,e,Bo,function(n){return n.getOrThunk(ut)}),f=B(l,function(n,e){return r===e?Math.max(t+n,ut()):n}),s=(o=a,u=f,i=Et.justCells(o),B(i,function(n){var e=Wu(n.row,n.row+n.rowspan,u);return{element:n.element,height:e,rowspan:n.rowspan}})),d=(c=f,B(a.all,function(n,e){return{element:n.element,height:c[e]}}));E(d,function(n){To(n.element,n.height)}),E(s,function(n){To(n.element,n.height)});var m=M(f,function(n,e){return n+e},0);To(n,m)},Hu=function(n){return B(n,b(0))},qu=function(n,e,t,r,o){return o(n.slice(0,e)).concat(r).concat(o(n.slice(t)))},Vu=function(i){return function(n,e,t,r){if(i(t)){var o=Math.max(r,n[e]-Math.abs(t)),u=Math.abs(o-n[e]);return 0<=t?u:-u}return t}},Uu=Vu(function(n){return n<0}),Ku=Vu(T),$u=function(){var f=function(n,t,e,r){var o=(100+e)/100,u=Math.max(r,(n[t]+e)/o);return B(n,function(n,e){return(e===t?u:n/o)-n})},c=function(n,e,t,r,o,u){return u?f(n,e,r,o):(a=t,l=Uu(i=n,c=e,r,o),qu(i,c,a+1,[l,0],Hu));var i,c,a,l};return{resizeTable:function(n,e){return n(e)},clampTableDelta:Uu,calcLeftEdgeDeltas:c,calcMiddleDeltas:function(n,e,t,r,o,u,i){return c(n,t,r,o,u,i)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return f(n,t,r,o);var i=Uu(n,t,r,o);return Hu(n.slice(0,t)).concat([i])},calcRedestributedWidths:function(n,e,t,r){if(r){var o=(e+t)/e,u=B(n,function(n){return n/o});return{delta:100*o-100,newSizes:u}}return{delta:t,newSizes:n}}}},Gu=function(){var i=function(n,e,t,r,o){var u=Ku(n,0<=r?t:e,r,o);return qu(n,e,t+1,[u,-u],Hu)};return{resizeTable:function(n,e,t){t&&n(e)},clampTableDelta:function(n,e,t,r,o){if(o){if(0<=t)return t;var u=N(n,function(n,e){return n+e-r},0);return Math.max(-u,t)}return Uu(n,e,t,r)},calcLeftEdgeDeltas:i,calcMiddleDeltas:function(n,e,t,r,o,u){return i(n,t,r,o,u)},calcRightEdgeDeltas:function(n,e,t,r,o,u){if(u)return Hu(n);var i=r/n.length;return B(n,b(i))},calcRedestributedWidths:function(n,e,t,r){return{delta:0,newSizes:n}}}},Xu=function(n,e){var t=x.from(n.dom.documentElement).map(Bn.fromDom).getOr(n);return{parent:b(t),view:b(n),origin:b(Gr(0,0)),isResizable:e}},Yu=function(n,e,t){return{parent:b(e),view:b(n),origin:b(Gr(0,0)),isResizable:t}},Ju=Cr([{invalid:["raw"]},{pixels:["value"]},{percent:["value"]}]),Qu=function(n,e,t){var r=t.substring(0,t.length-n.length),o=parseFloat(r);return r===o.toString()?e(o):Ju.invalid(t)},Zu=$($({},Ju),{from:function(n){return an(n,"%")?Qu("%",Ju.percent,n):an(n,"px")?Qu("px",Ju.pixels,n):Ju.invalid(n)}}),ni=function(n,r,o){return n.fold(function(){return r},function(n){return t=(e=n)/o,B(r,function(n){return Zu.from(n).fold(function(){return n},function(n){return n*t+"px"},function(n){return n/100*e+"px"})});var e,t},function(n){return e=o,B(r,function(n){return Zu.from(n).fold(function(){return n},function(n){return n/e*100+"%"},function(n){return n+"%"})});var e})},ei=function(n,e,t){var r,o,u,i=Zu.from(t),c=_(n,function(n){return"0px"===n})?(r=i,o=n.length,u=r.fold(function(){return b("")},function(n){return b(n/o+"px")},function(){return b(100/o+"%")}),I(o,u)):ni(i,n,e);return ri(c)},ti=function(n,e){return 0===n.length?e:M(n,function(n,e){return Zu.from(e).fold(b(0),d,d)+n},0)},ri=function(n){if(0===n.length)return n;var e,t,r=M(n,function(n,e){var t=Zu.from(e).fold(function(){return{value:e,remainder:0}},function(n){return e=n,t="px",{value:(r=Math.floor(e))+t,remainder:e-r};var e,t,r},function(n){return{value:n+"%",remainder:0}});return{output:[t.value].concat(n.output),remainder:n.remainder+t.remainder}},{output:[],remainder:0}),o=r.output;return o.slice(0,o.length-1).concat([(e=o[o.length-1],t=Math.round(r.remainder),Zu.from(e).fold(b(e),function(n){return n+t+"px"},function(n){return n+t+"%"}))])},oi=Zu.from,ui=function(n){return oi(n).fold(b("px"),b("px"),b("%"))},ii=function(l,n,e,f){var s=Et.fromTable(l),a=s.all,d=Et.justCells(s),m=Et.justColumns(s);n.each(function(n){var r,o,u,i,e=ui(n),t=jr(l),c=Ho(s,l,Lo,qo,f),a=ei(c,t,n);Et.hasColumns(s)?(u=a,i=e,E(m,function(n,e){var t=ti([u[e]],ot());$e(n.element,"width",t+i)})):(r=a,o=e,E(d,function(n){var e=r.slice(n.column,n.colspan+n.column),t=ti(e,ot());$e(n.element,"width",t+o)})),$e(l,"width",n)}),e.each(function(n){var r,e,o,t=ui(n),u=Ur(l),i=Uo(s,l,ao,zo,qo),c=ei(i,u,n);r=c,e=a,o=t,E(d,function(n){var e=r.slice(n.row,n.rowspan+n.row),t=ti(e,ut());$e(n.element,"height",t+o)}),E(e,function(n,e){$e(n.element,"height",r[e])}),$e(l,"height",n)})},ci=function(n){return Oo(n).exists(function(n){return wo.test(n)})},ai=function(n){return Oo(n).exists(function(n){return yo.test(n)})},li=function(n){return Oo(n).isNone()},fi=Po,si=function(n){return Et.fromTable(n).grid},di=function(e){var o=[];return{bind:function(n){if(n===undefined)throw new Error("Event bind error: undefined handler");o.push(n)},unbind:function(e){o=P(o,function(n){return n!==e})},trigger:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r={};E(e,function(n,e){r[n]=t[e]}),E(o,function(n){n(r)})}}},mi=function(n){return{registry:Fn(n,function(n){return{bind:n.bind,unbind:n.unbind}}),trigger:Fn(n,function(n){return n.trigger})}},gi=function(n){return n.slice(0).sort()},pi=function(r,o,u){if(0===o.length)throw new Error("You must specify at least one required field.");var t;return function(e,n){if(!a(n))throw new Error("The "+e+" fields must be an array. Was: "+n+".");E(n,function(n){if(!p(n))throw new Error("The value "+n+" in the "+e+" fields was not a string.")})}("required",o),t=gi(o),j(t,function(n,e){return e<t.length-1&&n===t[e+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+t.join(", ")+"].")}),function(e){var t=Ln(e);_(o,function(n){return A(t,n)})||function(n,e){throw new Error("All required keys ("+gi(n).join(", ")+") were not specified. Specified keys were: "+gi(e).join(", ")+".")}(o,t),r(o,t);var n=P(o,function(n){return!u.validate(e[n],n)});return 0<n.length&&function(n,e){throw new Error("All values need to be of type: "+e+". Keys ("+gi(n).join(", ")+") were not.")}(n,u.label),e}},hi=function(e,n){var t=P(n,function(n){return!A(e,n)});0<t.length&&function(n){throw new Error("Unsupported keys for object: "+gi(n).join(", "))}(t)},vi=function(n){return pi(hi,n,{validate:l,label:"function"})},bi=vi(["compare","extract","mutate","sink"]),wi=vi(["element","start","stop","destroy"]),yi=vi(["forceDrop","drop","move","delayDrop"]),Ci=function(){var u=x.none(),i=mi({move:di(["info"])});return{onEvent:function(n,o){o.extract(n).each(function(n){var e,t,r;(e=o,t=n,r=u.map(function(n){return e.compare(n,t)}),u=x.some(t),r).each(function(n){i.trigger.move(n)})})},reset:function(){u=x.none()},events:i.registry}},xi=function(){var n,e=(n=mi({move:di(["info"])}),{onEvent:C,reset:C,events:n.registry}),t=Ci(),r=e;return{on:function(){r.reset(),r=t},off:function(){r.reset(),r=e},isOn:function(){return r===t},onEvent:function(n,e){r.onEvent(n,e)},events:t.events}},Si=function(e,t,n){var r,o,u,i=!1,c=mi({start:di([]),stop:di([])}),a=xi(),l=function(){d.stop(),a.isOn()&&(a.off(),c.trigger.stop())},f=(r=l,o=200,u=null,{cancel:function(){null!==u&&(clearTimeout(u),u=null)},throttle:function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];null!==u&&clearTimeout(u),u=setTimeout(function(){r.apply(null,n),u=null},o)}});a.events.move.bind(function(n){t.mutate(e,n.info)});var s=function(t){return function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];i&&t.apply(null,n)}},d=t.sink(yi({forceDrop:l,drop:s(l),move:s(function(n){f.cancel(),a.onEvent(n,t)}),delayDrop:s(f.throttle)}),n);return{element:d.element,go:function(n){d.start(n),a.on(),c.trigger.start()},on:function(){i=!0},off:function(){i=!1},destroy:function(){d.destroy()},events:c.registry}},Ti=function(n){var t,r,e=Bn.fromDom(function(n){if(be()&&c(n.target)){var e=Bn.fromDom(n.target);if(oe(e)&&xe(e)&&n.composed&&n.composedPath){var t=n.composedPath();if(t)return V(t)}}return x.from(n.target)}(n).getOr(n.target)),o=function(){return n.stopPropagation()},u=function(){return n.preventDefault()},i=(t=u,r=o,function(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return t(r.apply(null,n))});return{target:e,x:n.clientX,y:n.clientY,stop:o,prevent:u,kill:i,raw:n}},Ri=function(n,e,t,r,o){var u,i,c=(u=t,i=r,function(n){u(n)&&i(Ti(n))});return n.dom.addEventListener(e,c,o),{unbind:S(Di,n,e,c,o)}},Di=function(n,e,t,r){n.dom.removeEventListener(e,t,r)},Oi=T,Ai=function(n,e,t){return Ri(n,e,Oi,t,!1)},ki=Ti,Ii=function(n,e){var t=Fe(n,e);return t===undefined||""===t?[]:t.split(" ")},Bi=function(n){return n.dom.classList!==undefined},Ei=function(n,e){return o=e,u=Ii(t=n,r="class").concat([o]),ze(t,r,u.join(" ")),!0;var t,r,o,u},Pi=function(n,e){Bi(n)?n.dom.classList.add(e):Ei(n,e)},Mi=function(n,e){return Bi(n)&&n.dom.classList.contains(e)},Ni=function(n){var e=n.replace(/\./g,"-");return{resolve:function(n){return e+"-"+n}}},ji=Ni("ephox-dragster").resolve,Wi=bi({compare:function(n,e){return Gr(e.left-n.left,e.top-n.top)},extract:function(n){return x.some(Gr(n.x,n.y))},sink:function(n,e){var t=function(n){var e=$({layerClass:ji("blocker")},n),t=Bn.fromTag("div");ze(t,"role","presentation"),Ge(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Pi(t,ji("blocker")),Pi(t,e.layerClass);return{element:function(){return t},destroy:function(){Yt(t)}}}(e),r=Ai(t.element(),"mousedown",n.forceDrop),o=Ai(t.element(),"mouseup",n.drop),u=Ai(t.element(),"mousemove",n.move),i=Ai(t.element(),"mouseout",n.delayDrop);return wi({element:t.element,start:function(n){Ut(n,t.element())},stop:function(){Yt(t.element())},destroy:function(){t.destroy(),o.unbind(),u.unbind(),i.unbind(),r.unbind()}})},mutate:function(n,e){n.mutate(e.left,e.top)}}),Li=function(n){return"true"===Fe(n,"contenteditable")},zi=Ni("ephox-snooker").resolve,_i=function(){var t,r=mi({drag:di(["xDelta","yDelta","target"])}),o=x.none(),n={mutate:function(n,e){t.trigger.drag(n,e)},events:(t=mi({drag:di(["xDelta","yDelta"])})).registry};n.events.drag.bind(function(e){o.each(function(n){r.trigger.drag(e.xDelta,e.yDelta,n)})});return{assign:function(n){o=x.some(n)},get:function(){return o},mutate:n.mutate,events:r.registry}},Fi=zi("resizer-bar"),Hi=zi("resizer-rows"),qi=zi("resizer-cols"),Vi=function(n){var e=Ae(n.parent(),"."+Fi);E(e,Yt)},Ui=function(t,n,r){var o=t.origin();E(n,function(n){n.each(function(n){var e=r(o,n);Pi(e,Fi),Ut(t.parent(),e)})})},Ki=function(n,e,l,f){Ui(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.col,r=e.x-n.left,o=l.top-n.top,u=7,i=f,c=Bn.fromTag("div"),Ge(c,{position:"absolute",left:r-u/2+"px",top:o+"px",height:i+"px",width:u+"px"}),_e(c,{"data-column":t,role:"presentation"}),c);return Pi(a,qi),a})},$i=function(n,e,l,f){Ui(n,e,function(n,e){var t,r,o,u,i,c,a=(t=e.row,r=l.left-n.left,o=e.y-n.top,u=f,i=7,c=Bn.fromTag("div"),Ge(c,{position:"absolute",left:r+"px",top:o-i/2+"px",height:i+"px",width:u+"px"}),_e(c,{"data-row":t,role:"presentation"}),c);return Pi(a,Hi),a})},Gi=function(n,e,t,r,o){var u,i=Yr(t),c=e.isResizable,a=0<r.length?ao.positions(r,t):[],l=0<a.length?(u=c,z(n.all,function(n,e){return u(n.element)?[e]:[]})):[],f=P(a,function(n,e){return k(l,function(n){return e===n})});$i(e,f,i,Wr(t));var s,d,m,g=0<o.length?fo.positions(o,t):[],p=0<g.length?(d=c,m=[],I((s=n).grid.columns,function(n){Et.getColumnAt(s,n).map(function(n){return n.element}).forall(d)&&m.push(n)}),P(m,function(e){var n=Et.filterItems(s,function(n){return n.column===e});return _(n,function(n){return d(n.element)})})):[],h=P(g,function(n,e){return k(p,function(n){return e===n})});Ki(e,h,i,Kr(t))},Xi=function(n,e){var t,r,o;Vi(n),n.isResizable(e)&&(t=Et.fromTable(e),r=_r(t),o=Lr(t),Gi(t,n,e,r,o))},Yi=function(n,e){var t=Ae(n.parent(),"."+Fi);E(t,e)},Ji=function(n){Yi(n,function(n){$e(n,"display","none")})},Qi=function(n){Yi(n,function(n){$e(n,"display","block")})},Zi=zi("resizer-bar-dragging"),nc=function(o){var t=_i(),r=function(n,e){void 0===e&&(e={});var t=e.mode!==undefined?e.mode:Wi;return Si(n,t,e)}(t,{}),e=x.none(),n=function(n,e){return x.from(Fe(n,e))};t.events.drag.bind(function(t){n(t.target,"data-row").each(function(n){var e=rt(t.target,"top");$e(t.target,"top",e+t.yDelta+"px")}),n(t.target,"data-column").each(function(n){var e=rt(t.target,"left");$e(t.target,"left",e+t.xDelta+"px")})});var u=function(n,e){return rt(n,e)-Ze(n,"data-initial-"+e,0)};r.events.stop.bind(function(){t.get().each(function(r){e.each(function(t){n(r,"data-row").each(function(n){var e=u(r,"top");qe(r,"data-initial-top"),s.trigger.adjustHeight(t,e,parseInt(n,10))}),n(r,"data-column").each(function(n){var e=u(r,"left");qe(r,"data-initial-left"),s.trigger.adjustWidth(t,e,parseInt(n,10))}),Xi(o,t)})})});var i=function(n,e){s.trigger.startAdjust(),t.assign(n),ze(n,"data-initial-"+e,rt(n,e)),Pi(n,Zi),$e(n,"opacity","0.2"),r.go(o.parent())},c=Ai(o.parent(),"mousedown",function(n){var e,t;e=n.target,Mi(e,Hi)&&i(n.target,"top"),t=n.target,Mi(t,qi)&&i(n.target,"left")}),a=function(n){return Mn(n,o.view())},l=function(n){return We(n,"table",a).filter(function(n){return We(n,"[contenteditable]",a).exists(Li)})},f=Ai(o.view(),"mouseover",function(n){l(n.target).fold(function(){Se(n.target)&&Vi(o)},function(n){e=x.some(n),Xi(o,n)})}),s=mi({adjustHeight:di(["table","delta","row"]),adjustWidth:di(["table","delta","column"]),startAdjust:di([])});return{destroy:function(){c.unbind(),f.unbind(),r.destroy(),Vi(o)},refresh:function(n){Xi(o,n)},on:r.on,off:r.off,hideBars:S(Ji,o),showBars:S(Qi,o),events:s.registry}},ec=function(n,o,u){var r=ao,i=fo,e=nc(n),c=mi({beforeResize:di(["table","type"]),afterResize:di(["table","type"]),startDrag:di([])});return e.events.adjustHeight.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"row");var t=r.delta(n.delta,e);Fu(e,t,n.row,r),c.trigger.afterResize(e,"row")}),e.events.startAdjust.bind(function(n){c.trigger.startDrag()}),e.events.adjustWidth.bind(function(n){var e=n.table;c.trigger.beforeResize(e,"col");var t=i.delta(n.delta,e),r=u(e);_u(e,t,n.column,o,r),c.trigger.afterResize(e,"col")}),{on:e.on,off:e.off,hideBars:e.hideBars,showBars:e.showBars,destroy:e.destroy,events:c.registry}},tc=function(n,e){return n.fire("newrow",{node:e})},rc=function(n,e){return n.fire("newcell",{node:e})},oc=function(n,e,t){n.fire("TableModified",$($({},t),{table:e}))},uc={structure:!1,style:!0},ic={structure:!0,style:!1},cc={"border-collapse":"collapse",width:"100%"},ac={border:"1"},lc="preservetable",fc=function(n){return n.getParam("table_sizing_mode","auto")},sc=function(n){return n.getParam("table_responsive_width")},dc=function(n){return n.getParam("table_default_attributes",ac,"object")},mc=function(n){return n.getParam("table_default_styles",function(n){if(wc(n)){var e=n.getBody().offsetWidth;return $($({},cc),{width:e+"px"})}return yc(n)?qn(cc,function(n,e){return"width"!==e}):cc}(n),"object")},gc=function(n){return n.getParam("table_cell_advtab",!0,"boolean")},pc=function(n){return n.getParam("table_row_advtab",!0,"boolean")},hc=function(n){return n.getParam("table_advtab",!0,"boolean")},vc=function(n){return n.getParam("table_style_by_css",!1,"boolean")},bc=function(n){return"relative"===fc(n)||!0===sc(n)},wc=function(n){return"fixed"===fc(n)||!1===sc(n)},yc=function(n){return"responsive"===fc(n)},Cc=function(n){var e="section",t=n.getParam("table_header_type",e,"string");return A(["section","cells","sectionCells","auto"],t)?t:e},xc=function(n){var e=n.getParam("table_column_resizing",lc,"string");return j(["preservetable","resizetable"],function(n){return n===e}).getOr(lc)},Sc=function(n){return"preservetable"===xc(n)},Tc=function(n){return"resizetable"===xc(n)},Rc=function(n){var e=n.getParam("table_clone_elements");return p(e)?x.some(e.split(/[ ,]/)):Array.isArray(e)?x.some(e):x.none()},Dc=function(n,e){if(bc(n)){var t=Tu(n,e.dom).filter(Ru).getOrThunk(function(){return fi(e)});return Qo(t,e)}return wc(n)?Jo(jr(e),e):Yo(e)},Oc=function(n){qe(n,"width")},Ac=function(n,e){var t=jr(n)+"px";ii(n,x.some(t),x.none(),e),Oc(n)},kc=function(n,e){var t,r,o,u=Dc(n,e);r=u,o=Po(t=e),ii(t,x.some(o),x.none(),r),Oc(t)},Ic=function(n,e){var t=Dc(n,e);Ac(e,t)},Bc=function(n){Qe(n,"width");var e=ft(n),t=0<e.length?e:lt(n);E(t,function(n){Qe(n,"width"),Oc(n)}),Oc(n)},Ec=function(){var n=Bn.fromTag("div");return Ge(n,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Ut(Te(Bn.fromDom(document)),n),n},Pc="bar-",Mc=function(n){return"false"!==Fe(n,"data-mce-resize")},Nc=function(f){var s,d,a=x.none(),i=x.none(),c=x.none(),o=function(n){return"TABLE"===n.nodeName},n=function(){return i},m=function(n){return Dc(f,n)},g=function(){return(Sc(f)?Gu:$u)()},u=function(n,e,t){var r,o,u,i,c,a,l=an(e,"e");""===d&&kc(f,n),t!==s&&""!==d?($e(n,"width",d),r=g(),o=m(n),u=Sc(f)||l?si(n).columns-1:0,_u(n,t-s,u,r,o)):Ru(d)&&(i=parseFloat(d.replace("%","")),$e(n,"width",t*i/s+"%")),/^(\d+(\.\d+)?)px$/.test(d)&&(c=n,a=Et.fromTable(c),Et.hasColumns(a)||E(lt(c),function(n){var e=Xe(n,"width");$e(n,"width",e),qe(n,"width")}))};return f.on("init",function(){var n,e,t,r,o,u=(e=Mc,(n=f).inline?Yu(bu(n),Ec(),e):Xu(Bn.fromDom(n.getDoc()),e));c=x.some(u),o=f.getParam("object_resizing",!0),(p(o)?"table"===o:o)&&f.getParam("table_resize_bars",!0,"boolean")&&(t=g(),(r=ec(u,t,m)).on(),r.events.startDrag.bind(function(n){a=x.some(f.selection.getRng())}),r.events.beforeResize.bind(function(n){var e,t,r,o,u,i=n.table.dom;e=f,r=wu(t=i),o=yu(i),u=Pc+n.type,e.fire("ObjectResizeStart",{target:t,width:r,height:o,origin:u})}),r.events.afterResize.bind(function(n){var e,t,r,o,u,i=n.table,c=i.dom;Su(i),a.each(function(n){f.selection.setRng(n),f.focus()}),e=f,r=wu(t=c),o=yu(c),u=Pc+n.type,e.fire("ObjectResized",{target:t,width:r,height:o,origin:u}),f.undoManager.add()}),i=x.some(r))}),f.on("ObjectResizeStart",function(n){var e,t=n.target;o(t)&&(e=Bn.fromDom(t),E(f.dom.select(".mce-clonedresizable"),function(n){f.dom.addClass(n,"mce-"+xc(f)+"-columns")}),!ai(e)&&wc(f)?Ic(f,e):!ci(e)&&bc(f)&&kc(f,e),li(e)&&cn(n.origin,Pc)&&kc(f,e),s=n.width,d=yc(f)?"":Tu(f,t).getOr(""))}),f.on("ObjectResized",function(n){var e,t,r=n.target;o(r)&&(e=Bn.fromDom(r),t=n.origin,cn(t,"corner-")&&u(e,t,n.width),Su(e),oc(f,e.dom,uc))}),f.on("SwitchMode",function(){i.each(function(n){f.mode.isReadOnly()?n.hideBars():n.showBars()})}),{lazyResize:n,lazyWire:function(){return c.getOr(Xu(Bn.fromDom(f.getBody()),Mc))},destroy:function(){i.each(function(n){n.destroy()}),c.each(function(n){var e;e=n,f.inline&&Yt(e.parent())})}}},jc=function(n,e){return{element:n,offset:e}},Wc=function(e,n,t){return e.property().isText(n)&&0===e.property().getText(n).trim().length||e.property().isComment(n)?t(n).bind(function(n){return Wc(e,n,t).orThunk(function(){return x.some(n)})}):x.none()},Lc=function(n,e){return n.property().isText(e)?n.property().getText(e).length:n.property().children(e).length},zc=function(n,e){var t=Wc(n,e,n.query().prevSibling).getOr(e);if(n.property().isText(t))return jc(t,Lc(n,t));var r=n.property().children(t);return 0<r.length?zc(n,r[r.length-1]):jc(t,Lc(n,t))},_c=zc,Fc=rr(),Hc=function(t,r){1===nt(t,"colspan")&&Oo(t).bind(function(n){return go(n,["fixed","relative","empty"])}).each(function(n){var e=n.value/2;Eo(t,e,n.unit),Eo(r,e,n.unit)})},qc=function(n,e,t,r){t===r?qe(n,e):ze(n,e,t)},Vc=function(n,e,t){U(Oe(n,e)).fold(function(){return Vt(n,t)},function(n){return qt(n,t)})},Uc=function(c,n){var t=[],r=[],a=function(n){return B(n,function(n){n.isNew&&t.push(n.element);var e=n.element;return Xt(e),E(n.cells,function(n){n.isNew&&r.push(n.element),qc(n.element,"colspan",n.colspan,1),qc(n.element,"rowspan",n.rowspan,1),Ut(e,n.element)}),e})},l=function(n){return z(n,function(n){return B(n.cells,function(n){return qc(n.element,"span",n.colspan,1),n.element})})},o=function(n,e){var t,r,o,u=(o=Ne(t=c,r=e).getOrThunk(function(){var n=Bn.fromTag(r,le(t).dom);return"thead"===r?Vc(t,"caption,colgroup",n):"colgroup"===r?Vc(t,"caption",n):Ut(t,n),n}),Xt(o),o),i=("colgroup"===e?l:a)(n);Gt(u,i)},e=function(n,e){0<n.length?o(n,e):Ne(c,e).each(Yt)},u=[],i=[],f=[],s=[];return E(n,function(n){switch(n.section){case"thead":u.push(n);break;case"tbody":i.push(n);break;case"tfoot":f.push(n);break;case"colgroup":s.push(n)}}),e(s,"colgroup"),e(u,"thead"),e(i,"tbody"),e(f,"tfoot"),{newRows:t,newCells:r}},Kc=function(n,e){if(0===n.length)return 0;var t=n[0];return W(n,function(n){return!e(t.element,n.element)}).fold(function(){return n.length},function(n){return n})},$c=function(n,e,t,r){var o,u=n[e].cells.slice(t),i=Kc(u,r),c=(o=t,B(n,function(n){return xt(n,o)}).slice(e));return{colspan:i,rowspan:Kc(c,r)}},Gc=function(o,u){var i=B(o,function(n){return B(n.cells,m)});return B(o,function(n,r){return{details:z(n.cells,function(n,e){if(!1!==i[r][e])return[];var t=$c(o,r,e,u);return function(n,e,t,r){for(var o=n;o<n+t;o++)for(var u=e;u<e+r;u++)i[o][u]=!0}(r,e,t.rowspan,t.colspan),[{element:n.element,rowspan:t.rowspan,colspan:t.colspan,isNew:n.isNew}]}),section:n.section}})},Xc=function(n,e,t){var r,o=[];Et.hasColumns(n)&&(r=B(Et.justColumns(n),function(n){return Qn(n.element,t,!1)}),o.push(Zn(r,"colgroup")));for(var u=0;u<n.grid.rows;u++){for(var i=[],c=0;c<n.grid.columns;c++){var a=Et.getAt(n,u,c).map(function(n){return Qn(n.element,t,n.isLocked)}).getOrThunk(function(){return Qn(e.gap(),!0,!1)});i.push(a)}var l=Zn(i,n.all[u].section);o.push(l)}return o},Yc=function(n,r){return B(n,function(n){var e,t=(e=n.details,K(e,function(n){return se(n.element).map(function(n){var e=se(n).isNone();return Qn(n,e,!1)})}).getOrThunk(function(){return Qn(r.row(),!0,!1)}));return{element:t.element,cells:n.details,section:n.section,isNew:t.isNew}})},Jc=function(n,e){var t=Gc(n,Mn);return Yc(t,e)},Qc=function(n,e){return K(n.all,function(n){return j(n.cells,function(n){return Mn(e,n.element)})})},Zc=function(e,n,t){var r=B(n.selection,function(n){return at(n).bind(function(n){return Qc(e,n)}).filter(t)}),o=Er(r);return Pr(0<o.length,o)},na=function(f,e,s,d,m){return function(o,u,n,i,c,a){var l=Et.fromTable(u);return e(l,n).map(function(n){var e=Xc(l,i,!1),t=f(e,n,Mn,m(i)),r=At(t.grid);return{info:n,grid:Jc(t.grid,i),cursor:t.cursor,lockedColumns:r}}).bind(function(n){var e=Uc(u,n.grid),t=x.from(c).getOrThunk(function(){return Yo(u)}),r=x.from(a).getOrThunk(Gu);return s(u,n.grid,n.info,t,r),d(u),Xi(o,u),qe(u,Dt),0<n.lockedColumns.length&&ze(u,Dt,n.lockedColumns.join(",")),x.some({cursor:n.cursor,newRows:e.newRows,newCells:e.newCells})})}},ea=function(e,n){return at(n.element).bind(function(n){return Qc(e,n)})},ta=function(n,e){return Zc(n,e,T).map(function(n){return{cells:n,generators:e.generators,clipboard:e.clipboard}})},ra=function(n,e){return Zc(n,e,T)},oa=function(n,e){return ea(n,e).filter(function(n){return!n.isLocked})},ua=function(n,e){return Zc(n,e,function(n){return!n.isLocked})},ia=function(e,n){return _(n,function(n){return Qc(e,n).exists(function(n){return!n.isLocked})})},ca=function(e,n){return n.mergable.filter(function(n){return ia(e,n.cells)})},aa=function(e,n){return n.unmergable.filter(function(n){return ia(e,n)})},la=function(n,e,t,r){for(var o=Rt(n).rows,u=!0,i=0;i<o.length;i++)for(var c=0;c<Tt(o[0]);c++){var a=o[i],l=xt(a,c),f=t(l.element,e);!0===f&&!1===u?wt(a,c,Qn(r(),!0,l.isLocked)):!0===f&&(u=!1)}return n},fa=function(n,t,i,c){var e,r,o,a=Rt(n).rows;return 0<t&&t<a.length&&(e=a[t-1].cells,o=i,r=N(e,function(n,e){return k(n,function(n){return o(n.element,e.element)})?n:n.concat([e])},[]),E(r,function(n){for(var o=x.none(),u=t;u<a.length;u++)for(var e=0;e<Tt(a[0]);e++)!function(e){var t=a[u],r=xt(t,e);i(r.element,n.element)&&(o.isNone()&&(o=x.some(c())),o.each(function(n){wt(t,e,Qn(n,!0,r.isLocked))}))}(e)})),n},sa=function(t){return{is:function(n){return t===n},isValue:T,isError:m,getOr:b(t),getOrThunk:b(t),getOrDie:b(t),or:function(n){return sa(t)},orThunk:function(n){return sa(t)},fold:function(n,e){return e(t)},map:function(n){return sa(n(t))},mapError:function(n){return sa(t)},each:function(n){n(t)},bind:function(n){return n(t)},exists:function(n){return n(t)},forall:function(n){return n(t)},toOptional:function(){return x.some(t)}}},da=function(t){return{is:m,isValue:m,isError:T,getOr:d,getOrThunk:function(n){return n()},getOrDie:function(){return n=String(t),function(){throw new Error(n)}();var n},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,e){return n(t)},map:function(n){return da(t)},mapError:function(n){return da(n(t))},each:C,bind:function(n){return da(t)},exists:m,forall:T,toOptional:x.none}},ma={value:sa,error:da,fromOption:function(n,e){return n.fold(function(){return da(e)},sa)}},ga=function(n,e){return{rowDelta:0,colDelta:Tt(n[0])-Tt(e[0])}},pa=function(n,e){return{rowDelta:n.length-e.length,colDelta:0}},ha=function(n,e,t,r){var o="colgroup"===e.section?t.col:t.cell;return I(n,function(n){return Qn(o(),!0,r(n))})},va=function(t,n,r,o){return t.concat(I(n,function(){var n=t[t.length-1],e=ha(n.cells.length,n,r,function(n){return $n(o,n.toString())});return yt(n,e)}))},ba=function(n,t,r,o){return B(n,function(n){var e=ha(t,n,r,m);return vt(n,o,e)})},wa=function(n,e,t){var r=e.colDelta<0?ba:d,o=e.rowDelta<0?va:d,u=At(n),i=Tt(n[0]),c=k(u,function(n){return n===i-1}),a=r(n,Math.abs(e.colDelta),t,c?i-1:i),l=At(a);return o(a,Math.abs(e.rowDelta),t,F(l,T))},ya=function(n,e,t,r,o,u){for(var i,c,a,l,f,s=n.row,d=n.column,m=s+t.length,g=d+Tt(t[0])+u.length,p=F(u,T),h=s;h<m;h++)for(var v,b,w,y,C=0,x=d;x<g;x++){p[x]?C++:(a=x,l=void 0,l=S(o,xt((i=e)[c=h],a).element),f=i[c],1<i.length&&1<Tt(f)&&(0<a&&l(St(f,a-1))||a<f.cells.length-1&&l(St(f,a+1))||0<c&&l(St(i[c-1],a))||c<i.length-1&&l(St(i[c+1],a)))&&la(e,St(e[h],x),o,r.cell),v=x-d-C,w=(b=xt(t[h-s],v)).element,y=r.replace(w),wt(e[h],x,Qn(y,!0,b.isLocked)))}return e},Ca=function(e,t,n){return P(n,function(n){return n>=e.column&&n<=Tt(t[0])+e.column})},xa=function(n,u,i,c,a){var e,t,r,o,l,f=At(u),s=(e=n,t=f,r=Tt(u[0]),o=I(r-e.column,function(n){return n+e.column}),l=j(o,function(e){return _(t,function(n){return n!==e})}).getOr(r-1),$($({},e),{column:l})),d=Ca(s,i,f);return function(n,e,t){if(n.row>=e.length||n.column>Tt(e[0]))return ma.error("invalid start address out of table bounds, row: "+n.row+", column: "+n.column);var r=e.slice(n.row),o=r[0].cells.slice(n.column),u=Tt(t[0]),i=t.length;return ma.value({rowDelta:r.length-i,colDelta:o.length-u})}(s,u,i).map(function(n){var e=$($({},n),{colDelta:n.colDelta-d.length}),t=wa(u,e,c),r=At(t),o=Ca(s,i,r);return ya(s,t,i,c,a,o)})},Sa=function(t,n,e,r,o){var u,i,c,a;u=n,i=t,c=o,a=r.cell,0<i&&i<u[0].cells.length&&E(u,function(n){var e=n.cells[i-1],t=n.cells[i];c(t.element,e.element)&&wt(n,i,Qn(a(),!0,t.isLocked))});var l=pa(e,n),f=wa(e,l,r),s=pa(n,f),d=wa(n,s,r);return B(d,function(n,e){return vt(n,t,f[e].cells)})},Ta=function(n,e,t,r,o){fa(e,n,o,r.cell);var u,i,c=At(e),a=ga(e,t),l=$($({},a),{colDelta:a.colDelta-c.length}),f=wa(e,l,r),s=Rt(f),d=s.cols,m=s.rows,g=At(f),p=ga(t,e),h=$($({},p),{colDelta:p.colDelta+g.length}),v=(u=r,i=g,B(t,function(r){return N(i,function(n,e){var t=ha(1,r,u,T)[0];return bt(n,e,t)},r)})),b=wa(v,h,r);return d.concat(m.slice(0,n)).concat(b).concat(m.slice(n,m.length))},Ra=function(n,t,e,r,o){var u=Rt(n),i=u.rows,c=u.cols,a=i.slice(0,t),l=i.slice(t),f=Ct(i[e],function(n,e){return 0<t&&t<i.length&&r(St(i[t-1],e),St(i[t],e))?xt(i[t],e):Qn(o(n.element,r),!0,n.isLocked)});return c.concat(a).concat([f]).concat(l)},Da=function(n,r,o,u,i){return B(n,function(n){var e=0<r&&r<Tt(n)&&u(St(n,r-1),St(n,r)),t=function(n,e,t,r,o,u,i){if("colgroup"!==t&&r)return xt(n,e);var c=xt(n,o);return Qn(i(c.element,u),!0,!1)}(n,r,n.section,e,o,u,i);return bt(n,r,t)})},Oa=function(n,t,r,o){return B(n,function(n){return Ct(n,function(n){return e=n,k(t,function(n){return r(e.element,n.element)})?Qn(o(n.element,r),!0,n.isLocked):n;var e})})},Aa=function(n,e,t,r){return St(n[e],t)!==undefined&&0<e&&r(St(n[e-1],t),St(n[e],t))},ka=function(n,e,t){return 0<e&&t(St(n,e-1),St(n,e))},Ia=function(t,r,o,n){var e=Rt(t).rows,u=z(e,function(n,e){return Aa(t,e,r,o)||ka(n,r,o)?[]:[xt(n,r)]});return Oa(t,u,o,n)},Ba=function(n,t,r,e){var o=Rt(n).rows,u=o[t],i=z(u.cells,function(n,e){return Aa(o,t,e,r)||ka(u,e,r)?[]:[n]});return Oa(n,i,r,e)},Ea=function(n){return N(n,function(n,e){return k(n,function(n){return n.column===e.column})?n:n.concat([e])},[]).sort(function(n,e){return n.column-e.column})},Pa=function(n){return{element:n,colspan:Ze(n,"colspan",1),rowspan:Ze(n,"rowspan",1)}},Ma=function(n){return He(n,"scope").map(function(n){return n.substr(0,3)})},Na=function(e,t){void 0===t&&(t=Pa);var r=Ko(x.none()),o=function(n){return function(n){switch(ne(n.element)){case"col":return e.col(n);default:return e.cell(n)}}(t(n))},u=function(n){var e=o(n);return r.get().isNone()&&r.set(x.some(e)),i=x.some({item:n,replacement:e}),e},i=x.none();return{getOrInit:function(e,t){return i.fold(function(){return u(e)},function(n){return t(e,n.item)?n.replacement:u(e)})},cursor:r.get}},ja=function(c,a){return function(r){var o=Ko(x.none()),u=[],i=function(n){var e={scope:c},t=r.replace(n,a,e);return u.push({item:n,sub:t}),o.get().isNone()&&o.set(x.some(t)),t};return{replaceOrInit:function(e,t){return"col"===ne(e)?e:(r=e,o=t,j(u,function(n){return o(n.item,r)}).fold(function(){return i(e)},function(n){return t(e,n.item)?n.sub:i(e)}));var r,o},cursor:o.get}}},Wa=function(r){var e=Ko(x.none());return{unmerge:function(t){e.get().isNone()&&e.set(x.some(t));var n=Ma(t);return n.each(function(n){return ze(t,"scope",n)}),function(){var e=r.cell({element:t,colspan:1,rowspan:1});return Qe(e,"width"),Qe(t,"width"),n.each(function(n){return ze(e,"scope",n)}),e}},merge:function(r){return Qe(r[0],"width"),function(){var n=Er(B(r,Ma));if(0===n.length)return x.none();var e=n[0],t=["row","col"];return k(n,function(n){return n!==e&&A(t,n)})?x.none():x.from(e)}().fold(function(){return qe(r[0],"scope")},function(n){return ze(r[0],"scope",n+"group")}),b(r[0])},cursor:e.get}},La=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],za=rr(),_a=function(n){return e=n,t=za.property().name(e),A(La,t);var e,t},Fa=function(n){return e=n,t=za.property().name(e),A(["ol","ul"],t);var e,t},Ha=function(n){return e=n,A(["br","img","hr","input"],za.property().name(e));var e},qa=function(n){var e,u=function(n){return"br"===ne(n)},t=function(o){return uu(o).bind(function(t){var r=ge(t).map(function(n){return!!_a(n)||!!Ha(n)&&"img"!==ne(n)}).getOr(!1);return se(t).map(function(n){return!0===r||("li"===ne(e=n)||Pe(e,Fa).isSome())||u(t)||_a(n)&&!Mn(o,n)?[]:[Bn.fromTag("br")];var e})}).getOr([])},r=0===(e=z(n,function(n){var e=pe(n);return _(e,function(n){return u(n)||ue(n)&&0===Zt(n).trim().length})?[]:e.concat(t(n))})).length?[Bn.fromTag("br")]:e;Xt(n[0]),Gt(n[0],r)},Va=function(n){0===lt(n).length&&Yt(n)},Ua=function(n,e){return{grid:n,cursor:e}},Ka=function(n,e,t){var r=Rt(n).rows;return $a(r,e,t).orThunk(function(){return $a(r,0,0)})},$a=function(n,e,t){return x.from(n[e]).bind(function(n){return x.from(n.cells[t]).bind(function(n){return x.from(n.element)})})},Ga=function(n,e,t){var r=Rt(n).rows;return Ua(n,$a(r,e,t))},Xa=function(n){return N(n,function(n,e){return k(n,function(n){return n.row===e.row})?n:n.concat([e])},[]).sort(function(n,e){return n.row-e.row})},Ya=function(n,e,t){var r,o,u=(r=n,o=t.section,gt(r,function(){return o})),i=Et.generate(u);return Xc(i,e,!0)},Ja=function(n,e){var t=P(n,e);return 0===t.length?x.some("td"):t.length===n.length?x.some("th"):x.none()},Qa=function(n,e,t,r){var o=Et.generate(e),u=r.getWidths(o,r);zu(o,u,r)},Za=function(n,e,t,r,o){var u=Et.generate(e),i=r.getWidths(u,r),c=r.pixelWidth(),a=o.calcRedestributedWidths(i,c,t.pixelDelta,r.isRelative),l=a.newSizes,f=a.delta;zu(u,l,r),r.adjustTableWidth(f)},nl=function(n,e){return k(e,function(n){return 0===n.column&&n.isLocked})},el=function(e,n){return k(n,function(n){return n.column+n.colspan>=e.grid.columns&&n.isLocked})},tl=function(n,e){var t=Lr(n),r=Ea(e);return N(r,function(n,e){return n+t[e.column].map(Wr).getOr(0)},0)},rl=function(t){return function(e,n){return ra(e,n).filter(function(n){return!(t?nl:el)(e,n)}).map(function(n){return{details:n,pixelDelta:tl(e,n)}})}},ol=function(t){return function(e,n){return ta(e,n).filter(function(n){return!(t?nl:el)(e,n.cells)})}},ul=na(function(n,e,t,r){var o=e[0].row,u=Xa(e),i=M(u,function(n,e){return{grid:Ra(n.grid,o,e.row+n.delta,t,r.getOrInit),delta:n.delta+1}},{grid:n,delta:0}).grid;return Ga(i,o,e[0].column)},ra,C,C,Na),il=na(function(n,e,t,r){var o=Xa(e),u=o[o.length-1],i=u.row+u.rowspan,c=M(o,function(n,e){return Ra(n,i,e.row,t,r.getOrInit)},n);return Ga(c,i,e[0].column)},ra,C,C,Na),cl=na(function(n,e,t,r){var o=e.details,u=Ea(o),i=u[0].column,c=M(u,function(n,e){return{grid:Da(n.grid,i,e.column+n.delta,t,r.getOrInit),delta:n.delta+1}},{grid:n,delta:0}).grid;return Ga(c,o[0].row,i)},rl(!0),Za,C,Na),al=na(function(n,e,t,r){var o=e.details,u=o[o.length-1],i=u.column+u.colspan,c=Ea(o),a=M(c,function(n,e){return Da(n,i,e.column,t,r.getOrInit)},n);return Ga(a,o[0].row,i)},rl(!1),Za,C,Na),ll=na(function(n,e,t,r){var o,u,i=Ea(e.details),c=(o=n,u=B(i,function(n){return n.column}),z(o,function(n){var e=n.cells,t=M(u,function(n,e){return 0<=e&&e<n.length?n.slice(0,e).concat(n.slice(e+1)):n},e);return 0<t.length?[Zn(t,n.section)]:[]})),a=Ka(c,i[0].row,i[0].column);return Ua(c,a)},function(e,n){return ua(e,n).map(function(n){return{details:n,pixelDelta:-tl(e,n)}})},Za,Va,Na),fl=na(function(n,e,t,r){var o,u,i,c,a,l=Xa(e),f=(o=n,u=l[0].row,i=l[l.length-1].row,c=Rt(o),a=c.rows,c.cols.concat(a.slice(0,u)).concat(a.slice(i+1))),s=Ka(f,e[0].row,e[0].column);return Ua(f,s)},ra,C,Va,Na),sl=(na(function(n,e,t,r){var o=Ia(n,e.column,t,r.replaceOrInit);return Ga(o,e.row,e.column)},oa,C,C,ja("row","th")),na(function(n,e,t,r){var o=Ea(e),u=N(o,function(n,e){return Ia(n,e.column,t,r.replaceOrInit)},n);return Ga(u,e[0].row,e[0].column)},ua,C,C,ja("row","th"))),dl=(na(function(n,e,t,r){var o=Ia(n,e.column,t,r.replaceOrInit);return Ga(o,e.row,e.column)},oa,C,C,ja(null,"td")),na(function(n,e,t,r){var o=Ea(e),u=N(o,function(n,e){return Ia(n,e.column,t,r.replaceOrInit)},n);return Ga(u,e[0].row,e[0].column)},ua,C,C,ja(null,"td"))),ml=(na(function(n,e,t,r){var o=Ba(n,e.row,t,r.replaceOrInit);return Ga(o,e.row,e.column)},ea,C,C,ja("col","th")),na(function(n,e,t,r){var o=Xa(e),u=N(o,function(n,e){return Ba(n,e.row,t,r.replaceOrInit)},n);return Ga(u,e[0].row,e[0].column)},ra,C,C,ja("col","th")),na(function(n,e,t,r){var o=Ba(n,e.row,t,r.replaceOrInit);return Ga(o,e.row,e.column)},ea,C,C,ja(null,"td")),na(function(n,e,t,r){var o=Xa(e),u=N(o,function(n,e){return Ba(n,e.row,t,r.replaceOrInit)},n);return Ga(u,e[0].row,e[0].column)},ra,C,C,ja(null,"td")),na(function(n,e,t,r){var o=e.cells;qa(o);var u=function(n,e,t){var r=Rt(n).rows;if(0===r.length)return n;for(var o=e.startRow;o<=e.finishRow;o++)for(var u=e.startCol;u<=e.finishCol;u++){var i=r[o],c=xt(i,u).isLocked;wt(i,u,Qn(t(),!1,c))}return n}(n,e.bounds,r.merge(o));return Ua(u,x.from(o[0]))},ca,Qa,C,Wa)),gl=na(function(n,e,t,r){var o=M(e,function(n,e){return la(n,e,t,r.unmerge(e))},n);return Ua(o,x.from(e[0]))},aa,Qa,C,Wa),pl=na(function(n,t,e,r){var o,u,i,c=(o=t.clipboard,u=t.generators,i=Et.fromTable(o),Xc(i,u,!0)),a={row:t.row,column:t.column};return xa(a,n,c,t.generators,e).fold(function(){return Ua(n,x.some(t.element))},function(n){var e=Ka(n,t.row,t.column);return Ua(n,e)})},function(e,t){return at(t.element).bind(function(n){return Qc(e,n).map(function(n){return $($({},n),{generators:t.generators,clipboard:t.clipboard})})})},Qa,C,Na),hl=na(function(n,e,t,r){var o=Rt(n).rows,u=e.cells[0].column,i=o[e.cells[0].row],c=Ya(e.clipboard,e.generators,i),a=Sa(u,n,c,e.generators,t),l=Ka(a,e.cells[0].row,e.cells[0].column);return Ua(a,l)},ol(!0),C,C,Na),vl=na(function(n,e,t,r){var o=Rt(n).rows,u=e.cells[e.cells.length-1].column+e.cells[e.cells.length-1].colspan,i=o[e.cells[0].row],c=Ya(e.clipboard,e.generators,i),a=Sa(u,n,c,e.generators,t),l=Ka(a,e.cells[0].row,e.cells[0].column);return Ua(a,l)},ol(!1),C,C,Na),bl=na(function(n,e,t,r){var o=Rt(n).rows,u=e.cells[0].row,i=o[u],c=Ya(e.clipboard,e.generators,i),a=Ta(u,n,c,e.generators,t),l=Ka(a,e.cells[0].row,e.cells[0].column);return Ua(a,l)},ta,C,C,Na),wl=na(function(n,e,t,r){var o=Rt(n).rows,u=e.cells[e.cells.length-1].row+e.cells[e.cells.length-1].rowspan,i=o[e.cells[0].row],c=Ya(e.clipboard,e.generators,i),a=Ta(u,n,c,e.generators,t),l=Ka(a,e.cells[0].row,e.cells[0].column);return Ua(a,l)},ta,C,C,Na),yl=function(n,e){var u=Et.fromTable(n);return ra(u,e).bind(function(n){var e=n[n.length-1],t=n[0].column,r=e.column+e.colspan,o=L(B(u.all,function(n){return P(n.cells,function(n){return n.column>=t&&n.column<r})}));return Ja(o,function(n){return"th"===ne(n.element)})}).getOr("")},Cl=function(n){return vu(n.parentNode)},xl=function(n,e){var t="thead"===Cl(e),r=!k(e.cells,function(n){return"th"!==vu(n)});return t||r?x.some({thead:t,ths:r}):x.none()},Sl=function(n,e){return"thead"===(t=xl(0,e).fold(function(){return Cl(e)},function(n){return"thead"}))?"header":"tfoot"===t?"footer":"body";var t},Tl=function(e,n,t){var r,o,u=e.getParent(n,"table"),i=n.parentNode,c=vu(i);t!==c&&((r=e.select(t,u)[0])||(r=e.create(t),o=u.firstChild,"thead"===t?U(Oe(Bn.fromDom(u),"caption,colgroup")).fold(function(){return u.insertBefore(r,o)},function(n){return e.insertAfter(r,n.dom)}):u.appendChild(r)),"tbody"===t&&"thead"===c&&r.firstChild?r.insertBefore(n,r.firstChild):r.appendChild(n),i.hasChildNodes()||e.remove(i))},Rl=function(n,e,t,r){var o=n.dom,u=function(n,e,t){if(c(t)&&vu(e)!==t){var r=n.dom.rename(e,t);return rc(n,r),r}return e}(n,e,t);return i(r)||o.setAttrib(u,"scope",r),u},Dl=function(e,n,t,r){return E(n,function(n){return Rl(e,n,t,r)})},Ol=function(n,e,t){var r,o,u,i=n.dom;"header"===t?(o="auto"===(r=Cc(n))?(u=st(Bn.fromDom(e.cells[0])).map(dt).getOr([]),K(u,function(n){return xl(0,n.dom)}).map(function(n){return n.thead&&n.ths?"sectionCells":n.thead?"section":"cells"}).getOr("section")):r,Dl(n,e.cells,"section"===o?"td":"th","col"),Tl(i,e,"cells"===o?"tbody":"thead")):(Dl(n,e.cells,"td",null),Tl(i,e,"footer"===t?"tfoot":"tbody"))},Al=function(u){return function(n,e){var t,r=ne(n),o="col"===r||"colgroup"===r?st(t=n).bind(function(n){return wr(n,Bu.firstSelectedSelector)}).fold(function(){return t},function(n){return n[0]}):n;return We(o,u,e)}},kl=Al("th,td"),Il=Al("th,td,caption"),Bl=function(n,e,t){return kl(n,t).map(function(n){return Ou(e)}).getOr([])},El=function(n,t){var e,r,o,u=kl(n),i=u.bind(function(n){return st(n)}).map(dt);return r=i,o=function(e,n){return P(n,function(n){return k(hu(n.dom.cells),function(n){return"1"===Fe(n,t)||Mn(n,e)})})},((e=u).isSome()&&r.isSome()?x.some(o(e.getOrDie(),r.getOrDie())):x.none()).getOr([])},Pl=function(d,n,r){var e=function(n){return"table"===ne(bu(n))},m=Rc(d),t=Tc(d)?C:Hc,o=function(c,a,l,f,s){return function(n,e){Su(n);var t=f(),r=Bn.fromDom(d.getDoc()),o=gu(l,r,m),u=Dc(d,n),i=(Tc(d)?$u:Gu)();return a(n)?c(t,n,e,o,u,i).bind(function(n){return E(n.newRows,function(n){tc(d,n.dom)}),E(n.newCells,function(n){rc(d,n.dom)}),n.cursor.map(function(n){var e=_c(Fc,n),t=d.dom.createRng();return t.setStart(e.element.dom,e.offset),t.setEnd(e.element.dom,e.offset),{rng:t,effect:s}})}):x.none()}},u=o(fl,function(n){return!1===e(d)||1<si(n).rows},C,n,ic),i=o(ll,function(n){return!1===e(d)||1<si(n).columns},C,n,ic),c=o(ul,T,C,n,ic),a=o(il,T,C,n,ic),l=o(cl,T,t,n,ic),f=o(al,T,t,n,ic),s=o(ml,T,C,n,ic),g=o(gl,T,C,n,ic),p=o(hl,T,C,n,ic),h=o(vl,T,C,n,ic),v=o(bl,T,C,n,ic),b=o(wl,T,C,n,ic),w=o(pl,T,C,n,ic),y=function(n,e){return Kn(n,"type").filter(function(n){return A(e,n)})};return{deleteRow:u,deleteColumn:i,insertRowsBefore:c,insertRowsAfter:a,insertColumnsBefore:l,insertColumnsAfter:f,mergeCells:s,unmergeCells:g,pasteColsBefore:p,pasteColsAfter:h,pasteRowsBefore:v,pasteRowsAfter:b,pasteCells:w,setTableCellType:function(t,n){return y(n,["td","th"]).each(function(n){var e=B(Bl(Du(t),r),function(n){return n.dom});Dl(t,e,n,null)})},setTableRowType:function(t,n){return y(n,["header","body","footer"]).each(function(e){B(El(Du(t),Bu.selected),function(n){return Ol(t,n.dom,e)})})},makeColumnsHeader:o(sl,T,C,n,ic),unmakeColumnsHeader:o(dl,T,C,n,ic),getTableRowType:function(n){var e=El(Du(n),Bu.selected);if(0<e.length){var t=B(e,function(n){return Sl(0,n.dom)}),r=A(t,"header"),o=A(t,"footer");if(r||o){var u=A(t,"body");return!r||u||o?r||u||!o?"":"footer":"header"}return"body"}},getTableCellType:function(n){return Ja(Bl(Du(n),r),function(n){return"th"===ne(n)}).getOr("")},getTableColType:yl}},Ml={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},colGroups:!1},Nl=function(n,e,t,r){for(var o=Bn.fromTag("tr"),u=0;u<n;u++){var i=r<e||u<t?Bn.fromTag("th"):Bn.fromTag("td");u<t&&ze(i,"scope","row"),r<e&&ze(i,"scope","col"),Ut(i,Bn.fromTag("br")),Ut(o,i)}return o},jl=function(n){var e=Bn.fromTag("colgroup");return I(n,function(){return Ut(e,Bn.fromTag("col"))}),e},Wl=function(n,e,t,r){return I(n,function(n){return Nl(e,t,r,n)})},Ll=function(n,e){n.selection.select(e.dom,!0),n.selection.collapse(!0)},zl=function(u,i,c,a,l){var o=mc(u),f={styles:o,attributes:dc(u),colGroups:u.getParam("table_use_colgroups",!1,"boolean")};return u.undoManager.ignore(function(){var n=function(n,e,t,r,o,u){void 0===u&&(u=Ml);var i=Bn.fromTag("table"),c="cells"!==o;Ge(i,u.styles),_e(i,u.attributes),u.colGroups&&Ut(i,jl(e));var a,l,f=Math.min(n,t);c&&0<t&&(a=Bn.fromTag("thead"),Ut(i,a),l=Wl(t,e,"sectionCells"===o?f:0,r),Gt(a,l));var s=Bn.fromTag("tbody");Ut(i,s);var d=Wl(c?n-f:n,e,c?0:t,r);return Gt(s,d),i}(c,i,l,a,Cc(u),f);ze(n,"data-mce-id","__mce");var e,t,r,o=(e=n,t=Bn.fromTag("div"),r=Bn.fromDom(e.dom.cloneNode(!0)),Ut(t,r),t.dom.innerHTML);u.insertContent(o),u.addVisual()}),je(bu(u),'table[data-mce-id="__mce"]').map(function(n){var e,t,r;return wc(u)?Ic(u,n):yc(u)?Bc(n):(bc(u)||(e=o.width,p(e)&&-1!==e.indexOf("%")))&&kc(u,n),Su(n),qe(n,"data-mce-id"),t=u,E(Ae(n,"tr"),function(n){tc(t,n.dom),E(Ae(n,"th,td"),function(n){rc(t,n.dom)})}),r=u,je(n,"td,th").each(S(Ll,r)),n.dom}).getOr(null)},_l=function(n,e,t,r,o){void 0===r&&(r={});var u=function(n){return f(n)&&0<n};if(u(e)&&u(t)){var i=r.headerRows||0,c=r.headerColumns||0;return zl(n,t,e,c,i)}return console.error(o),null},Fl=function(n){return function(){return n().fold(function(){return[]},function(n){return B(n,function(n){return n.dom})})}},Hl=function(t){return function(n){var e=0<n.length?x.some(hu(n)):x.none();t(e)}},ql=function(n,e,t,r){return{insertTable:(o=n,function(n,e,t){void 0===t&&(t={});var r=_l(o,e,n,t,"Invalid values for insertTable - rows and columns values are required to insert a table.");return o.undoManager.add(),r}),setClipboardRows:Hl(e.setRows),getClipboardRows:Fl(e.getRows),setClipboardCols:Hl(e.setColumns),getClipboardCols:Fl(e.getColumns),resizeHandler:t,selectionTargets:r};var o},Vl=function(n,e,t){var r=Ze(n,e,1);1===t||r<=1?qe(n,e):ze(n,e,Math.min(t,r))},Ul=function(n,e){var i=Et.fromTable(n);return ua(i,e).map(function(n){var o,u,e=n[n.length-1],t=n[0].column,r=e.column+e.colspan;return G(function(n,t,r){if(Et.hasColumns(n)){var e=P(Et.justColumns(n),function(n){return n.column>=t&&n.column<r}),o=B(e,function(n){var e=Ir(n.element);return Vl(e,"span",r-t),e}),u=Bn.fromTag("colgroup");return Gt(u,o),[u]}return[]}(i,t,r),(o=t,u=r,B(i.all,function(n){var e=P(n.cells,function(n){return n.column>=o&&n.column<u}),t=B(e,function(n){var e=Ir(n.element);return Vl(e,"colspan",u-o),e}),r=Bn.fromTag("tr");return Gt(r,t),r})))})},Kl=function(n,e,u){var i=Et.fromTable(n);return ra(i,e).bind(function(n){var e=Xc(i,u,!1),t=Rt(e).rows.slice(n[0].row,n[n.length-1].row+n[n.length-1].rowspan),r=z(t,function(n){var e=P(n.cells,function(n){return!n.isLocked});return 0<e.length?[$($({},n),{cells:e})]:[]}),o=Jc(r,u);return Pr(0<o.length,o)}).map(function(n){return B(n,function(n){var t=kr(n.element);return E(n.cells,function(n){var e=Ir(n.element);qc(e,"colspan",n.colspan,1),qc(e,"rowspan",n.rowspan,1),Ut(t,e)}),t})})},$l=tinymce.util.Tools.resolve("tinymce.util.Tools"),Gl=function(o,n,u){return function(n,e){for(var t=0;t<e.length;t++){var r=o.getStyle(e[t],u);if(void 0===n&&(n=r),n!==r)return""}return n}(void 0,o.select("td,th",n))},Xl=function(n,e,t){t&&n.formatter.apply("align"+t,{},e)},Yl=function(e,t){$l.each("left center right".split(" "),function(n){e.formatter.remove("align"+n,{},t)})},Jl=function(n,e){return(e||[]).concat(B(n,function(n){var e=n.text||n.title;return Gn(n,"menu")?{text:e,items:Jl(n.menu)}:{text:e,value:n.value}}))},Ql=function(e){return function(n){return cn(n,"rgb")?e.toHex(n):n}},Zl=function(n,e){var t=Bn.fromDom(e);return{borderwidth:Je(t,"border-width").getOr(""),borderstyle:Je(t,"border-style").getOr(""),bordercolor:Je(t,"border-color").map(Ql(n)).getOr(""),backgroundcolor:Je(t,"background-color").map(Ql(n)).getOr("")}},nf=function(n){var o=n[0],e=n.slice(1);return E(e,function(n){E(Ln(o),function(r){_n(n,function(n,e){var t=o[r];""!==t&&r===e&&t!==n&&(o[r]="")})})}),o},ef=function(n){var e=[{name:"borderstyle",type:"listbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===n?[{name:"borderwidth",type:"input",label:"Border width"}].concat(e):e}},tf=function(n,e,t,r){return j(n,function(n){return t.formatter.matchNode(r,e+n)}).getOr("")},rf=S(tf,["left","center","right"],"align"),of=S(tf,["top","middle","bottom"],"valign"),uf=function(n,e){var t,r,o,u,i=mc(n),c=dc(n),a=e?(t=n.dom,{borderstyle:Kn(i,"border-style").getOr(""),bordercolor:Ql(t)(Kn(i,"border-color").getOr("")),backgroundcolor:Ql(t)(Kn(i,"background-color").getOr(""))}):{};return $($($($($($({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),i),c),a),(u=i["border-width"],vc(n)&&u?{border:u}:Kn(c,"border").fold(function(){return{}},function(n){return{border:n}}))),(r=Kn(i,"border-spacing").or(Kn(c,"cellspacing")).fold(function(){return{}},function(n){return{cellspacing:n}}),o=Kn(i,"border-padding").or(Kn(c,"cellpadding")).fold(function(){return{}},function(n){return{cellpadding:n}}),$($({},r),o)))},cf=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"listbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"listbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"listbox",label:"Horizontal align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"listbox",label:"Vertical align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],af=function(n){return cf.concat((0<(e=Jl(n.getParam("table_cell_class_list",[],"array"))).length?x.some({name:"class",type:"listbox",label:"Class",items:e}):x.none()).toArray());var e},lf=function(u){return function(t,r){var o=t.dom;return{setAttrib:function(n,e){u&&!e||o.setAttrib(r,n,e)},setStyle:function(n,e){u&&!e||o.setStyle(r,n,e)},setFormat:function(n,e){u&&!e||(""===e?t.formatter.remove(n,{value:null},r,!0):t.formatter.apply(n,{value:e},r))}}}},ff={normal:lf(!1),ifTruthy:lf(!0)},sf=function(o){return st(o[0]).map(function(n){var e=Et.fromTable(n),t=Et.justCells(e),r=P(t,function(e){return k(o,function(n){return Mn(e.element,n)})});return B(r,function(n){return{element:n.element.dom,column:Et.getColumnAt(e,n.column).map(function(n){return n.element.dom})}})})},df=function(g,n,t,p){var e,r,h=1===n.length,o=qn(p,function(n,e){return t[e]!==n});0<Un(o)&&1<=n.length&&(e=st(n[0]),sf(n).each(function(n){E(n,function(n){var e,t,r,o,u,i,c,a,l,f,s=Rl(g,n.element,p.celltype),d=h?ff.normal(g,s):ff.ifTruthy(g,s),m=n.column.map(function(n){return h?ff.normal(g,n):ff.ifTruthy(g,n)}).getOr(d);t=m,r=p,(e=d).setAttrib("scope",r.scope),e.setAttrib("class",r["class"]),e.setStyle("height",xu(r.height)),t.setStyle("width",xu(r.width)),gc(g)&&(u=p,(o=d).setFormat("tablecellbackgroundcolor",u.backgroundcolor),o.setFormat("tablecellbordercolor",u.bordercolor),o.setFormat("tablecellborderstyle",u.borderstyle),o.setFormat("tablecellborderwidth",xu(u.borderwidth))),h&&(Yl(g,s),i=g,c=s,$l.each("top middle bottom".split(" "),function(n){i.formatter.remove("valign"+n,{},c)})),p.halign&&Xl(g,s,p.halign),p.valign&&(a=g,l=s,(f=p.valign)&&a.formatter.apply("valign"+f,{},l))})}),r=0<Un(qn(o,function(n,e){return"scope"!==e&&"celltype"!==e})),e.each(function(n){return oc(g,n.dom,{structure:$n(o,"celltype"),style:r})}))},mf=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact(function(){df(n,e,t,o),n.focus()})},gf=function(a,n){var e=sf(n).map(function(n){return B(n,function(n){return e=a,t=n.element,r=gc(a),o=n.column,u=e.dom,i=o.getOr(t),$({width:(c=function(n,e){return u.getStyle(n,e)||u.getAttrib(n,e)})(i,"width"),height:c(t,"height"),scope:u.getAttrib(t,"scope"),celltype:vu(t),"class":u.getAttrib(t,"class",""),halign:rf(e,t),valign:of(e,t)},r?Zl(u,t):{});var e,t,r,o,u,i,c})});return nf(e.getOrDie())},pf=function(n,e){var t,r,o,u=Bl(Du(n),e);0!==u.length&&(t=gf(n,u),r={type:"tabpanel",tabs:[{title:"General",name:"general",items:af(n)},ef("cell")]},o={type:"panel",items:[{type:"grid",columns:2,items:af(n)}]},n.windowManager.open({title:"Cell Properties",size:"normal",body:gc(n)?r:o,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:t,onSubmit:S(mf,n,u,t)}))},hf=[{type:"listbox",name:"type",label:"Row type",items:[{text:"Header",value:"header"},{text:"Body",value:"body"},{text:"Footer",value:"footer"}]},{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],vf=function(n){return hf.concat((0<(e=Jl(n.getParam("table_row_class_list",[],"array"))).length?x.some({name:"class",type:"listbox",label:"Class",items:e}):x.none()).toArray());var e},bf=function(i,n,c,a){var e,t,l=1===n.length,r=qn(a,function(n,e){return c[e]!==n});0<Un(r)&&(E(n,function(n){a.type!==vu(n.parentNode)&&Ol(i,n,a.type);var e,t,r,o,u=l?ff.normal(i,n):ff.ifTruthy(i,n);t=a,(e=u).setAttrib("class",t["class"]),e.setStyle("height",xu(t.height)),pc(i)&&(o=a,(r=u).setStyle("background-color",o.backgroundcolor),r.setStyle("border-color",o.bordercolor),r.setStyle("border-style",o.borderstyle)),a.align!==c.align&&(Yl(i,n),Xl(i,n,a.align))}),e=$n(r,"type"),t=!e||1<Un(r),st(Bn.fromDom(n[0])).each(function(n){return oc(i,n.dom,{structure:e,style:t})}))},wf=function(n,e,t,r){var o=r.getData();r.close(),n.undoManager.transact(function(){bf(n,e,t,o),n.focus()})},yf=function(u){var n,e,t,r,o=El(Du(u),Bu.selected);0!==o.length&&(n=B(o,function(n){return e=u,t=n.dom,r=pc(u),o=e.dom,$({height:o.getStyle(t,"height")||o.getAttrib(t,"height"),"class":o.getAttrib(t,"class",""),type:Sl(0,t),align:rf(e,t)},r?Zl(o,t):{});var e,t,r,o}),e=nf(n),t={type:"tabpanel",tabs:[{title:"General",name:"general",items:vf(u)},ef("row")]},r={type:"panel",items:[{type:"grid",columns:2,items:vf(u)}]},u.windowManager.open({title:"Row Properties",size:"normal",body:pc(u)?t:r,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:e,onSubmit:S(wf,u,B(o,function(n){return n.dom}),e)}))},Cf=tinymce.util.Tools.resolve("tinymce.Env"),xf=function(n,e,t,r){if("TD"===e.tagName||"TH"===e.tagName)p(t)?n.setStyle(e,t,r):n.setStyle(e,t);else if(e.children)for(var o=0;o<e.children.length;o++)xf(n,e.children[o],t,r)},Sf=function(o,u,t,n){var i,c=o.dom,a=n.getData(),l=qn(a,function(n,e){return t[e]!==n});n.close(),""===a["class"]&&delete a["class"],o.undoManager.transact(function(){var n,e,t,r;u||(n=parseInt(a.cols,10)||1,e=parseInt(a.rows,10)||1,u=zl(o,n,e,0,0)),0<Un(l)&&(function(n,e,t){var r,o=n.dom,u={},i={};if(u["class"]=t["class"],i.height=xu(t.height),o.getAttrib(e,"width")&&!vc(n)?u.width=(r=t.width)?r.replace(/px$/,""):"":i.width=xu(t.width),vc(n)?(i["border-width"]=xu(t.border),i["border-spacing"]=xu(t.cellspacing)):(u.border=t.border,u.cellpadding=t.cellpadding,u.cellspacing=t.cellspacing),vc(n)&&e.children)for(var c=0;c<e.children.length;c++)xf(o,e.children[c],{"border-width":xu(t.border),padding:xu(t.cellpadding)}),hc(n)&&xf(o,e.children[c],{"border-color":t.bordercolor});hc(n)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),u.style=o.serializeStyle($($({},mc(n)),i)),o.setAttribs(e,$($({},dc(n)),u))}(o,u,a),(i=c.select("caption",u)[0])&&!a.caption&&c.remove(i),!i&&a.caption&&((i=c.create("caption")).innerHTML=Cf.ie?"\xa0":'<br data-mce-bogus="1"/>',u.insertBefore(i,u.firstChild)),""===a.align?Yl(o,u):Xl(o,u,a.align)),o.focus(),o.addVisual(),0<Un(l)&&(r=!(t=$n(l,"caption"))||1<Un(l),oc(o,u,{structure:t,style:r}))})},Tf=function(n,e){var t,r,o,u,i,c,a,l,f=n.dom,s=uf(n,hc(n));!1===e?(t=f.getParent(n.selection.getStart(),"table",n.getBody()))?(o=t,u=hc(r=n),l=r.dom,s=$({width:l.getStyle(o,"width")||l.getAttrib(o,"width"),height:l.getStyle(o,"height")||l.getAttrib(o,"height"),cellspacing:l.getStyle(o,"border-spacing")||l.getAttrib(o,"cellspacing"),cellpadding:l.getAttrib(o,"cellpadding")||Gl(r.dom,o,"padding"),border:(i=l,c=o,a=Je(Bn.fromDom(c),"border-width"),vc(r)&&a.isSome()?a.getOr(""):i.getAttrib(c,"border")||Gl(r.dom,c,"border-width")||Gl(r.dom,c,"border")),caption:!!l.select("caption",o)[0],"class":l.getAttrib(o,"class",""),align:rf(r,o)},u?Zl(l,o):{})):hc(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""):(s.cols="1",s.rows="1",hc(n)&&(s.borderstyle="",s.bordercolor="",s.backgroundcolor=""));var d=Jl(n.getParam("table_class_list",[],"array"));0<d.length&&s["class"]&&(s["class"]=s["class"].replace(/\s*mce\-item\-table\s*/g,""));var m,g,p,h,v={type:"grid",columns:2,items:(m=d,g=e?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],p=n.getParam("table_appearance_options",!0,"boolean")?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],h=0<m.length?[{type:"listbox",name:"class",label:"Class",items:m}]:[],g.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(p).concat([{type:"listbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(h))},b=hc(n)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[v]},ef("table")]}:{type:"panel",items:[v]};n.windowManager.open({title:"Table Properties",size:"normal",body:b,onSubmit:S(Sf,n,t,s),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:s})},Rf=function(n){return Il(Du(n),Cu(n))},Df=function(n){return kl(Du(n),Cu(n))},Of=function(c,r,t,a,e){var u=Cu(c),l=function(n){return st(n,u)},f=function(e){return function(n){c.selection.setRng(n.rng),c.focus(),t.clear(e),Su(e),oc(c,e.dom,n.effect)}},o=function(r){return Df(c).each(function(t){l(t).each(function(n){var e=Eu(a,n,t);r(n,e).each(f(n))})})},i=function(){return Df(c).map(function(r){return l(r).bind(function(n){var e=Eu(a,n,r),t=gu(C,Bn.fromDom(c.getDoc()),x.none());return Kl(n,e,t)})})},s=function(){return Df(c).map(function(t){return l(t).bind(function(n){var e=Eu(a,n,t);return Ul(n,e)})})},d=function(i,n){return n().each(function(n){var u=B(n,Ir);Df(c).each(function(n){return l(n).each(function(n){var e,t,r=pu(Bn.fromDom(c.getDoc())),o=(e=u,t=r,{selection:Ou(a),clipboard:e,generators:t});i(n,o).each(f(n))})})})};_n({mceTableSplitCells:function(){return o(r.unmergeCells)},mceTableMergeCells:function(){return o(r.mergeCells)},mceTableInsertRowBefore:function(){return o(r.insertRowsBefore)},mceTableInsertRowAfter:function(){return o(r.insertRowsAfter)},mceTableInsertColBefore:function(){return o(r.insertColumnsBefore)},mceTableInsertColAfter:function(){return o(r.insertColumnsAfter)},mceTableDeleteCol:function(){return o(r.deleteColumn)},mceTableDeleteRow:function(){return o(r.deleteRow)},mceTableCutCol:function(n){return s().each(function(n){e.setColumns(n),o(r.deleteColumn)})},mceTableCutRow:function(n){return i().each(function(n){e.setRows(n),o(r.deleteRow)})},mceTableCopyCol:function(n){return s().each(function(n){return e.setColumns(n)})},mceTableCopyRow:function(n){return i().each(function(n){return e.setRows(n)})},mceTablePasteColBefore:function(n){return d(r.pasteColsBefore,e.getColumns)},mceTablePasteColAfter:function(n){return d(r.pasteColsAfter,e.getColumns)},mceTablePasteRowBefore:function(n){return d(r.pasteRowsBefore,e.getRows)},mceTablePasteRowAfter:function(n){return d(r.pasteRowsAfter,e.getRows)},mceTableDelete:function(){return Rf(c).each(function(n){st(n,u).filter(h(u)).each(function(n){var e,t=Bn.fromText("");qt(n,t),Yt(n),c.dom.isEmpty(c.getBody())?(c.setContent(""),c.selection.setCursorLocation()):((e=c.dom.createRng()).setStart(t.dom,0),e.setEnd(t.dom,0),c.selection.setRng(e),c.nodeChanged())})})},mceTableSizingMode:function(n,e){return t=e,Rf(c).each(function(n){yc(c)||wc(c)||bc(c)||st(n,u).each(function(n){"relative"!==t||ci(n)?"fixed"!==t||ai(n)?"responsive"!==t||li(n)||Bc(n):Ic(c,n):kc(c,n),Su(n),oc(c,n.dom,ic)})});var t}},function(n,e){return c.addCommand(e,n)});var m=function(e,n){n.each(function(n){oc(e,n.dom,ic)})};_n({mceTableCellType:function(n,e){var t=st(Du(c),u);r.setTableCellType(c,e),m(c,t)},mceTableRowType:function(n,e){var t=st(Du(c),u);r.setTableRowType(c,e),m(c,t)}},function(n,e){return c.addCommand(e,n)}),c.addCommand("mceTableColType",function(n,e){return Kn(e,"type").each(function(n){return o("th"===n?r.makeColumnsHeader:r.unmakeColumnsHeader)})}),_n({mceTableProps:S(Tf,c,!1),mceTableRowProps:S(yf,c),mceTableCellProps:S(pf,c,a)},function(n,e){return c.addCommand(e,function(){return n()})}),c.addCommand("mceInsertTable",function(n,e){g(e)&&0<Ln(e).length?_l(c,e.rows,e.columns,e.options,"Invalid values for mceInsertTable - rows and columns values are required to insert a table."):Tf(c,!0)}),c.addCommand("mceTableApplyCellStyle",function(n,e){var r,t,o=function(n){return"tablecell"+n.toLowerCase().replace("-","")};!g(e)||0!==(r=Bl(Du(c),a,u)).length&&(function(n){for(var e in n)if(zn.call(n,e))return!1;return!0}(t=qn(e,function(n,e){return c.formatter.has(o(e))&&p(n)}))||(_n(t,function(e,t){E(r,function(n){ff.normal(c,n.dom).setFormat(o(t),e)})}),l(r[0]).each(function(n){return oc(c,n.dom,uc)})))})},Af=function(t,r,o){var n=Cu(t);_n({mceTableRowType:function(){return r.getTableRowType(t)},mceTableCellType:function(){return r.getTableCellType(t)},mceTableColType:function(){return kl(Du(t)).bind(function(t){return st(t,n).map(function(n){var e=Eu(o,n,t);return r.getTableColType(n,e)})}).getOr("")}},function(n,e){return t.addQueryValueHandler(e,n)})},kf={tablecellbackgroundcolor:{selector:"td,th",styles:{backgroundColor:"%value"},remove_similar:!0},tablecellbordercolor:{selector:"td,th",styles:{borderColor:"%value"},remove_similar:!0},tablecellborderstyle:{selector:"td,th",styles:{borderStyle:"%value"},remove_similar:!0},tablecellborderwidth:{selector:"td,th",styles:{borderWidth:"%value"},remove_similar:!0}},If=Cr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Bf=$($({},If),{none:function(n){return void 0===n&&(n=undefined),If.none(n)}}),Ef=function(t,n){return st(t,n).bind(function(n){var e=lt(n);return W(e,function(n){return Mn(t,n)}).map(function(n){return{index:n,all:e}})})},Pf=function(n,e,t,r){return{start:n,soffset:e,finish:t,foffset:r}},Mf=Cr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Nf={before:Mf.before,on:Mf.on,after:Mf.after,cata:function(n,e,t,r){return n.fold(e,t,r)},getStart:function(n){return n.fold(d,d,d)}},jf=Cr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Wf={domRange:jf.domRange,relative:jf.relative,exact:jf.exact,exactFromRange:function(n){return jf.exact(n.start,n.soffset,n.finish,n.foffset)},getWin:function(n){var e,t=n.match({domRange:function(n){return Bn.fromDom(n.startContainer)},relative:function(n,e){return Nf.getStart(n)},exact:function(n,e,t,r){return n}});return e=t,Bn.fromDom(fe(e).dom.defaultView)},range:Pf},Lf=function(n,e){return n.selectNodeContents(e.dom)},zf=function(n,e,t){var r,o,u=n.document.createRange();return r=u,e.fold(function(n){r.setStartBefore(n.dom)},function(n,e){r.setStart(n.dom,e)},function(n){r.setStartAfter(n.dom)}),o=u,t.fold(function(n){o.setEndBefore(n.dom)},function(n,e){o.setEnd(n.dom,e)},function(n){o.setEndAfter(n.dom)}),u},_f=function(n,e,t,r,o){var u=n.document.createRange();return u.setStart(e.dom,t),u.setEnd(r.dom,o),u},Ff=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom,width:n.width,height:n.height}},Hf=Cr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),qf=function(n,e,t){return e(Bn.fromDom(t.startContainer),t.startOffset,Bn.fromDom(t.endContainer),t.endOffset)},Vf=function(n,e){var o,t,r,u=(o=n,e.match({domRange:function(n){return{ltr:b(n),rtl:x.none}},relative:function(n,e){return{ltr:Y(function(){return zf(o,n,e)}),rtl:Y(function(){return x.some(zf(o,e,n))})}},exact:function(n,e,t,r){return{ltr:Y(function(){return _f(o,n,e,t,r)}),rtl:Y(function(){return x.some(_f(o,t,r,n,e))})}}}));return(r=(t=u).ltr()).collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Hf.rtl(Bn.fromDom(n.endContainer),n.endOffset,Bn.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return qf(0,Hf.ltr,r)}):qf(0,Hf.ltr,r)},Uf=function(u,n){return Vf(u,n).match({ltr:function(n,e,t,r){var o=u.document.createRange();return o.setStart(n.dom,e),o.setEnd(t.dom,r),o},rtl:function(n,e,t,r){var o=u.document.createRange();return o.setStart(t.dom,r),o.setEnd(n.dom,e),o}})},Kf=(Hf.ltr,Hf.rtl,function(n,e,t){return e>=n.left&&e<=n.right&&t>=n.top&&t<=n.bottom}),$f=function(t,r,n,e,o){var u=function(n){var e=t.dom.createRange();return e.setStart(r.dom,n),e.collapse(!0),e},i=Zt(r).length,c=function(n,e,t,r,o){if(0===o)return 0;if(e===r)return o-1;for(var u=r,i=1;i<o;i++){var c=n(i),a=Math.abs(e-c.left);if(t<=c.bottom){if(t<c.top||u<a)return i-1;u=a}}return 0}(function(n){return u(n).getBoundingClientRect()},n,e,o.right,i);return u(c)},Gf=function(n,e,t,r){return ue(e)?function(e,t,r,o){var n=e.dom.createRange();n.selectNode(t.dom);var u=n.getClientRects();return K(u,function(n){return Kf(n,r,o)?x.some(n):x.none()}).map(function(n){return $f(e,t,r,o,n)})}(n,e,t,r):(u=e,i=t,c=r,a=(o=n).dom.createRange(),l=pe(u),K(l,function(n){return a.selectNode(n.dom),Kf(a.getBoundingClientRect(),i,c)?Gf(o,n,i,c):x.none()}));var o,u,i,c,a,l},Xf=function(n,e){return e-n.left<n.right-e},Yf=function(n,e,t){var r=n.dom.createRange();return r.selectNode(e.dom),r.collapse(t),r},Jf=function(e,n,t){var r=e.dom.createRange();r.selectNode(n.dom);var o=r.getBoundingClientRect(),u=Xf(o,t);return(!0===u?ou:uu)(n).map(function(n){return Yf(e,n,u)})},Qf=function(n,e,t){var r=e.dom.getBoundingClientRect(),o=Xf(r,t);return x.some(Yf(n,e,o))},Zf=function(t,n,e){return x.from(t.dom.caretPositionFromPoint(n,e)).bind(function(n){if(null===n.offsetNode)return x.none();var e=t.dom.createRange();return e.setStart(n.offsetNode,n.offset),e.collapse(),x.some(e)})},ns=function(n,e,t){return x.from(n.dom.caretRangeFromPoint(e,t))},es=function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect();return function(n,e,t,r){var o=n.dom.createRange();o.selectNode(e.dom);var u=o.getBoundingClientRect(),i=Math.max(u.left,Math.min(u.right,t)),c=Math.max(u.top,Math.min(u.bottom,r));return Gf(n,e,i,c)}(n,e,Math.max(u.left,Math.min(u.right,t)),Math.max(u.top,Math.min(u.bottom,r)))},ts=function(o,u,e){return Bn.fromPoint(o,u,e).bind(function(r){var n=function(){return n=o,t=u,(0===pe(e=r).length?Qf:Jf)(n,e,t);var n,e,t};return 0===pe(r).length?n():es(o,r,u,e).orThunk(n)})},rs=document.caretPositionFromPoint?Zf:document.caretRangeFromPoint?ns:ts,os=function(n,e){var t=ne(n);return"input"===t?Nf.after(n):A(["br","img"],t)?0===e?Nf.before(n):Nf.after(n):Nf.on(n,e)},us=function(n,e){var t=n.fold(Nf.before,os,Nf.after),r=e.fold(Nf.before,os,Nf.after);return Wf.relative(t,r)},is=function(n,e,t,r){var o=os(n,e),u=os(t,r);return Wf.relative(o,u)},cs=function(n,e,t,r){var o,u,i,c,a,l=(u=e,i=t,c=r,(a=le(o=n).dom.createRange()).setStart(o.dom,u),a.setEnd(i.dom,c),a),f=Mn(n,t)&&e===r;return l.collapsed&&!f},as=function(n){return x.from(n.getSelection())},ls=function(n,e){as(n).each(function(n){n.removeAllRanges(),n.addRange(e)})},fs=function(n,e,t,r,o){var u=_f(n,e,t,r,o);ls(n,u)},ss=function(s,n){return Vf(s,n).match({ltr:function(n,e,t,r){fs(s,n,e,t,r)},rtl:function(c,a,l,f){as(s).each(function(n){if(n.setBaseAndExtent)n.setBaseAndExtent(c.dom,a,l.dom,f);else if(n.extend)try{t=c,r=a,o=l,u=f,(e=n).collapse(t.dom,r),e.extend(o.dom,u)}catch(i){fs(s,l,f,c,a)}else fs(s,l,f,c,a);var e,t,r,o,u})}})},ds=function(n,e,t,r,o){var u=is(e,t,r,o);ss(n,u)},ms=function(n,e,t){var r=us(e,t);ss(n,r)},gs=function(n){var o=Wf.getWin(n).dom,e=function(n,e,t,r){return _f(o,n,e,t,r)},t=n.match({domRange:function(n){var e=Bn.fromDom(n.startContainer),t=Bn.fromDom(n.endContainer);return is(e,n.startOffset,t,n.endOffset)},relative:us,exact:is});return Vf(o,t).match({ltr:e,rtl:e})},ps=function(n){if(0<n.rangeCount){var e=n.getRangeAt(0),t=n.getRangeAt(n.rangeCount-1);return x.some(Pf(Bn.fromDom(e.startContainer),e.startOffset,Bn.fromDom(t.endContainer),t.endOffset))}return x.none()},hs=function(n){if(null===n.anchorNode||null===n.focusNode)return ps(n);var e=Bn.fromDom(n.anchorNode),t=Bn.fromDom(n.focusNode);return cs(e,n.anchorOffset,t,n.focusOffset)?x.some(Pf(e,n.anchorOffset,t,n.focusOffset)):ps(n)},vs=function(n,e){var t,r,o=(t=e,r=n.document.createRange(),Lf(r,t),r);ls(n,o)},bs=function(n){return as(n).filter(function(n){return 0<n.rangeCount}).bind(hs).map(function(n){return Wf.exact(n.start,n.soffset,n.finish,n.foffset)})},ws=function(n,e){var t,r,o,u=Uf(n,e);return r=(t=u).getClientRects(),0<(o=0<r.length?r[0]:t.getBoundingClientRect()).width||0<o.height?x.some(o).map(Ff):x.none()},ys=function(n,e,t){return r=n,o=e,u=t,i=Bn.fromDom(r.document),rs(i,o,u).map(function(n){return Pf(Bn.fromDom(n.startContainer),n.startOffset,Bn.fromDom(n.endContainer),n.endOffset)});var r,o,u,i},Cs=tinymce.util.Tools.resolve("tinymce.util.VK"),xs=function(n,e,t){return Rs(n,e,Ef(r=t,o).fold(function(){return Bf.none(r)},function(n){return n.index+1<n.all.length?Bf.middle(r,n.all[n.index+1]):Bf.last(r)}));var r,o},Ss=function(n,e,t){return Rs(n,e,Ef(r=t,o).fold(function(){return Bf.none()},function(n){return 0<=n.index-1?Bf.middle(r,n.all[n.index-1]):Bf.first(r)}));var r,o},Ts=function(n,e){var t=Wf.exact(e,0,e,0);return gs(t)},Rs=function(t,e,n){return n.fold(x.none,x.none,function(n,e){return ou(e).map(function(n){return Ts(0,n)})},function(n){return st(n,e).bind(function(n){return t.execCommand("mceTableInsertRowAfter"),e=Ae(n,"tr"),U(e).bind(function(n){return je(n,"td,th").map(function(n){return Ts(0,n)})});var e})})},Ds=["table","li","dl"],Os=function(n,e){return{selection:n,kill:e}},As=function(n,e,t,r){return{start:Nf.on(n,e),finish:Nf.on(t,r)}},ks=function(n,e){var t=Uf(n,e);return Pf(Bn.fromDom(t.startContainer),t.startOffset,Bn.fromDom(t.endContainer),t.endOffset)},Is=As,Bs=function(t,n,r,e,o){return Mn(r,e)?x.none():pr(r,e,n).bind(function(n){var e=n.boxes.getOr([]);return 0<e.length?(o(t,e,n.start,n.finish),x.some(Os(x.some(Is(r,0,r,eu(r))),!0))):x.none()})},Es=function(n,e){return{item:n,mode:e}},Ps=function(n,e,t,r){return void 0===r&&(r=Ms),n.property().parent(e).map(function(n){return Es(n,r)})},Ms=function(n,e,t,r){return void 0===r&&(r=Ns),t.sibling(n,e).map(function(n){return Es(n,r)})},Ns=function(n,e,t,r){void 0===r&&(r=Ns);var o=n.property().children(e);return t.first(o).map(function(n){return Es(n,r)})},js=[{current:Ps,next:Ms,fallback:x.none()},{current:Ms,next:Ns,fallback:x.some(Ps)},{current:Ns,next:Ns,fallback:x.some(Ms)}],Ws=function(e,t,r,o,n){return void 0===n&&(n=js),j(n,function(n){return n.current===r}).bind(function(n){return n.current(e,t,o,n.next).orThunk(function(){return n.fallback.bind(function(n){return Ws(e,t,n,o)})})})},Ls=function(){return{sibling:function(n,e){return n.query().prevSibling(e)},first:function(n){return 0<n.length?x.some(n[n.length-1]):x.none()}}},zs=function(){return{sibling:function(n,e){return n.query().nextSibling(e)},first:function(n){return 0<n.length?x.some(n[0]):x.none()}}},_s=function(e,n,t,r,o,u){return Ws(e,n,r,o).bind(function(n){return u(n.item)?x.none():t(n.item)?x.some(n.item):_s(e,n.item,t,n.mode,o,u)})},Fs=function(e){return function(n){return 0===e.property().children(n).length}},Hs=function(n,e,t,r){return _s(n,e,t,Ms,Ls(),r)},qs=function(n,e,t,r){return _s(n,e,t,Ms,zs(),r)},Vs=rr(),Us=function(n,e){return Hs(t=Vs,n,Fs(t),e);var t},Ks=function(n,e){return qs(t=Vs,n,Fs(t),e);var t},$s=Cr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}]),Gs=function(n){return We(n,"tr")},Xs=$($({},$s),{verify:function(c,n,e,t,r,a,o){return We(t,"td,th",o).bind(function(i){return We(n,"td,th",o).map(function(u){return Mn(i,u)?Mn(t,i)&&eu(i)===r?a(u):$s.none("in same cell"):mr(Gs,[i,u]).fold(function(){return e=u,t=i,r=(n=c).getRect(e),(o=n.getRect(t)).right>r.left&&o.left<r.right?$s.success():a(u);var n,e,t,r,o},function(n){return a(u)})})}).getOr($s.none("default"))},cata:function(n,e,t,r,o){return n.fold(e,t,r,o)}}),Ys=function(n,e){return W(n,S(Mn,e))},Js=function(n){return"br"===ne(n)},Qs=function(n,e,t){return e(n,t).bind(function(n){return ue(n)&&0===Zt(n).trim().length?Qs(n,e,t):x.some(n)})},Zs=function(e,n,t,r){return he(o=n,u=t).filter(Js).orThunk(function(){return he(o,u-1).filter(Js)}).bind(function(n){return r.traverse(n).fold(function(){return Qs(n,r.gather,e).map(r.relative)},function(n){return se(r=n).bind(function(e){var t=pe(e);return Ys(t,r).map(function(n){return{parent:e,children:t,element:r,index:n}})}).map(function(n){return Nf.on(n.parent,n.index)});var r})});var o,u},nd=function(n,e,t,r){var o,u,i;return(Js(e)?(o=n,u=e,(i=r).traverse(u).orThunk(function(){return Qs(u,i.gather,o)}).map(i.relative)):Zs(n,e,t,r)).map(function(n){return{start:n,finish:n}})},ed=function(n,e){return{left:n.left,top:n.top+e,right:n.right,bottom:n.bottom+e}},td=function(n,e){return{left:n.left,top:n.top-e,right:n.right,bottom:n.bottom-e}},rd=function(n,e,t){return{left:n.left+e,top:n.top+t,right:n.right+e,bottom:n.bottom+t}},od=function(n){return{left:n.left,top:n.top,right:n.right,bottom:n.bottom}},ud=function(n,e){return x.some(n.getRect(e))},id=function(n,e,t){return oe(e)?ud(n,e).map(od):ue(e)?(r=n,o=e,(0<=(u=t)&&u<eu(o)?r.getRangedRect(o,u,o,u+1):0<u?r.getRangedRect(o,u-1,o,u):x.none()).map(od)):x.none();var r,o,u},cd=function(n,e){return oe(e)?ud(n,e).map(od):ue(e)?n.getRangedRect(e,0,e,eu(e)).map(od):x.none()},ad=Cr([{none:[]},{retry:["caret"]}]),ld=function(e,n,r){return ke(function(n,e){return e(n)},Pe,n,_a,t).fold(m,function(n){return cd(e,n).exists(function(n){return t=n,(e=r).left<t.left||Math.abs(t.right-e.left)<1||e.left>t.right;var e,t})});var t},fd={point:function(n){return n.bottom},adjuster:function(n,e,t,r,o){var u=ed(o,5);return Math.abs(t.bottom-r.bottom)<1||t.top>o.bottom?ad.retry(u):t.top===o.bottom?ad.retry(ed(o,1)):ld(n,e,o)?ad.retry(rd(u,5,0)):ad.none()},move:ed,gather:Ks},sd=function(t,r,o,u,i){return 0===i?x.some(u):(a=t,l=u.left,f=r.point(u),a.elementFromPoint(l,f).filter(function(n){return"table"===ne(n)}).isSome()?(e=u,c=i-1,sd(t,n=r,o,n.move(e,5),c)):t.situsFromPoint(u.left,r.point(u)).bind(function(n){return n.start.fold(x.none,function(e){return cd(t,e).bind(function(n){return r.adjuster(t,e,n,o,u).fold(x.none,function(n){return sd(t,r,o,n,i-1)})}).orThunk(function(){return x.some(u)})},x.none)}));var n,e,c,a,l,f},dd=function(e,t,n){var r,o,u,i=e.move(n,5),c=sd(t,e,n,i,100).getOr(i);return o=c,u=t,((r=e).point(o)>u.getInnerHeight()?x.some(r.point(o)-u.getInnerHeight()):r.point(o)<0?x.some(-r.point(o)):x.none()).fold(function(){return t.situsFromPoint(c.left,e.point(c))},function(n){return t.scrollBy(0,n),t.situsFromPoint(c.left,e.point(c)-n)})},md={tryUp:S(dd,{point:function(n){return n.top},adjuster:function(n,e,t,r,o){var u=td(o,5);return Math.abs(t.top-r.top)<1||t.bottom<o.top?ad.retry(u):t.bottom===o.top?ad.retry(td(o,1)):ld(n,e,o)?ad.retry(rd(u,5,0)):ad.none()},move:td,gather:Us}),tryDown:S(dd,fd),ieTryUp:function(n,e){return n.situsFromPoint(e.left,e.top-5)},ieTryDown:function(n,e){return n.situsFromPoint(e.left,e.bottom+5)},getJumpSize:b(5)},gd=function(u,i,c){return u.getSelection().bind(function(o){return nd(i,o.finish,o.foffset,c).fold(function(){return x.some(jc(o.finish,o.foffset))},function(n){var e,t=u.fromSitus(n),r=Xs.verify(u,o.finish,o.foffset,t.finish,t.foffset,c.failure,i);return e=r,Xs.cata(e,function(n){return x.none()},function(){return x.none()},function(n){return x.some(jc(n,0))},function(n){return x.some(jc(n,eu(n)))})})})},pd=function(r,o,u,i,c,a){return 0===a?x.none():bd(r,o,u,i,c).bind(function(n){var e=r.fromSitus(n),t=Xs.verify(r,u,i,e.finish,e.foffset,c.failure,o);return Xs.cata(t,function(){return x.none()},function(){return x.some(n)},function(n){return Mn(u,n)&&0===i?hd(r,u,i,td,c):pd(r,o,n,0,c,a-1)},function(n){return Mn(u,n)&&i===eu(n)?hd(r,u,i,ed,c):pd(r,o,n,eu(n),c,a-1)})})},hd=function(e,n,t,r,o){return id(e,n,t).bind(function(n){return vd(e,o,r(n,md.getJumpSize()))})},vd=function(n,e,t){var r=kn().browser;return r.isChrome()||r.isSafari()||r.isFirefox()||r.isEdge()?e.otherRetry(n,t):r.isIE()?e.ieRetry(n,t):x.none()},bd=function(e,n,t,r,o){return id(e,t,r).bind(function(n){return vd(e,o,n)})},wd=function(n,e){return Pe(n,function(n){return se(n).exists(function(n){return Mn(n,e)})},t).isSome();var t},yd=function(u,i,c,n,a){return We(n,"td,th",i).bind(function(o){return We(o,"table",i).bind(function(n){return wd(a,n)?gd(e=u,t=i,r=c).bind(function(n){return pd(e,t,n.element,n.offset,r,20).map(e.fromSitus)}).bind(function(e){return We(e.finish,"td,th",i).map(function(n){return{start:o,finish:n,range:e}})}):x.none();var e,t,r})})},Cd=function(n,e,t,r,o,u){return kn().browser.isIE()?x.none():u(r,e).orThunk(function(){return yd(n,e,t,r,o).map(function(n){var e=n.range;return Os(x.some(Is(e.start,e.soffset,e.finish,e.foffset)),!0)})})},xd=function(n,r){return We(n,"tr",r).bind(function(t){return We(t,"table",r).bind(function(n){var e=Ae(n,"tr");return Mn(t,e[0])?Hs(Vs,n,function(n){return uu(n).isSome()},r).map(function(n){var e=eu(n);return Os(x.some(Is(n,e,n,e)),!0)}):x.none()})})},Sd=function(n,r){return We(n,"tr",r).bind(function(t){return We(t,"table",r).bind(function(n){var e=Ae(n,"tr");return Mn(t,e[e.length-1])?qs(Vs,n,function(n){return ou(n).isSome()},r).map(function(n){return Os(x.some(Is(n,0,n,0)),!0)}):x.none()})})},Td=function(n,e,t,r,o,u,i){return yd(n,t,r,o,u).bind(function(n){return Bs(e,t,n.start,n.finish,i)})},Rd=function(n,e){return We(n,"td,th",e)},Dd=function(o,u,e,i){var t,r=(t=Ko(x.none()),{clear:function(){return t.set(x.none())},set:function(n){return t.set(x.some(n))},isSet:function(){return t.get().isSome()},on:function(n){return t.get().each(n)}}),c=r.clear,a=function(n){r.on(function(r){i.clearBeforeUpdate(u),Rd(n.target,e).each(function(t){pr(r,t,e).each(function(n){var e=n.boxes.getOr([]);(1<e.length||1===e.length&&!Mn(r,t))&&(i.selectRange(u,e,n.start,n.finish),o.selectContents(t))})})})};return{clearstate:c,mousedown:function(n){i.clear(u),Rd(n.target,e).each(r.set)},mouseover:function(n){a(n)},mouseup:function(n){a(n),c()}}},Od={traverse:ge,gather:Ks,relative:Nf.before,otherRetry:md.tryDown,ieRetry:md.ieTryDown,failure:Xs.failedDown},Ad={traverse:me,gather:Us,relative:Nf.before,otherRetry:md.tryUp,ieRetry:md.ieTryUp,failure:Xs.failedUp},kd=function(e){return function(n){return n===e}},Id=kd(38),Bd=kd(40),Ed=function(n){return 37<=n&&n<=40},Pd={isBackward:kd(37),isForward:kd(39)},Md={isBackward:kd(39),isForward:kd(37)},Nd=function(c){return{elementFromPoint:function(n,e){return Bn.fromPoint(Bn.fromDom(c.document),n,e)},getRect:function(n){return n.dom.getBoundingClientRect()},getRangedRect:function(n,e,t,r){var o=Wf.exact(n,e,t,r);return ws(c,o)},getSelection:function(){return bs(c).map(function(n){return ks(c,n)})},fromSitus:function(n){var e=Wf.relative(n.start,n.finish);return ks(c,e)},situsFromPoint:function(n,e){return ys(c,n,e).map(function(n){return As(n.start,n.soffset,n.finish,n.foffset)})},clearSelection:function(){as(c).each(function(n){return n.removeAllRanges()})},collapseSelection:function(i){void 0===i&&(i=!1),bs(c).each(function(n){return n.fold(function(n){return n.collapse(i)},function(n,e){var t=i?n:e;ms(c,t,t)},function(n,e,t,r){var o=i?n:t,u=i?e:r;ds(c,o,u,o,u)})})},setSelection:function(n){ds(c,n.start,n.soffset,n.finish,n.foffset)},setRelativeSelection:function(n,e){ms(c,n,e)},selectContents:function(n){vs(c,n)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){var n,e,t,r;return(n=Bn.fromDom(c.document),e=n!==undefined?n.dom:document,t=e.body.scrollLeft||e.documentElement.scrollLeft,r=e.body.scrollTop||e.documentElement.scrollTop,Gr(t,r)).top},scrollBy:function(n,e){var t,r,o,u;t=n,r=e,o=Bn.fromDom(c.document),(u=(o!==undefined?o.dom:document).defaultView)&&u.scrollBy(t,r)}}},jd=function(n,e){return{rows:n,cols:e}},Wd=function(n,g,p,h){var l=Nd(n),f=function(){return h.clear(g),x.none()};return{keydown:function(n,e,t,r,o,i){var u=n.raw,c=u.which,a=!0===u.shiftKey;return hr(g,h.selectedSelector).fold(function(){return Bd(c)&&a?S(Td,l,g,p,Od,r,e,h.selectRange):Id(c)&&a?S(Td,l,g,p,Ad,r,e,h.selectRange):Bd(c)?S(Cd,l,p,Od,r,e,Sd):Id(c)?S(Cd,l,p,Ad,r,e,xd):x.none},function(u){var n=function(n){return function(){return K(n,function(n){return e=n.rows,t=n.cols,r=g,br(u,e,t,(o=h).firstSelectedSelector,o.lastSelectedSelector).map(function(n){return o.clearBeforeUpdate(r),o.selectRange(r,n.boxes,n.start,n.finish),n.boxes});var e,t,r,o}).fold(function(){return vr(g,h.firstSelectedSelector,h.lastSelectedSelector).map(function(n){var e=Bd(c)||i.isForward(c)?Nf.after:Nf.before;return l.setRelativeSelection(Nf.on(n.first,0),e(n.table)),h.clear(g),Os(x.none(),!0)})},function(n){return x.some(Os(x.none(),!0))})}};return Bd(c)&&a?n([jd(1,0)]):Id(c)&&a?n([jd(-1,0)]):i.isBackward(c)&&a?n([jd(0,-1),jd(-1,0)]):i.isForward(c)&&a?n([jd(0,1),jd(1,0)]):Ed(c)&&!1==a?f:x.none})()},keyup:function(l,f,s,d,m){return hr(g,h.selectedSelector).fold(function(){var t,r,n,e,o,u,i,c=l.raw,a=c.which;return!1!=(!0===c.shiftKey)&&Ed(a)?(t=g,r=p,n=f,e=s,o=d,u=m,i=h.selectRange,Mn(n,o)&&e===u?x.none():We(n,"td,th",r).bind(function(e){return We(o,"td,th",r).bind(function(n){return Bs(t,r,e,n,i)})})):x.none()},x.none)}}},Ld=function(o,u,e){var t=function(n){qe(n,o.selected),qe(n,o.firstSelected),qe(n,o.lastSelected)},i=function(n){ze(n,o.selected,"1")},c=function(n){r(n),e()},r=function(n){var e=Ae(n,o.selectedSelector);E(e,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(n,e,t,r){c(n),E(e,i),ze(t,o.firstSelected,"1"),ze(r,o.lastSelected,"1"),u(e,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},zd=function(n,e,s){var d=Et.fromTable(n);return ra(d,e).map(function(n){var t,e,r,o,u,i,c,a,l,f=Xc(d,s,!1);return{upOrLeftCells:(t=n,e=s,r=f.slice(0,t[t.length-1].row+1),o=Jc(r,e),z(o,function(n){var e=n.cells.slice(0,t[t.length-1].column+1);return B(e,function(n){return n.element})})),downOrRightCells:(i=n,c=s,a=(u=f).slice(i[0].row+i[0].rowspan-1,u.length),l=Jc(a,c),z(l,function(n){var e=n.cells.slice(i[0].column+i[0].colspan-1,n.cells.length);return B(e,function(n){return n.element})}))}})},_d=function(n){return!1===Mi(Bn.fromDom(n.target),"ephox-snooker-resizer-bar")};function Fd(b,w,n){var y=Ld(Bu,function(l,f,s){n.targets().each(function(a){st(f).each(function(n){var e,t,r,o,u=Rc(b),i=gu(C,Bn.fromDom(b.getDoc()),u),c=zd(n,a,i);e=l,t=f,r=s,o=c,b.fire("TableSelectionChange",{cells:e,start:t,finish:r,otherCells:o})})})},function(){b.fire("TableSelectionClear")});return b.on("init",function(n){var e,t,r,o,u,i,c=b.getWin(),a=bu(b),l=Cu(b),f=(e=Nd(c),{clearstate:(t=Dd(e,a,l,y)).clearstate,mousedown:t.mousedown,mouseover:t.mouseover,mouseup:t.mouseup}),s=Wd(c,a,l,y),d=(r=a,o=l,u=y,i=Nd(c),function(n,t){u.clearBeforeUpdate(r),pr(n,t,o).each(function(n){var e=n.boxes.getOr([]);u.selectRange(r,e,n.start,n.finish),i.selectContents(t),i.collapseSelection()})});b.on("TableSelectorChange",function(n){return d(n.start,n.finish)});var m,g,p=function(n,e){!0===n.raw.shiftKey&&(e.kill&&n.kill(),e.selection.each(function(n){var e=Wf.relative(n.start,n.finish),t=Uf(c,e);b.selection.setRng(t)}))},h=function(n){return 0===n.button},v=(m=Ko(Bn.fromDom(a)),g=Ko(0),{touchEnd:function(n){var e,t,r=Bn.fromDom(n.target);"td"!==ne(r)&&"th"!==ne(r)||(e=m.get(),t=g.get(),Mn(e,r)&&n.timeStamp-t<300&&(n.preventDefault(),d(r,r))),m.set(r),g.set(n.timeStamp)}});b.on("dragstart",function(n){f.clearstate()}),b.on("mousedown",function(n){h(n)&&_d(n)&&f.mousedown(ki(n))}),b.on("mouseover",function(n){var e;((e=n).buttons===undefined||Cf.browser.isEdge()&&0===e.buttons||0!=(1&e.buttons))&&_d(n)&&f.mouseover(ki(n))}),b.on("mouseup",function(n){h(n)&&_d(n)&&f.mouseup(ki(n))}),b.on("touchend",v.touchEnd),b.on("keyup",function(n){var e,t,r,o=ki(n);o.raw.shiftKey&&Ed(o.raw.which)&&(e=b.selection.getRng(),t=Bn.fromDom(e.startContainer),r=Bn.fromDom(e.endContainer),s.keyup(o,t,e.startOffset,r,e.endOffset).each(function(n){p(o,n)}))}),b.on("keydown",function(n){var e=ki(n);w().each(function(n){return n.hideBars()});var t=b.selection.getRng(),r=Bn.fromDom(t.startContainer),o=Bn.fromDom(t.endContainer),u=Hr(Pd,Md)(Bn.fromDom(b.selection.getStart()));s.keydown(e,r,t.startOffset,o,t.endOffset,u).each(function(n){p(e,n)}),w().each(function(n){return n.showBars()})}),b.on("NodeChange",function(){var n=b.selection,e=Bn.fromDom(n.getStart()),t=Bn.fromDom(n.getEnd());mr(st,[e,t]).fold(function(){return y.clear(a)},C)})}),{clear:y.clear}}var Hd=function(n,r){var o=Ko(x.none()),u=Ko([]),t=x.none(),i=ae("caption"),e=function(e){return t.forall(function(n){return!n[e]})},c=function(){return Il(Du(n),Cu(n)).bind(function(t){return st(t).map(function(n){return i(t)?{element:e=t,mergable:x.none(),unmergable:x.none(),selection:[e]}:Eu(r,n,t);var e})})},a=function(o){return st(o.element).map(function(n){var t=Et.fromTable(n),e=ra(t,o).getOr([]),r=N(e,function(n,e){return e.isLocked&&(n.onAny=!0,0===e.column?n.onFirst=!0:e.column+e.colspan>=t.grid.columns&&(n.onLast=!0)),n},{onAny:!1,onFirst:!1,onLast:!1});return{mergeable:ca(t,o).isSome(),unmergeable:aa(t,o).isSome(),locked:r}})},l=function(){o.set(Y(c)()),t=o.get().bind(a),E(u.get(),function(n){return n()})},f=function(e,t){var r=function(){return o.get().fold(function(){e.setDisabled(!0)},function(n){e.setDisabled(t(n))})};return r(),u.set(u.get().concat([r])),function(){u.set(P(u.get(),function(n){return n!==r}))}},s=function(e){return t.exists(function(n){return n.locked[e]})};return n.on("NodeChange ExecCommand TableSelectorChange",l),{onSetupTable:function(n){return f(n,function(n){return!1})},onSetupCellOrRow:function(n){return f(n,function(n){return i(n.element)})},onSetupColumn:function(e){return function(n){return f(n,function(n){return i(n.element)||s(e)})}},onSetupPasteable:function(e){return function(n){return f(n,function(n){return i(n.element)||e().isNone()})}},onSetupPasteableColumn:function(e,t){return function(n){return f(n,function(n){return i(n.element)||e().isNone()||s(t)})}},onSetupMergeable:function(n){return f(n,function(n){return e("mergeable")})},onSetupUnmergeable:function(n){return f(n,function(n){return e("unmergeable")})},resetTargets:l,targets:function(){return o.get()}}},qd=function(a){var n,e,t,r,o,u,i,c,l=(n=function(){return bu(a)},e=function(){return Il(Du(a))},t=Bu.selectedSelector,{get:function(){return wr(n(),t).fold(function(){return e().map(Dr).getOrThunk(Tr)},function(n){return Rr(n)})}}),f=Hd(a,l),s=Nc(a),d=Fd(a,s.lazyResize,f),m=Pl(a,s.lazyWire,l),g=(r=Ko(x.none()),o=Ko(x.none()),u=function(n){n.set(x.none())},{getRows:r.get,setRows:function(n){r.set(n),u(o)},clearRows:function(){return u(r)},getColumns:o.get,setColumns:function(n){o.set(n),u(r)},clearColumns:function(){return u(o)}});return Of(a,m,d,l,g),Af(a,m,l),Pu(a,l,m,d),function(e,n,t){var r=function(n){return function(){return e.execCommand(n)}},o=function(n){e.execCommand("mceInsertTable",!1,{rows:n.numRows,columns:n.numColumns})},u={text:"Table properties",onSetup:n.onSetupTable,onAction:r("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:n.onSetupTable,onAction:r("mceTableDelete")};e.ui.registry.addMenuItem("tableinsertrowbefore",{text:"Insert row before",icon:"table-insert-row-above",onAction:r("mceTableInsertRowBefore"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tableinsertrowafter",{text:"Insert row after",icon:"table-insert-row-after",onAction:r("mceTableInsertRowAfter"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tabledeleterow",{text:"Delete row",icon:"table-delete-row",onAction:r("mceTableDeleteRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablerowprops",{text:"Row properties",icon:"table-row-properties",onAction:r("mceTableRowProps"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecutrow",{text:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablecopyrow",{text:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablepasterowbefore",{text:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addMenuItem("tablepasterowafter",{text:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)});e.ui.registry.addMenuItem("tableinsertcolumnbefore",{text:"Insert column before",icon:"table-insert-column-before",onAction:r("mceTableInsertColBefore"),onSetup:n.onSetupColumn("onFirst")}),e.ui.registry.addMenuItem("tableinsertcolumnafter",{text:"Insert column after",icon:"table-insert-column-after",onAction:r("mceTableInsertColAfter"),onSetup:n.onSetupColumn("onLast")}),e.ui.registry.addMenuItem("tabledeletecolumn",{text:"Delete column",icon:"table-delete-column",onAction:r("mceTableDeleteCol"),onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addMenuItem("tablecutcolumn",{text:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addMenuItem("tablecopycolumn",{text:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addMenuItem("tablepastecolumnbefore",{text:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteableColumn(t.getColumns,"onFirst")}),e.ui.registry.addMenuItem("tablepastecolumnafter",{text:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteableColumn(t.getColumns,"onLast")});e.ui.registry.addMenuItem("tablecellprops",{text:"Cell properties",icon:"table-cell-properties",onAction:r("mceTableCellProps"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addMenuItem("tablemergecells",{text:"Merge cells",icon:"table-merge-cells",onAction:r("mceTableMergeCells"),onSetup:n.onSetupMergeable}),e.ui.registry.addMenuItem("tablesplitcells",{text:"Split cell",icon:"table-split-cells",onAction:r("mceTableSplitCells"),onSetup:n.onSetupUnmergeable});!1===e.getParam("table_grid",!0,"boolean")?e.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:r("mceInsertTable")}):e.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:o}]}}),e.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:r("mceInsertTable")}),e.ui.registry.addMenuItem("tableprops",u),e.ui.registry.addMenuItem("deletetable",i),e.ui.registry.addNestedMenuItem("row",{type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return"tableinsertrowbefore tableinsertrowafter tabledeleterow tablerowprops | tablecutrow tablecopyrow tablepasterowbefore tablepasterowafter"}}),e.ui.registry.addNestedMenuItem("column",{type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return"tableinsertcolumnbefore tableinsertcolumnafter tabledeletecolumn | tablecutcolumn tablecopycolumn tablepastecolumnbefore tablepastecolumnafter"}}),e.ui.registry.addNestedMenuItem("cell",{type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return"tablecellprops tablemergecells tablesplitcells"}}),e.ui.registry.addContextMenu("table",{update:function(){return n.resetTargets(),n.targets().fold(function(){return""},function(n){return"caption"===ne(n.element)?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})}(a,f,g),function(e,n,t){e.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(n){return n("inserttable | cell row column | advtablesort | tableprops deletetable")}});var r=function(n){return function(){return e.execCommand(n)}};e.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:r("mceTableProps"),icon:"table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:r("mceTableDelete"),icon:"table-delete-table",onSetup:n.onSetupTable}),e.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:r("mceTableCellProps"),icon:"table-cell-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:r("mceTableMergeCells"),icon:"table-merge-cells",onSetup:n.onSetupMergeable}),e.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:r("mceTableSplitCells"),icon:"table-split-cells",onSetup:n.onSetupUnmergeable}),e.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:r("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:r("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:r("mceTableDeleteRow"),icon:"table-delete-row",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:r("mceTableRowProps"),icon:"table-row-properties",onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:r("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:n.onSetupColumn("onFirst")}),e.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:r("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:n.onSetupColumn("onLast")}),e.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:r("mceTableDeleteCol"),icon:"table-delete-column",onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",icon:"cut-row",onAction:r("mceTableCutRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",icon:"duplicate-row",onAction:r("mceTableCopyRow"),onSetup:n.onSetupCellOrRow}),e.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",icon:"paste-row-before",onAction:r("mceTablePasteRowBefore"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",icon:"paste-row-after",onAction:r("mceTablePasteRowAfter"),onSetup:n.onSetupPasteable(t.getRows)}),e.ui.registry.addButton("tablecutcol",{tooltip:"Cut column",icon:"cut-column",onAction:r("mceTableCutCol"),onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addButton("tablecopycol",{tooltip:"Copy column",icon:"duplicate-column",onAction:r("mceTableCopyCol"),onSetup:n.onSetupColumn("onAny")}),e.ui.registry.addButton("tablepastecolbefore",{tooltip:"Paste column before",icon:"paste-column-before",onAction:r("mceTablePasteColBefore"),onSetup:n.onSetupPasteableColumn(t.getColumns,"onFirst")}),e.ui.registry.addButton("tablepastecolafter",{tooltip:"Paste column after",icon:"paste-column-after",onAction:r("mceTablePasteColAfter"),onSetup:n.onSetupPasteableColumn(t.getColumns,"onLast")}),e.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:r("mceInsertTable"),icon:"table"})}(a,f,g),0<(c=(i=a).getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol")).length&&i.ui.registry.addContextToolbar("table",{predicate:function(n){return i.dom.is(n,"table")&&i.getBody().contains(n)},items:c,scope:"node",position:"node"}),a.on("PreInit",function(){a.serializer.addTempAttr(Bu.firstSelected),a.serializer.addTempAttr(Bu.lastSelected),a.formatter.register(kf)}),a.getParam("table_tab_navigation",!0,"boolean")&&a.on("keydown",function(n){var e,t,r,o,u,i,c;t=a,r=d,(e=n).keyCode===Cs.TAB&&(o=bu(t),u=function(n){var e=ne(n);return Mn(n,o)||A(Ds,e)},i=t.selection.getRng(),c=Bn.fromDom(e.shiftKey?i.startContainer:i.endContainer),at(c,u).each(function(n){e.preventDefault(),st(n,u).each(r.clear),t.selection.collapse(e.shiftKey),(e.shiftKey?Ss:xs)(t,u,n).each(function(n){t.selection.setRng(n)})}))}),a.on("remove",function(){s.destroy()}),ql(a,g,s,f)};Or.add("table",qd)}();