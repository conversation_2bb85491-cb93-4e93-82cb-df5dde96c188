// // use once_cell::sync::Lazy;
// use rand::prelude::*;
// // use regex::Regex;
// use std::collections::HashMap;

// pub trait HMVecValRand {
//     fn get_random(&self, rng: &mut ThreadRng, key: &str) -> String;
// }
// impl HMVecValRand for HashMap<String, Vec<String>> {
//     fn get_random(&self, rng: &mut ThreadRng, key: &str) -> String {
//         self.get(key)
//             .and_then(|list| {
//                 if !list.is_empty() {
//                     list.choose(rng).map(|s| s.to_string())
//                 } else {
//                     None
//                 }
//             })
//             .unwrap_or_else(|| "".to_string())
//     }
// }
// // static TagRe: Lazy<regex::Regex> =
// //     Lazy::new(|| Regex::new(r"\{[\p{Han}]+(\d*)(?:_(\d+))?\}").unwrap());

// pub trait HMStrValRand {
//     fn get_random(&self, rng: &mut ThreadRng) -> String;
// }
// impl HMStrValRand for HashMap<String, String> {
//     fn get_random(&self, rng: &mut ThreadRng) -> String {
//         self.values().choose(rng).cloned().unwrap_or("".to_string())
//     }
// }

// pub trait StringReplacerTag {
//     fn replacen_tags(&self, tag: &str, value: &str) -> String;
//     fn replacen_tag(&self, tag: &str, value: &str) -> String;
// }
// impl StringReplacerTag for String {
//     fn replacen_tags(&self, tag: &str, value: &str) -> String {
//         self.replace(tag, value)
//     }
//     fn replacen_tag(&self, tag: &str, value: &str) -> String {
//         self.replacen(tag, value, 1)
//     }
// }
