<?php

namespace App\Admin\Services;

use App\Models\SeoRegion;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class MenuSyncService
{
    /**
     * 同步地区菜单
     */
    public static function syncRegionMenus()
    {
        try {
            DB::beginTransaction();
            
            // 获取网站管理主菜单ID
            $websiteMainMenu = DB::table('admin_menu')
                ->where('title', '网站管理')
                ->where('parent_id', 0)
                ->first();
            
            if (!$websiteMainMenu) {
                // 创建网站管理主菜单
                $websiteMainMenuId = DB::table('admin_menu')->insertGetId([
                    'parent_id' => 0,
                    'order' => 15,
                    'title' => '网站管理',
                    'icon' => 'fa-sitemap',
                    'uri' => '',
                    'extension' => '',
                    'show' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            } else {
                $websiteMainMenuId = $websiteMainMenu->id;
            }
            
            // 确保全部网站菜单存在
            $allWebsitesMenu = DB::table('admin_menu')
                ->where('title', '全部网站')
                ->where('parent_id', $websiteMainMenuId)
                ->first();
                
            if (!$allWebsitesMenu) {
                DB::table('admin_menu')->insert([
                    'parent_id' => $websiteMainMenuId,
                    'order' => 1,
                    'title' => '全部网站',
                    'icon' => 'fa-globe',
                    'uri' => 'seo/website-management',
                    'extension' => '',
                    'show' => 1,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);
            }
            
            // 获取所有启用的地区
            $regions = SeoRegion::where('status', 1)
                              ->orderBy('priority')
                              ->orderBy('code')
                              ->get();
            
            // 删除不存在的地区菜单
            $existingRegionCodes = $regions->pluck('code')->toArray();
            DB::table('admin_menu')
                ->where('parent_id', $websiteMainMenuId)
                ->where('title', '!=', '全部网站')
                ->whereNotIn('uri', array_map(function($code) {
                    return "seo/website-management?region={$code}";
                }, $existingRegionCodes))
                ->delete();
            
            // 为每个地区创建或更新菜单
            foreach ($regions as $index => $region) {
                $menuTitle = self::getRegionDisplayName($region);
                $menuUri = "seo/website-management?region={$region->code}";
                
                $existingMenu = DB::table('admin_menu')
                    ->where('parent_id', $websiteMainMenuId)
                    ->where('uri', $menuUri)
                    ->first();
                
                if ($existingMenu) {
                    // 更新现有菜单
                    DB::table('admin_menu')
                        ->where('id', $existingMenu->id)
                        ->update([
                            'title' => $menuTitle,
                            'order' => $index + 2,
                            'updated_at' => now()
                        ]);
                } else {
                    // 创建新菜单
                    DB::table('admin_menu')->insert([
                        'parent_id' => $websiteMainMenuId,
                        'order' => $index + 2,
                        'title' => $menuTitle,
                        'icon' => 'fa-circle-thin',
                        'uri' => $menuUri,
                        'extension' => '',
                        'show' => 1,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }
            }
            
            DB::commit();
            
            // 清除菜单缓存
            RegionMenuService::clearMenuCache();
            
            Log::info('地区菜单同步完成', [
                'regions_count' => $regions->count(),
                'main_menu_id' => $websiteMainMenuId
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('地区菜单同步失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return false;
        }
    }
    
    /**
     * 获取地区显示名称（带国旗emoji）
     */
    private static function getRegionDisplayName($region)
    {
        $flags = [
            'br' => '🇧🇷',
            'pk' => '🇵🇰', 
            'in' => '🇮🇳',
            'us' => '🇺🇸',
            'id' => '🇮🇩',
            'th' => '🇹🇭',
            'mx' => '🇲🇽',
            'ng' => '🇳🇬',
            'bd' => '🇧🇩',
            'ph' => '🇵🇭',
            'vn' => '🇻🇳',
            'jp' => '🇯🇵',
            'default' => '🌍'
        ];
        
        $flag = $flags[$region->code] ?? '🌍';
        return "{$flag} {$region->name}";
    }
    
    /**
     * 删除地区菜单
     */
    public static function removeRegionMenu($regionCode)
    {
        try {
            $menuUri = "seo/website-management?region={$regionCode}";
            
            DB::table('admin_menu')
                ->where('uri', $menuUri)
                ->delete();
            
            // 清除缓存
            RegionMenuService::clearMenuCache();
            
            Log::info('地区菜单删除成功', ['region_code' => $regionCode]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('地区菜单删除失败', [
                'region_code' => $regionCode,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }
}
