use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::RwLock;
use chrono::{DateTime, Utc};
use chrono_tz::Tz;

use crate::entities::Region;

/// 地区时区上下文管理器
/// 
/// 负责管理不同地区的时区信息，提供地区特定的时间计算功能
#[derive(Debug, <PERSON>lone)]
pub struct RegionTimezoneContext {
    /// 地区时区映射缓存
    region_timezones: Arc<RwLock<HashMap<String, String>>>,
    /// 默认时区
    default_timezone: String,
    /// 缓存更新时间
    last_updated: Arc<RwLock<DateTime<Utc>>>,
}

impl RegionTimezoneContext {
    /// 创建新的地区时区上下文
    pub fn new(default_timezone: String) -> Self {
        Self {
            region_timezones: Arc::new(RwLock::new(HashMap::new())),
            default_timezone,
            last_updated: Arc::new(RwLock::new(Utc::now())),
        }
    }

    /// 从地区列表初始化时区映射
    pub async fn initialize_from_regions(&self, regions: Vec<Region>) {
        let mut timezones = self.region_timezones.write().await;
        timezones.clear();

        for region in regions {
            // 验证时区字符串是否有效
            if region.timezone.parse::<chrono_tz::Tz>().is_ok() {
                timezones.insert(region.code.clone(), region.timezone.clone());
            } else {
                eprintln!("⚠️  地区 {} 的时区 '{}' 无效，跳过", region.code, region.timezone);
            }
        }

        *self.last_updated.write().await = Utc::now();
    }

    /// 获取地区时区
    pub async fn get_region_timezone(&self, region_code: &str) -> String {
        let timezones = self.region_timezones.read().await;
        match timezones.get(region_code) {
            Some(timezone) => {
                timezone.clone()
            }
            None => {
                eprintln!("⚠️  地区 {} 时区配置缺失，使用默认时区: {}", region_code, self.default_timezone);
                self.default_timezone.clone()
            }
        }
    }

    /// 更新地区时区
    pub async fn update_region_timezone(&self, region_code: String, timezone: String) {
        let mut timezones = self.region_timezones.write().await;
        timezones.insert(region_code, timezone);
        *self.last_updated.write().await = Utc::now();
    }

    /// 移除地区时区
    pub async fn remove_region_timezone(&self, region_code: &str) {
        let mut timezones = self.region_timezones.write().await;
        timezones.remove(region_code);
        *self.last_updated.write().await = Utc::now();
    }

    /// 获取所有地区时区
    pub async fn get_all_region_timezones(&self) -> HashMap<String, String> {
        self.region_timezones.read().await.clone()
    }

    /// 检查时区是否有效
    pub fn is_valid_timezone(&self, timezone: &str) -> bool {
        timezone.parse::<Tz>().is_ok()
    }

    /// 获取缓存更新时间
    pub async fn get_last_updated(&self) -> DateTime<Utc> {
        *self.last_updated.read().await
    }

    /// 获取地区数量
    pub async fn get_region_count(&self) -> usize {
        self.region_timezones.read().await.len()
    }
}

/// 地区时区工具函数
pub struct RegionTimezoneUtils;

impl RegionTimezoneUtils {
    /// 根据地区代码获取当前时间
    pub async fn now_for_region_code(
        context: &RegionTimezoneContext, 
        region_code: &str
    ) -> DateTime<Tz> {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::now_for_region(&timezone)
    }

    /// 根据地区代码获取时间戳
    pub async fn timestamp_for_region_code(
        context: &RegionTimezoneContext, 
        region_code: &str
    ) -> i64 {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::timestamp_for_region(&timezone)
    }

    /// 根据地区代码计算缓存过期时间
    pub async fn calculate_cache_expiration_for_region(
        context: &RegionTimezoneContext,
        region_code: &str,
        expiration_hours: u64
    ) -> i64 {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::calculate_region_cache_expiration(&timezone, expiration_hours)
    }

    /// 根据地区代码格式化时间
    pub async fn format_time_for_region(
        context: &RegionTimezoneContext,
        region_code: &str,
        format: &str
    ) -> String {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::format_region_time(&timezone, format)
    }

    /// 检查时间戳是否在地区时区下过期
    pub async fn is_expired_in_region_code(
        context: &RegionTimezoneContext,
        timestamp: i64,
        region_code: &str
    ) -> bool {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::is_expired_in_region(timestamp, &timezone)
    }

    /// 获取地区的日期边界
    pub async fn get_region_day_boundaries(
        context: &RegionTimezoneContext,
        region_code: &str
    ) -> (i64, i64) {
        let timezone = context.get_region_timezone(region_code).await;
        let day_start = crate::util::time::get_region_day_start_timestamp(&timezone);
        let day_end = crate::util::time::get_region_day_end_timestamp(&timezone);
        (day_start, day_end)
    }

    /// 将UTC时间转换为地区时间
    pub async fn utc_to_region_time(
        context: &RegionTimezoneContext,
        utc_time: DateTime<Utc>,
        region_code: &str
    ) -> DateTime<Tz> {
        let timezone = context.get_region_timezone(region_code).await;
        crate::util::time::utc_to_region_timezone(utc_time, &timezone)
    }
}

/// 地区时区统计信息
#[derive(Debug, Clone, serde::Serialize)]
pub struct RegionTimezoneStats {
    pub total_regions: usize,
    pub unique_timezones: usize,
    pub last_updated: String,
    pub timezone_distribution: HashMap<String, usize>,
}

impl RegionTimezoneContext {
    /// 获取地区时区统计信息
    pub async fn get_stats(&self) -> RegionTimezoneStats {
        let timezones = self.region_timezones.read().await;
        let last_updated = self.last_updated.read().await;
        
        // 统计时区分布
        let mut timezone_distribution = HashMap::new();
        for timezone in timezones.values() {
            *timezone_distribution.entry(timezone.clone()).or_insert(0) += 1;
        }
        
        RegionTimezoneStats {
            total_regions: timezones.len(),
            unique_timezones: timezone_distribution.len(),
            last_updated: last_updated.format("%Y-%m-%d %H:%M:%S UTC").to_string(),
            timezone_distribution,
        }
    }
}
