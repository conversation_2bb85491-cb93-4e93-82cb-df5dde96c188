<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">地区使用统计</h4>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>地区代码</th>
                                <th>地区名称</th>
                                <th>语言</th>
                                <th>时区</th>
                                <th>总网站数</th>
                                <th>启用网站</th>
                                <th>禁用网站</th>
                                <th>使用率</th>
                                <th>状态</th>
                                <th>数据目录</th>
                                <th>模板目录</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($regions as $region)
                            <tr>
                                <td>
                                    <span class="badge badge-primary">{{ $region->code }}</span>
                                </td>
                                <td>{{ $region->name }}</td>
                                <td>{{ $region->language }}</td>
                                <td>{{ $region->timezone }}</td>
                                <td>
                                    <span class="badge badge-info">{{ $region->site_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-success">{{ $region->active_site_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-warning">{{ $region->site_count - $region->active_site_count }}</span>
                                </td>
                                <td>
                                    @if($region->site_count > 0)
                                        {{ round(($region->active_site_count / $region->site_count) * 100, 1) }}%
                                    @else
                                        0%
                                    @endif
                                </td>
                                <td>
                                    @if($region->status)
                                        <span class="badge badge-success">启用</span>
                                    @else
                                        <span class="badge badge-danger">禁用</span>
                                    @endif
                                </td>
                                <td>
                                    @if($region->hasDataDirectory())
                                        <span class="text-success">✓</span>
                                    @else
                                        <span class="text-danger">✗</span>
                                    @endif
                                </td>
                                <td>
                                    @if($region->hasTemplateDirectory())
                                        <span class="text-success">✓</span>
                                    @else
                                        <span class="text-danger">✗</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button type="button" class="btn btn-outline-primary" onclick="reloadRegion('{{ $region->code }}')">
                                            <i class="feather icon-refresh-cw"></i> 重载
                                        </button>
                                        {{-- 创建目录功能已禁用，请手动创建目录以确保安全 --}}
                                        <button type="button" class="btn btn-outline-secondary" disabled title="为了安全，请手动创建目录">
                                            <i class="feather icon-folder-plus"></i> 创建目录 (已禁用)
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">地区分布</h5>
            </div>
            <div class="card-body">
                <canvas id="regionChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">网站状态分布</h5>
            </div>
            <div class="card-body">
                <canvas id="statusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 地区分布图表
const regionCtx = document.getElementById('regionChart').getContext('2d');
const regionChart = new Chart(regionCtx, {
    type: 'doughnut',
    data: {
        labels: [
            @foreach($regions as $region)
                '{{ $region->name }} ({{ $region->code }})',
            @endforeach
        ],
        datasets: [{
            data: [
                @foreach($regions as $region)
                    {{ $region->site_count }},
                @endforeach
            ],
            backgroundColor: [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', 
                '#FF9F40', '#FF6384', '#C9CBCF', '#4BC0C0', '#FF6384'
            ]
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 网站状态分布图表
const statusCtx = document.getElementById('statusChart').getContext('2d');
const totalActive = {{ $regions->sum('active_site_count') }};
const totalInactive = {{ $regions->sum('site_count') - $regions->sum('active_site_count') }};

const statusChart = new Chart(statusCtx, {
    type: 'pie',
    data: {
        labels: ['启用网站', '禁用网站'],
        datasets: [{
            data: [totalActive, totalInactive],
            backgroundColor: ['#28a745', '#dc3545']
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// 重载地区资源
function reloadRegion(code) {
    if (confirm('确定要重新加载地区 ' + code + ' 的资源吗？')) {
        fetch(`/admin/seo/regions/${code}/reload`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                Dcat.success(data.message);
            } else {
                Dcat.error(data.message);
            }
        })
        .catch(error => {
            Dcat.error('操作失败: ' + error.message);
        });
    }
}

// 创建目录
function createDirectories(code) {
    if (confirm('确定要为地区 ' + code + ' 创建数据和模板目录吗？')) {
        fetch('/admin/seo/regions/batch-create-directories', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                codes: [code]
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status) {
                Dcat.success(data.message);
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                Dcat.error(data.message);
            }
        })
        .catch(error => {
            Dcat.error('操作失败: ' + error.message);
        });
    }
}
</script>

<style>
.table th, .table td {
    vertical-align: middle;
    text-align: center;
}
.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}
</style>
