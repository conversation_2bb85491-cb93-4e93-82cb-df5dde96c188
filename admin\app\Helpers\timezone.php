<?php

/**
 * 时区辅助函数
 */

if (!function_exists('to_app_timezone')) {
    /**
     * 将UTC时间转换为应用时区
     *
     * @param \DateTime|string|null $utcTime UTC时间
     * @return \Carbon\Carbon|null
     */
    function to_app_timezone($utcTime)
    {
        if (!$utcTime) {
            return null;
        }
        
        if (is_string($utcTime)) {
            $utcTime = \Carbon\Carbon::parse($utcTime, 'UTC');
        }
        
        return $utcTime->setTimezone(config('app.timezone'));
    }
}

if (!function_exists('to_utc')) {
    /**
     * 将应用时区时间转换为UTC
     *
     * @param \DateTime|string|null $localTime 本地时间
     * @return \Carbon\Carbon|null
     */
    function to_utc($localTime)
    {
        if (!$localTime) {
            return null;
        }
        
        if (is_string($localTime)) {
            $localTime = \Carbon\Carbon::parse($localTime, config('app.timezone'));
        }
        
        return $localTime->setTimezone('UTC');
    }
}

if (!function_exists('format_datetime')) {
    /**
     * 格式化日期时间为本地格式
     *
     * @param \DateTime|string|null $datetime 日期时间
     * @param string $format 格式
     * @return string|null
     */
    function format_datetime($datetime, $format = 'Y-m-d H:i:s')
    {
        if (!$datetime) {
            return null;
        }
        
        if (is_string($datetime)) {
            // 假设输入是UTC时间
            $datetime = \Carbon\Carbon::parse($datetime, 'UTC');
        }
        
        return to_app_timezone($datetime)->format($format);
    }
} 