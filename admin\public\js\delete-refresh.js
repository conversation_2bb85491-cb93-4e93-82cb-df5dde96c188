/**
 * 通用删除后刷新功能
 * 自动为所有删除按钮添加删除后刷新页面的功能
 */
(function() {
    'use strict';

    // 初始化删除刷新功能
    function initDeleteRefresh() {
        // 调试：输出当前页面的删除按钮
        console.log('Delete buttons found:', $('.grid-row-delete, [data-action="delete"], .btn-outline-danger, .text-danger[href*="delete"]').length);

        // 监听多种可能的删除按钮选择器
        var deleteSelectors = [
            '.grid-row-delete',
            '[data-action="delete"]',
            '.btn-outline-danger[href*="/"]',
            '.text-danger[href*="/"]',
            'a[href*="delete"]',
            '.fa-trash'
        ].join(', ');

        $(document).off('click.delete-refresh', deleteSelectors).on('click.delete-refresh', deleteSelectors, function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var $this = $(this);
            var deleteUrl = $this.attr('href') || $this.data('url');
            var $row = $this.closest('tr');

            // 调试信息
            console.log('Delete button clicked:', $this[0], 'URL:', deleteUrl);

            // 如果URL是javascript:void(0)，说明已经被处理过了
            if (!deleteUrl || deleteUrl === 'javascript:void(0)' || deleteUrl.indexOf('javascript:') === 0) {
                console.log('Invalid delete URL, skipping');
                return false;
            }

            // 检查是否是删除相关的URL
            if (deleteUrl.indexOf('/delete') === -1 && !deleteUrl.match(/\/\d+$/)) {
                console.log('URL does not appear to be a delete URL, skipping');
                return true; // 让其他处理器处理
            }
            
            Dcat.confirm('确定要删除这条记录吗？', '删除后将无法恢复！', function() {
                // 显示加载状态
                Dcat.loading();
                
                $.ajax({
                    url: deleteUrl,
                    type: 'DELETE',
                    data: {
                        _token: Dcat.token
                    },
                    success: function(response) {
                        Dcat.loading(false);
                        
                        // 检查响应格式
                        if (response && (response.status === true || response.status !== false)) {
                            Dcat.success(response.message || '删除成功');
                            // 立即刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 500);
                        } else {
                            Dcat.error(response.message || '删除失败');
                        }
                    },
                    error: function(xhr) {
                        Dcat.loading(false);
                        
                        var response = xhr.responseJSON;
                        var message = '删除失败';
                        
                        if (response && response.message) {
                            message = response.message;
                        } else if (xhr.status === 404) {
                            message = '记录不存在';
                        } else if (xhr.status === 403) {
                            message = '没有删除权限';
                        } else if (xhr.status >= 500) {
                            message = '服务器错误';
                        }
                        
                        Dcat.error(message);
                    }
                });
            });
            
            return false;
        });
        
        // 监听自定义删除按钮（如website-delete-btn）
        $(document).off('click.custom-delete', '.website-delete-btn').on('click.custom-delete', '.website-delete-btn', function(e) {
            e.preventDefault();
            
            var $this = $(this);
            var url = $this.data('url');
            var host = $this.data('host') || '该记录';
            
            Dcat.confirm('确定要删除 ' + host + ' 吗？', '删除后将无法恢复！', function() {
                Dcat.loading();
                
                $.ajax({
                    url: url,
                    type: 'DELETE',
                    data: {
                        _token: Dcat.token
                    },
                    success: function(response) {
                        Dcat.loading(false);
                        
                        if (response && (response.status === true || response.status !== false)) {
                            Dcat.success(response.message || '删除成功');
                            // 立即刷新页面
                            setTimeout(function() {
                                location.reload();
                            }, 500);
                        } else {
                            Dcat.error(response.message || '删除失败');
                        }
                    },
                    error: function(xhr) {
                        Dcat.loading(false);
                        
                        var response = xhr.responseJSON;
                        var message = response && response.message ? response.message : '删除失败';
                        Dcat.error(message);
                    }
                });
            });
        });
    }

    // 页面加载完成后初始化
    $(document).ready(function() {
        initDeleteRefresh();
    });

    // PJAX完成后重新初始化
    $(document).on('pjax:complete', function() {
        initDeleteRefresh();
    });

    // 导出到全局，供其他脚本使用
    window.DeleteRefresh = {
        init: initDeleteRefresh
    };
})();
