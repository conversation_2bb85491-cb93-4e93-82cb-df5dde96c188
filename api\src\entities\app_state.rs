use crate::database::mongo::MongoClient;
use crate::database::redis::RedisClient;
use crate::util::region_timezone_context::RegionTimezoneContext;
use sqlx::{MySql, Pool};

use super::{SystemConfig, RegionManager};

#[derive(Clone)]
pub struct AppState {
    pub mysql: Pool<MySql>,
    pub redis: RedisClient,
    pub mongo: MongoClient,
    pub system_config: SystemConfig,
    pub region_manager: RegionManager,
    pub region_timezone_context: RegionTimezoneContext,
}
