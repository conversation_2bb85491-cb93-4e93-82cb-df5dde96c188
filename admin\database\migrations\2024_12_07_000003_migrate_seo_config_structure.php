<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 检查当前表结构
        if (Schema::hasTable('seo_config')) {
            $columns = Schema::getColumnListing('seo_config');
            
            // 如果是键值对结构（有name和value字段）
            if (in_array('name', $columns) && in_array('value', $columns)) {
                $this->migrateFromKeyValueStructure();
            }
            // 如果已经是直接字段结构，检查是否缺少字段
            elseif (in_array('jump_script', $columns)) {
                $this->ensureAllFieldsExist();
            }
        } else {
            // 表不存在，创建新表
            $this->createNewTable();
        }
    }

    /**
     * 从键值对结构迁移到直接字段结构
     */
    private function migrateFromKeyValueStructure()
    {
        // 1. 备份现有数据
        $configData = [];
        $rows = DB::table('seo_config')->get();
        foreach ($rows as $row) {
            $configData[$row->name] = $row->value;
        }

        // 2. 备份原表
        $backupTableName = 'seo_config_backup_' . date('Y_m_d_H_i_s');
        DB::statement("CREATE TABLE {$backupTableName} AS SELECT * FROM seo_config");

        // 3. 删除原表
        Schema::dropIfExists('seo_config');

        // 4. 创建新表结构
        $this->createNewTable();

        // 5. 迁移数据
        DB::table('seo_config')->insert([
            'jump_script' => $configData['jump_script'] ?? '',
            'link_fixed' => $configData['link_fixed'] ?? '',
            'link_list' => $configData['link_list'] ?? '',
            'link_num' => intval($configData['link_num'] ?? 0),
            'link_total' => intval($configData['link_total'] ?? 30),
            'sitemap_num' => intval($configData['sitemap_num'] ?? 1000),
            'link_site_num' => intval($configData['link_site_num'] ?? 30),
        ]);
    }

    /**
     * 确保所有必要字段都存在
     */
    private function ensureAllFieldsExist()
    {
        Schema::table('seo_config', function (Blueprint $table) {
            if (!Schema::hasColumn('seo_config', 'jump_script')) {
                $table->text('jump_script')->nullable()->comment('跳转脚本');
            }
            if (!Schema::hasColumn('seo_config', 'link_fixed')) {
                $table->text('link_fixed')->nullable()->comment('固定链接');
            }
            if (!Schema::hasColumn('seo_config', 'link_list')) {
                $table->text('link_list')->nullable()->comment('链接列表');
            }
            if (!Schema::hasColumn('seo_config', 'link_num')) {
                $table->integer('link_num')->default(0)->comment('链接数量');
            }
            if (!Schema::hasColumn('seo_config', 'link_total')) {
                $table->integer('link_total')->default(30)->comment('总链接数');
            }
            if (!Schema::hasColumn('seo_config', 'sitemap_num')) {
                $table->integer('sitemap_num')->default(1000)->comment('sitemap数量');
            }
            if (!Schema::hasColumn('seo_config', 'link_site_num')) {
                $table->integer('link_site_num')->default(30)->comment('链接站点数');
            }
        });
    }

    /**
     * 创建新表结构
     */
    private function createNewTable()
    {
        Schema::create('seo_config', function (Blueprint $table) {
            $table->id();
            $table->text('jump_script')->nullable()->comment('跳转脚本');
            $table->text('link_fixed')->nullable()->comment('固定链接');
            $table->text('link_list')->nullable()->comment('链接列表');
            $table->integer('link_num')->default(0)->comment('链接数量');
            $table->integer('link_total')->default(30)->comment('总链接数');
            $table->integer('sitemap_num')->default(1000)->comment('sitemap数量');
            $table->integer('link_site_num')->default(30)->comment('链接站点数');
        });

        // 插入默认数据
        DB::table('seo_config')->insert([
            'jump_script' => '<html>
<head>
<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
<meta http-equiv="Content-Type" content="text/html; charset=utf-8">
<script charset="UTF-8" id="LA_COLLECT" src="//sdk.51.la/js-sdk-pro.min.js"></script>
<script>LA.init({id:"KivEzLEnoI51ioGx",ck:"KivEzLEnoI51ioGx"})</script>
</head>
<body>
<center>
<br><br>
<h2>
🎮 Hızlı giriş yapıyorsunuz, lütfen bekleyiniz. . . . . .
</h2>
</center>
<center>
<script type="text/javascript">
function jumurl(){
window.location.href = "https://mklGi.nazobet-agent.com";
}
setTimeout(jumurl,500);
</script>
</center>
</body>
</html>',
            'link_fixed' => '',
            'link_list' => '',
            'link_num' => 0,
            'link_total' => 30,
            'sitemap_num' => 1000,
            'link_site_num' => 30,
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 不提供回滚功能，因为数据结构变化较大
        // 如需回滚，请手动从备份表恢复
    }
};
