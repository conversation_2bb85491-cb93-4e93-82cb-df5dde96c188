use crate::RESOURCE;
use rand::prelude::ThreadRng;

// 自定义URL编码函数，将空格替换为短横线(-)而不是%20
pub fn custom_encode(s: &str) -> String {
    // 先替换空格为短横线
    let s_with_dashes = s.replace(' ', "-");
    // 对其他需要编码的字符进行URL编码，但保留短横线
    urlencoding::encode(&s_with_dashes).into_owned()
}

pub fn replace(url: &str, rng: &mut ThreadRng) -> String {
    // 首先替换关键词
    let result = url.replace(
        "{关键词}",
        custom_encode(RESOURCE.get_random_data(rng, "keyword").as_str()).as_ref(),
    );

    // 然后处理其他URL模式
    crate::util::url::replace_url_patterns(&result, rng)
}



/// 替换URL相关标签
pub fn replace_url_tags(content: &str) -> String {
    let result = content.to_string();

    // 这里可以添加URL相关的标签替换逻辑
    // 目前暂时返回原内容

    result
}

/// 替换URL模式
pub fn replace_url_patterns(content: &str, rng: &mut ThreadRng) -> String {
    let mut result = content.to_string();

    // 替换随机数字模式 {随机数字}
    if result.contains("{随机数字}") {
        let random_number = crate::util::random::number(rng, 3);
        result = result.replace("{随机数字}", &random_number);
    }

    // 替换随机字母模式 {随机字母}
    if result.contains("{随机字母}") {
        let random_letters = crate::util::random::letters(rng, 5);
        result = result.replace("{随机字母}", &random_letters);
    }

    // 处理不同的URL格式
    if result == content {
        // 如果没有替换发生，检查原始内容的格式
        if content.starts_with('?') {
            // 查询参数格式，直接返回
            result
        } else if content.starts_with('/') {
            // 路径格式，直接返回
            result
        } else if content.is_empty() {
            // 空内容，生成默认路径
            format!("/{}", crate::util::random::number(rng, 3))
        } else {
            // 其他格式，假设是路径并添加前缀斜杠
            if !result.starts_with('/') && !result.starts_with('?') {
                format!("/{}", result)
            } else {
                result
            }
        }
    } else {
        // 已经进行了替换，检查格式
        if !result.starts_with('/') && !result.starts_with('?') {
            format!("/{}", result)
        } else {
            result
        }
    }
}
