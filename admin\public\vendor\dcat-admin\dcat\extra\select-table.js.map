{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./resources/assets/dcat/extra/select-table.js"], "names": ["installedModules", "__webpack_require__", "moduleId", "exports", "module", "i", "l", "modules", "call", "m", "c", "d", "name", "getter", "o", "Object", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "prototype", "hasOwnProperty", "p", "s", "w", "SelectTable", "options", "$", "extend", "dialog", "container", "input", "button", "cancel", "table", "multiple", "max", "values", "lang", "exceed_max_item", "Dcat", "this", "$input", "selected", "init", "self", "labels", "resetSelected", "document", "on", "$dialog", "$button", "find", "$cancel", "getSelectedRows", "set<PERSON><PERSON><PERSON>", "render", "trigger", "checkbox", "getCheckbox", "remove", "$this", "id", "data", "label", "checked", "length", "prop", "warning", "each", "parents", "css", "current", "keys", "get<PERSON><PERSON><PERSON>", "ids", "push", "box", "placeholder", "option", "addClass", "removeClass", "html", "hasClass", "unshift", "join", "$tags", "removeAll", "deleteKey", "parent", "renderMultiple", "text", "append", "renderDefault", "val", "results", "String", "split", "grid", "opts", "window"], "mappings": "aACE,IAAIA,EAAmB,GAGvB,SAASC,EAAoBC,GAG5B,GAAGF,EAAiBE,GACnB,OAAOF,EAAiBE,GAAUC,QAGnC,IAAIC,EAASJ,EAAiBE,GAAY,CACzCG,EAAGH,EACHI,GAAG,EACHH,QAAS,IAUV,OANAI,EAAQL,GAAUM,KAAKJ,EAAOD,QAASC,EAAQA,EAAOD,QAASF,GAG/DG,EAAOE,GAAI,EAGJF,EAAOD,QAKfF,EAAoBQ,EAAIF,EAGxBN,EAAoBS,EAAIV,EAGxBC,EAAoBU,EAAI,SAASR,EAASS,EAAMC,GAC3CZ,EAAoBa,EAAEX,EAASS,IAClCG,OAAOC,eAAeb,EAASS,EAAM,CAAEK,YAAY,EAAMC,IAAKL,KAKhEZ,EAAoBkB,EAAI,SAAShB,GACX,oBAAXiB,QAA0BA,OAAOC,aAC1CN,OAAOC,eAAeb,EAASiB,OAAOC,YAAa,CAAEC,MAAO,WAE7DP,OAAOC,eAAeb,EAAS,aAAc,CAAEmB,OAAO,KAQvDrB,EAAoBsB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQrB,EAAoBqB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,iBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKX,OAAOY,OAAO,MAGvB,GAFA1B,EAAoBkB,EAAEO,GACtBX,OAAOC,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOrB,EAAoBU,EAAEe,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRzB,EAAoB6B,EAAI,SAAS1B,GAChC,IAAIS,EAAST,GAAUA,EAAOqB,WAC7B,WAAwB,OAAOrB,EAAgB,SAC/C,WAA8B,OAAOA,GAEtC,OADAH,EAAoBU,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRZ,EAAoBa,EAAI,SAASiB,EAAQC,GAAY,OAAOjB,OAAOkB,UAAUC,eAAe1B,KAAKuB,EAAQC,IAGzG/B,EAAoBkC,EAAI,IAIjBlC,EAAoBA,EAAoBmC,EAAI,I,wDClFrD,SAAWC,GACP,SAASC,EAAYC,GACjBA,EAAUC,EAAEC,OAAO,CACfC,OAAQ,KACRC,UAAW,KACXC,MAAO,KACPC,OAAQ,cACRC,OAAQ,cACRC,MAAO,eACPC,UAAU,EACVC,IAAK,EACLC,OAAQ,GACRC,KAAM,CACFC,gBAAiBC,KAAKF,KAAKC,iBAAmB,gBAEnDb,GAEQe,KAENf,QAAUA,EAFJe,KAGNC,OAASf,EAAED,EAAQK,OAHbU,KAINE,SAAW,GAJLF,KAMNG,OAGTnB,EAAYL,UAAY,CACpBwB,KADoB,WAEhB,IAAIC,EAAOJ,KACPf,EAAUmB,EAAKnB,QACfW,EAASX,EAAQW,OAIrB,IAAK,IAAI7C,KAFTqD,EAAKC,OAAS,GAEAT,EACVQ,EAAKC,OAAOT,EAAO7C,GAAP,IAAmB6C,EAAO7C,GAAP,MAInCqD,EAAKE,gBAELpB,EAAEqB,UAAUC,GAAG,eAAgBvB,EAAQG,QAAQ,WAC3CgB,EAAKK,QAAUvB,EAAED,EAAQG,QACzBgB,EAAKM,QAAUN,EAAKK,QAAQE,KAAK1B,EAAQM,QACzCa,EAAKQ,QAAUR,EAAKK,QAAQE,KAAK1B,EAAQO,QAGzCY,EAAKM,QAAQF,GAAG,SAAS,WACrB,IAAIN,EAAWE,EAAKS,kBAEpBT,EAAKU,QAAQZ,EAAS,IAEtBE,EAAKW,OAAOb,EAAS,IAErBE,EAAKK,QAAQO,QAAQ,gBAGrBZ,EAAKE,mBAITF,EAAKQ,QAAQJ,GAAG,SAAS,WACrBJ,EAAKK,QAAQO,QAAQ,mBAIzBZ,EAAK7B,OAGL6B,EAAKE,mBAITF,EAAKW,OAAOnB,IAGhBrB,KAlDoB,WAmDhB,IAAI6B,EAAOJ,KACPf,EAAUmB,EAAKnB,QAGnBmB,EAAKK,QAAQE,KAAK1B,EAAQQ,OAAOe,GAAG,gBAAgB,WAChD,IAAIS,EAAWb,EAAKc,cAEdjC,EAAQS,UAEVR,EAAEc,MAAMW,KAAK,yBAAyBQ,SAG1CF,EAAST,GAAG,UAAU,WAClB,IAAIY,EAAQlC,EAAEc,MACVqB,EAAKD,EAAME,KAAK,MAChBC,EAAQH,EAAME,KAAK,SAEvB,GAAItB,KAAKwB,SAOL,GANMvC,EAAQS,WACVU,EAAKF,SAAW,IAEpBE,EAAKF,SAASmB,GAAM,CAACA,GAAIA,EAAIE,MAAOA,GAGhCtC,EAAQU,KAAQS,EAAKS,kBAAkB,GAAGY,OAASxC,EAAQU,IAI3D,OAHAyB,EAAMM,KAAK,WAAW,UACftB,EAAKF,SAASmB,GAEdtB,KAAK4B,QAAQvB,EAAKnB,QAAQY,KAAKC,6BAGnCM,EAAKF,SAASmB,GAGnBpC,EAAQS,UACNM,KAAKwB,SAELP,EAASW,MAAK,WACV,IAAIR,EAAQlC,EAAEc,MAEVoB,EAAME,KAAK,OAASD,IACpBD,EAAMM,KAAK,WAAW,GACtBN,EAAMS,QAAQ,MAAMC,IAAI,mBAAoB,WAQhEb,EAASW,MAAK,WACV,IAAIR,EAAQlC,EAAEc,MACV+B,EAAUX,EAAME,KAAK,MAKzB,IAAK,IAAIvE,KAFTqD,EAAKC,OAAO0B,GAAWX,EAAME,KAAK,SAEpBlB,EAAKF,SACX6B,GAAWhF,GACXqE,EAAMM,KAAK,WAAW,GAAMV,QAAQ,UAM5CI,EAAMJ,QAAQ,iBAM1BV,cA1HoB,WA2HhB,IACI0B,EADOhC,KACKiC,UAIhB,IAAK,IAAIlF,KALEiD,KAGNE,SAAW,GAEF8B,EALHhC,KAMFE,SAAS8B,EAAKjF,IAAM,CAACsE,GAAIW,EAAKjF,GAAIwE,MANhCvB,KAM4CK,OAAO2B,EAAKjF,MAIvEmE,YArIoB,WAsIhB,OAAOlB,KAAKS,QAAQE,KAAK,iDAG7BE,gBAzIoB,WA0IhB,IACIX,EAAW,GACXgC,EAAM,GAEV,IAAK,IAAInF,KAJEiD,KAIQE,SAJRF,KAKIE,SAASnD,KAIpBmF,EAAIC,KAAKpF,GACTmD,EAASiC,KAVFnC,KAUYE,SAASnD,KAGhC,MAAO,CAACmD,EAAUgC,IAGtBnB,OA1JoB,SA0Jbb,GACH,IACIjB,EADOe,KACQf,QACfmD,EAAMlD,EAAED,EAAQI,WAChBgD,EAAcD,EAAIzB,KAAK,iBACvB2B,EAASF,EAAIzB,KAAK,WAEtB,OAAMT,GAAcA,EAASuB,QAW7BY,EAAYE,SAAS,UACrBD,EAAOE,YAAY,UAEbvD,EAAQS,SAmCtB,SAAwBQ,EAAUE,EAAMnB,GACpC,IAAIwD,EAAO,GACPL,EAAMlD,EAAED,EAAQI,WAChBgD,EAAcD,EAAIzB,KAAK,iBACvB2B,EAASF,EAAIzB,KAAK,WAEhByB,EAAIM,SAAS,YACfN,EAAIG,SAAS,iFAIjB,IAAK,IAAIxF,KAFTqF,EAAII,YAAY,gBAEFtC,EACVuC,EAAKN,KAAL,uDACNjC,EAASnD,GAAT,MADM,2BACiCmD,EAASnD,GAAT,GADjC,6FAKJ0F,EAAKE,QAAQ,8DAEbF,EAAO,0GAAH,OAC+BA,EAAKG,KAAK,IADzC,mBAIJ,IAAIC,EAAQ3D,EAAEuD,GAgBd,SAASK,IACLR,EAAOG,KAAK,IACZJ,EAAYG,YAAY,UACxBF,EAAOC,SAAS,UAEhBH,EAAIG,SAAS,gBAEbnC,EAAKU,QAAQ,IArBjBwB,EAAOG,KAAKI,GAEZA,EAAMlC,KAAK,WAAWH,GAAG,SAAS,WAC9B,IAAIY,EAAQlC,EAAEc,MAEdI,EAAK2C,UAAU3B,EAAME,KAAK,OAE1BF,EAAM4B,SAAS7B,SAETf,EAAK6B,UAAUR,QACjBqB,OAcRD,EAAMlC,KAAK,eAAeH,GAAG,QAASsC,GAhF3BG,CAAe/C,EAxBXF,KAwB2Bf,GAoF9C,SAAuBiB,EAAUE,EAAMnB,GACnC,IAAImD,EAAMlD,EAAED,EAAQI,WAChBgD,EAAcD,EAAIzB,KAAK,iBACvB2B,EAASF,EAAIzB,KAAK,WAElBQ,EAASjC,EAAE,4EAEfoD,EAAOY,KAAKhD,EAAS,GAAT,OACZoC,EAAOa,OAAOhC,GAEdA,EAAOX,GAAG,SAAS,WACfJ,EAAKU,QAAQ,IACbuB,EAAYG,YAAY,UACxBF,EAAOC,SAAS,aApGLa,CAAclD,EArBdF,KAqB8Bf,KAdrCoD,EAAYG,YAAY,UACxBF,EAAOC,SAAS,eAEZtD,EAAQS,UACR0C,EAAIG,SAAS,mBAgBzBzB,QAtLoB,SAsLZkB,GAEJhC,KAAKC,OAAOoD,IAAIrB,EAAKP,OAASO,EAAKY,KAAK,KAAO,IAAI5B,QAAQ,WAG/D+B,UA3LoB,SA2LVzE,GACN,IAAI+E,EAAMrD,KAAKiC,UACXqB,EAAU,GAEd,IAAK,IAAIvG,KAAKsG,EACNA,EAAItG,IAAMuB,GACVgF,EAAQnB,KAAKkB,EAAItG,IAIzBiD,KAAKc,QAAQwC,IAGjBrB,QAxMoB,WAyMhB,IAAIoB,EAAMrD,KAAKC,OAAOoD,MAEtB,OAAMA,EAECE,OAAOF,GAAKG,MAAM,KAFP,KA6E1BzD,KAAK0D,KAAKzE,YAAc,SAAU0E,GAC9B,OAAO,IAAI1E,EAAY0E,IAnT/B,CAqTGC", "file": "/resources/dist/dcat/extra/select-table.js", "sourcesContent": [" \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"/\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 10);\n", "(function (w) {\n    function SelectTable(options) {\n        options = $.extend({\n            dialog: null,\n            container: null,\n            input: null,\n            button: '.submit-btn',\n            cancel: '.cancel-btn',\n            table: '.async-table',\n            multiple: false,\n            max: 0,\n            values: [],\n            lang: {\n                exceed_max_item: Dcat.lang.exceed_max_item || '已超出最大可选择的数量',\n            },\n        }, options);\n\n        let self = this;\n\n        self.options = options;\n        self.$input = $(options.input);\n        self.selected = {}; // 保存临时选中的ID\n\n        self.init();\n    }\n\n    SelectTable.prototype = {\n        init() {\n            let self = this,\n                options = self.options,\n                values = options.values;\n\n            self.labels = {};\n\n            for (let i in values) {\n                self.labels[values[i]['id']] = values[i]['label']\n            }\n\n            // 保存临时选中的值\n            self.resetSelected();\n\n            $(document).on('dialog:shown', options.dialog, function () {\n                self.$dialog = $(options.dialog);\n                self.$button = self.$dialog.find(options.button);\n                self.$cancel = self.$dialog.find(options.cancel);\n\n                // 提交按钮\n                self.$button.on('click', function () {\n                    var selected = self.getSelectedRows();\n\n                    self.setKeys(selected[1]);\n\n                    self.render(selected[0]);\n\n                    self.$dialog.trigger('dialog:close');\n\n                    // 重置已选中数据\n                    self.resetSelected();\n                });\n\n                // 取消按钮\n                self.$cancel.on('click', function () {\n                    self.$dialog.trigger('dialog:close');\n                });\n\n                // 绑定相关事件\n                self.bind();\n\n                // 重置已选中数据\n                self.resetSelected();\n            });\n\n            // 渲染选中的数据\n            self.render(values);\n        },\n\n        bind() {\n            let self = this,\n                options = self.options;\n\n            // 表格加载完成事件\n            self.$dialog.find(options.table).on('table:loaded', function () {\n                let checkbox = self.getCheckbox();\n\n                if (! options.multiple) {\n                    // 移除全选按钮\n                    $(this).find('.checkbox-grid-header').remove();\n                }\n\n                checkbox.on('change', function () {\n                    let $this = $(this),\n                        id = $this.data('id'),\n                        label = $this.data('label');\n\n                    if (this.checked) {\n                        if (! options.multiple) {\n                            self.selected = {};\n                        }\n                        self.selected[id] = {id: id, label: label};\n\n                        // 多选\n                        if (options.max && (self.getSelectedRows()[0].length > options.max)) {\n                            $this.prop('checked', false);\n                            delete self.selected[id];\n\n                            return Dcat.warning(self.options.lang.exceed_max_item);\n                        }\n                    } else {\n                        delete self.selected[id];\n                    }\n\n                    if (! options.multiple) {\n                        if (this.checked) {\n                            // 单选效果\n                            checkbox.each(function () {\n                                let $this = $(this);\n\n                                if ($this.data('id') != id) {\n                                    $this.prop('checked', false);\n                                    $this.parents('tr').css('background-color', '');\n                                }\n                            });\n                        }\n                    }\n                });\n\n                // 选中默认选项\n                checkbox.each(function () {\n                    let $this = $(this),\n                        current = $this.data('id');\n\n                    // 保存label字段\n                    self.labels[current] = $this.data('label');\n\n                    for (let i in self.selected) {\n                        if (current == i) {\n                            $this.prop('checked', true).trigger('change');\n\n                            continue;\n                        }\n                    }\n\n                    $this.trigger('change');\n                });\n            })\n        },\n\n        // 重置已选中数据\n        resetSelected() {\n            let self = this,\n                keys = self.getKeys();\n\n            self.selected = {};\n\n            for (let i in keys) {\n                self.selected[keys[i]] = {id: keys[i], label: self.labels[keys[i]]};\n            }\n        },\n\n        getCheckbox() {\n            return this.$dialog.find('.checkbox-grid-column input[type=\"checkbox\"]');\n        },\n\n        getSelectedRows() {\n            let self = this,\n                selected = [],\n                ids = [];\n\n            for (let i in self.selected) {\n                if (! self.selected[i]) {\n                    continue;\n                }\n\n                ids.push(i);\n                selected.push(self.selected[i])\n            }\n\n            return [selected, ids];\n        },\n\n        render(selected) {\n            let self = this,\n                options = self.options,\n                box = $(options.container),\n                placeholder = box.find('.default-text'),\n                option = box.find('.option');\n\n            if (! selected || ! selected.length) {\n                placeholder.removeClass('d-none');\n                option.addClass('d-none');\n\n                if (options.multiple) {\n                    box.addClass('form-control');\n                }\n\n                return;\n            }\n\n            placeholder.addClass('d-none');\n            option.removeClass('d-none');\n\n            if (! options.multiple) {\n                return renderDefault(selected, self, options);\n            }\n\n            return renderMultiple(selected, self, options);\n        },\n\n        setKeys(keys) {\n            // 手动触发change事件，方便监听值变化\n            this.$input.val(keys.length ? keys.join(',') : '').trigger('change');\n        },\n\n        deleteKey(key) {\n            let val = this.getKeys(),\n                results = [];\n\n            for (let i in val) {\n                if (val[i] != key) {\n                    results.push(val[i])\n                }\n            }\n\n            this.setKeys(results)\n        },\n\n        getKeys() {\n            let val = this.$input.val();\n\n            if (! val) return [];\n\n            return String(val).split(',');\n        },\n    };\n\n    // 多选\n    function renderMultiple(selected, self, options) {\n        let html = [],\n            box = $(options.container),\n            placeholder = box.find('.default-text'),\n            option = box.find('.option');\n\n        if (! box.hasClass('select2')) {\n            box.addClass('select2 select2-container select2-container--default select2-container--below');\n        }\n        box.removeClass('form-control');\n\n        for (let i in selected) {\n            html.push(`<li class=\"select2-selection__choice\" >\n    ${selected[i]['label']} <span data-id=\"${selected[i]['id']}\" class=\"select2-selection__choice__remove remove \" role=\"presentation\"> ×</span>\n</li>`);\n        }\n\n        html.unshift('<span class=\"select2-selection__clear remove-all\">×</span>');\n\n        html = `<span class=\"select2-selection select2-selection--multiple\">\n <ul class=\"select2-selection__rendered\">${html.join('')}</ul>\n </span>`;\n\n        var $tags = $(html);\n\n        option.html($tags);\n\n        $tags.find('.remove').on('click', function () {\n            var $this = $(this);\n\n            self.deleteKey($this.data('id'));\n\n            $this.parent().remove();\n\n            if (! self.getKeys().length) {\n                removeAll();\n            }\n        });\n\n        function removeAll() {\n            option.html('');\n            placeholder.removeClass('d-none');\n            option.addClass('d-none');\n\n            box.addClass('form-control');\n\n            self.setKeys([]);\n        }\n\n        $tags.find('.remove-all').on('click', removeAll);\n    }\n\n    // 单选\n    function renderDefault(selected, self, options) {\n        let box = $(options.container),\n            placeholder = box.find('.default-text'),\n            option = box.find('.option');\n\n        var remove = $(\"<div class='pull-right ' style='font-weight:bold;cursor:pointer'>×</div>\");\n\n        option.text(selected[0]['label']);\n        option.append(remove);\n\n        remove.on('click', function () {\n            self.setKeys([]);\n            placeholder.removeClass('d-none');\n            option.addClass('d-none');\n        });\n    }\n\n    Dcat.grid.SelectTable = function (opts) {\n        return new SelectTable(opts)\n    };\n})(window)"], "sourceRoot": ""}