use actix_web::{get, post, put, delete, web, HttpResponse, Result};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

use crate::entities::AppState;
use crate::util::region_timezone_context::RegionTimezoneUtils;

/// 地区时区信息响应
#[derive(Debug, Serialize)]
pub struct RegionTimezoneResponse {
    pub region_code: String,
    pub timezone: String,
    pub current_time: String,
    pub timestamp: i64,
}

/// 地区时区更新请求
#[derive(Debug, Deserialize)]
pub struct UpdateRegionTimezoneRequest {
    pub timezone: String,
}

/// 批量地区时区更新请求
#[derive(Debug, Deserialize)]
pub struct BatchUpdateRegionTimezoneRequest {
    pub updates: HashMap<String, String>,
}

/// 获取所有地区时区信息
#[get("/region-timezones")]
pub async fn get_all_region_timezones(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let region_timezones = app_state.region_timezone_context.get_all_region_timezones().await;
    let mut responses = Vec::new();
    
    for (region_code, timezone) in region_timezones {
        let current_time = RegionTimezoneUtils::format_time_for_region(
            &app_state.region_timezone_context,
            &region_code,
            "%Y-%m-%d %H:%M:%S"
        ).await;
        
        let timestamp = RegionTimezoneUtils::timestamp_for_region_code(
            &app_state.region_timezone_context,
            &region_code
        ).await;
        
        responses.push(RegionTimezoneResponse {
            region_code,
            timezone,
            current_time,
            timestamp,
        });
    }
    
    Ok(HttpResponse::Ok().json(responses))
}

/// 获取特定地区时区信息
#[get("/region-timezones/{region_code}")]
pub async fn get_region_timezone(
    app_state: web::Data<AppState>,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();
    let timezone = app_state.region_timezone_context.get_region_timezone(&region_code).await;
    
    let current_time = RegionTimezoneUtils::format_time_for_region(
        &app_state.region_timezone_context,
        &region_code,
        "%Y-%m-%d %H:%M:%S"
    ).await;
    
    let timestamp = RegionTimezoneUtils::timestamp_for_region_code(
        &app_state.region_timezone_context,
        &region_code
    ).await;
    
    let response = RegionTimezoneResponse {
        region_code,
        timezone,
        current_time,
        timestamp,
    };
    
    Ok(HttpResponse::Ok().json(response))
}

/// 更新地区时区
#[put("/region-timezones/{region_code}")]
pub async fn update_region_timezone(
    app_state: web::Data<AppState>,
    path: web::Path<String>,
    request: web::Json<UpdateRegionTimezoneRequest>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();
    let new_timezone = &request.timezone;
    
    // 验证时区是否有效
    if !app_state.region_timezone_context.is_valid_timezone(new_timezone) {
        return Ok(HttpResponse::BadRequest().json(serde_json::json!({
            "error": "Invalid timezone",
            "timezone": new_timezone
        })));
    }
    
    // 更新时区
    app_state.region_timezone_context
        .update_region_timezone(region_code.clone(), new_timezone.clone())
        .await;
    
    // 同时更新数据库中的地区时区
    let update_result = sqlx::query(
        "UPDATE seo_regions SET timezone = ? WHERE code = ?"
    )
    .bind(new_timezone)
    .bind(&region_code)
    .execute(&app_state.mysql)
    .await;
    
    match update_result {
        Ok(_) => {
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "message": "Region timezone updated successfully",
                "region_code": region_code,
                "timezone": new_timezone
            })))
        }
        Err(e) => {
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to update database",
                "details": e.to_string()
            })))
        }
    }
}

/// 批量更新地区时区
#[post("/region-timezones/batch-update")]
pub async fn batch_update_region_timezones(
    app_state: web::Data<AppState>,
    request: web::Json<BatchUpdateRegionTimezoneRequest>,
) -> Result<HttpResponse> {
    let mut success_count = 0;
    let mut error_count = 0;
    let mut errors = Vec::new();
    
    for (region_code, timezone) in &request.updates {
        // 验证时区
        if !app_state.region_timezone_context.is_valid_timezone(timezone) {
            error_count += 1;
            errors.push(format!("Invalid timezone for region {}: {}", region_code, timezone));
            continue;
        }
        
        // 更新内存中的时区
        app_state.region_timezone_context
            .update_region_timezone(region_code.clone(), timezone.clone())
            .await;
        
        // 更新数据库
        let update_result = sqlx::query(
            "UPDATE seo_regions SET timezone = ? WHERE code = ?"
        )
        .bind(timezone)
        .bind(region_code)
        .execute(&app_state.mysql)
        .await;
        
        match update_result {
            Ok(_) => success_count += 1,
            Err(e) => {
                error_count += 1;
                errors.push(format!("Failed to update database for region {}: {}", region_code, e));
            }
        }
    }
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Batch update completed",
        "success_count": success_count,
        "error_count": error_count,
        "errors": errors
    })))
}

/// 删除地区时区
#[delete("/region-timezones/{region_code}")]
pub async fn delete_region_timezone(
    app_state: web::Data<AppState>,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();
    
    // 从内存中移除
    app_state.region_timezone_context.remove_region_timezone(&region_code).await;
    
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Region timezone removed from cache",
        "region_code": region_code
    })))
}

/// 获取地区时区统计信息
#[get("/region-timezones/stats")]
pub async fn get_region_timezone_stats(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let stats = app_state.region_timezone_context.get_stats().await;
    Ok(HttpResponse::Ok().json(stats))
}

/// 重新加载地区时区配置
#[post("/region-timezones/reload")]
pub async fn reload_region_timezones(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    use crate::entities::Region;
    
    match Region::get_active_regions(&app_state.mysql).await {
        Ok(regions) => {
            app_state.region_timezone_context.initialize_from_regions(regions).await;
            let stats = app_state.region_timezone_context.get_stats().await;
            
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "message": "Region timezones reloaded successfully",
                "stats": stats
            })))
        }
        Err(e) => {
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "Failed to reload region timezones",
                "details": e.to_string()
            })))
        }
    }
}

/// 测试地区时区功能
#[get("/region-timezones/{region_code}/test")]
pub async fn test_region_timezone(
    app_state: web::Data<AppState>,
    path: web::Path<String>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();

    let timezone = app_state.region_timezone_context.get_region_timezone(&region_code).await;
    let current_time = RegionTimezoneUtils::now_for_region_code(
        &app_state.region_timezone_context,
        &region_code
    ).await;

    let timestamp = current_time.timestamp();
    let (day_start, day_end) = RegionTimezoneUtils::get_region_day_boundaries(
        &app_state.region_timezone_context,
        &region_code
    ).await;

    let cache_expiration_1h = RegionTimezoneUtils::calculate_cache_expiration_for_region(
        &app_state.region_timezone_context,
        &region_code,
        1
    ).await;

    let cache_expiration_24h = RegionTimezoneUtils::calculate_cache_expiration_for_region(
        &app_state.region_timezone_context,
        &region_code,
        24
    ).await;

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "region_code": region_code,
        "timezone": timezone,
        "current_time": current_time.format("%Y-%m-%d %H:%M:%S %Z").to_string(),
        "timestamp": timestamp,
        "day_boundaries": {
            "start": day_start,
            "end": day_end
        },
        "cache_expiration": {
            "1_hour": cache_expiration_1h,
            "24_hours": cache_expiration_24h
        }
    })))
}



/// 配置地区时区管理路由
pub fn config_region_timezone_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/api/timezone")
            .service(get_all_region_timezones)
            .service(get_region_timezone)
            .service(update_region_timezone)
            .service(batch_update_region_timezones)
            .service(delete_region_timezone)
            .service(get_region_timezone_stats)
            .service(reload_region_timezones)
            .service(test_region_timezone)
    );
}
