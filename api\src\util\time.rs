use chrono::{DateTime, Local, Utc, TimeZone};
// 使用 pub use 直接导入并重新导出 Tz 类型
pub use chrono_tz::Tz;
use lazy_static::lazy_static;
use std::sync::RwLock;
use std::env;

// 全局时区配置
lazy_static! {
    /// 全局默认时区，在程序启动时从环境变量加载
    static ref DEFAULT_TIMEZONE: RwLock<Tz> = RwLock::new(load_timezone_from_env());
}

/// 从环境变量加载时区配置
/// 
/// 查找顺序:
/// 1. TIME_ZONE 环境变量
/// 2. 如果无法解析或不存在，则使用UTC
fn load_timezone_from_env() -> Tz {
    let timezone_str = env::var("TIME_ZONE").unwrap_or_else(|_| "UTC".to_string());
    
    match timezone_str.parse::<Tz>() {
        Ok(tz) => {
            // 成功解析时区
            tz
        },
        Err(_) => {
            // 无法解析时区，回退到UTC
            eprintln!("Warning: Could not parse timezone '{}' from environment. Using UTC instead.", timezone_str);
            Tz::UTC
        }
    }
}

/// 设置全局默认时区
/// 
/// # 参数
/// 
/// * `timezone`: 时区字符串，例如 "Europe/Istanbul" 或 "America/Sao_Paulo"
/// 
/// # 返回值
/// 
/// 返回设置是否成功
pub fn set_timezone(timezone: &str) -> bool {
    match timezone.parse::<Tz>() {
        Ok(tz) => {
            let mut default_tz = DEFAULT_TIMEZONE.write().unwrap();
            *default_tz = tz;
            true
        },
        Err(_) => {
            eprintln!("Error: Invalid timezone '{}'", timezone);
            false
        }
    }
}

/// 获取当前默认时区
pub fn get_timezone() -> String {
    let default_tz = DEFAULT_TIMEZONE.read().unwrap();
    default_tz.to_string()
}

/// 获取当前默认时区对象
pub fn get_default_timezone() -> Tz {
    let default_tz = DEFAULT_TIMEZONE.read().unwrap();
    *default_tz
}

/// 获取当前时间的DateTime对象（使用全局配置的时区）
///
/// 注意：这个函数主要用于系统级操作和回退场景
/// 对于业务逻辑，建议使用 now_for_region() 函数
pub fn now() -> DateTime<Tz> {
    let tz = get_default_timezone();
    tz.from_utc_datetime(&Utc::now().naive_utc())
}

/// 获取当前时间的DateTime对象（系统本地时区）
/// 注意：为了保持时区一致性，此函数现在返回配置的时区时间，但转换为Local类型
pub fn now_local() -> DateTime<Local> {
    // 获取配置的时区时间，然后转换为Local类型（保持时间点不变）
    now().with_timezone(&Local)
}

/// 获取当前UTC时间的DateTime对象
pub fn now_utc() -> DateTime<Utc> {
    Utc::now()
}

/// 获取当前时间的时间戳（秒）
/// 使用全局配置的时区
///
/// 注意：这个函数主要用于系统级操作和回退场景
/// 对于业务逻辑，建议使用 timestamp_for_region() 函数
pub fn timestamp() -> i64 {
    now().timestamp()
}

/// 获取当前时间的时间戳（毫秒）
/// 使用全局配置的时区
///
/// 注意：这个函数主要用于系统级操作和回退场景
/// 对于业务逻辑，建议使用 timestamp_millis_for_region() 函数
pub fn timestamp_millis() -> i64 {
    now().timestamp_millis()
}

/// 将时间戳（秒）转换为DateTime对象（UTC时区）
pub fn from_timestamp(timestamp: i64) -> DateTime<Utc> {
    DateTime::from_timestamp(timestamp, 0)
        .unwrap_or_else(|| DateTime::from_timestamp(0, 0)
            .unwrap_or_else(|| Utc.timestamp_opt(0, 0).unwrap()))
}

/// 将时间戳（秒）转换为DateTime对象（本地时区类型，但使用配置的时区）
/// 
/// 注意：此函数返回的是配置时区的时间，但类型为 Local
/// 函数名称保持不变只是为了兼容现有代码
pub fn from_timestamp_local(timestamp: i64) -> DateTime<Local> {
    // 获取配置时区的时间，然后转换为 Local 类型（保持时间点不变）
    from_timestamp_tz(timestamp).with_timezone(&Local)
}

/// 将时间戳（秒）转换为DateTime对象（应用时区）
pub fn from_timestamp_tz(timestamp: i64) -> DateTime<Tz> {
    // 使用 unwrap_or 直接提供默认值，避免使用 unwrap
    let utc_time = DateTime::from_timestamp(timestamp, 0)
        .unwrap_or_else(|| DateTime::from_timestamp(0, 0)
            .unwrap_or_else(|| Utc.timestamp_opt(0, 0).unwrap()));
    
    // 使用配置的时区
    let tz = get_default_timezone();
    utc_time.with_timezone(&tz)
}

/// 格式化当前时间为字符串
pub fn format_now(format: &str) -> String {
    now().format(format).to_string()
}

/// 格式化指定时间为字符串
pub fn format_date(time: DateTime<Tz>, format: &str) -> String {
    time.format(format).to_string()
}

/// 格式化UTC时间为字符串（转换为应用时区）
pub fn format_utc_date(time: DateTime<Utc>, format: &str) -> String {
    let app_time = from_timestamp_tz(time.timestamp());
    format_date(app_time, format)
}

/// 将UTC时间转换为当前应用时区
pub fn utc_to_app_timezone(time: DateTime<Utc>) -> DateTime<Tz> {
    let tz = get_default_timezone();
    time.with_timezone(&tz)
}

/// 将时间转换为UTC
pub fn to_utc<T: chrono::TimeZone>(time: DateTime<T>) -> DateTime<Utc> {
    time.with_timezone(&Utc)
}

// ==================== 地区时区支持功能 ====================

/// 根据地区时区获取当前时间
///
/// # 参数
///
/// * `region_timezone`: 地区时区字符串，例如 "America/Sao_Paulo"
///
/// # 返回值
///
/// 返回指定地区时区的当前时间
pub fn now_for_region(region_timezone: &str) -> DateTime<Tz> {
    let tz = region_timezone.parse::<Tz>().unwrap_or_else(|_| {
        eprintln!("Warning: Invalid region timezone '{}', using default timezone", region_timezone);
        get_default_timezone()
    });
    tz.from_utc_datetime(&Utc::now().naive_utc())
}

/// 根据地区时区获取时间戳（秒）
pub fn timestamp_for_region(region_timezone: &str) -> i64 {
    now_for_region(region_timezone).timestamp()
}

/// 根据地区时区获取时间戳（毫秒）
pub fn timestamp_millis_for_region(region_timezone: &str) -> i64 {
    now_for_region(region_timezone).timestamp_millis()
}

/// 将UTC时间转换为指定地区时区
pub fn utc_to_region_timezone(utc_time: DateTime<Utc>, region_timezone: &str) -> DateTime<Tz> {
    let tz = region_timezone.parse::<Tz>().unwrap_or_else(|_| {
        eprintln!("Warning: Invalid region timezone '{}', using default timezone", region_timezone);
        get_default_timezone()
    });
    utc_time.with_timezone(&tz)
}

/// 计算地区时区的缓存过期时间戳
///
/// # 参数
///
/// * `region_timezone`: 地区时区字符串
/// * `expiration_hours`: 过期小时数
///
/// # 返回值
///
/// 返回基于地区时区计算的过期时间戳（秒）
pub fn calculate_region_cache_expiration(region_timezone: &str, expiration_hours: u64) -> i64 {
    let region_now = now_for_region(region_timezone);
    region_now.timestamp() + (expiration_hours * 3600) as i64
}

/// 格式化地区时区时间
pub fn format_region_time(region_timezone: &str, format: &str) -> String {
    now_for_region(region_timezone).format(format).to_string()
}

/// 检查时间戳是否在地区时区下已过期
pub fn is_expired_in_region(timestamp: i64, region_timezone: &str) -> bool {
    let current_timestamp = timestamp_for_region(region_timezone);
    timestamp < current_timestamp
}

/// 获取地区时区的日期边界（当天开始时间戳）
pub fn get_region_day_start_timestamp(region_timezone: &str) -> i64 {
    let region_now = now_for_region(region_timezone);
    let day_start = region_now.date_naive().and_hms_opt(0, 0, 0).unwrap();
    let tz = region_timezone.parse::<Tz>().unwrap_or_else(|_| get_default_timezone());
    tz.from_local_datetime(&day_start).unwrap().timestamp()
}

/// 获取地区时区的日期边界（当天结束时间戳）
pub fn get_region_day_end_timestamp(region_timezone: &str) -> i64 {
    let region_now = now_for_region(region_timezone);
    let day_end = region_now.date_naive().and_hms_opt(23, 59, 59).unwrap();
    let tz = region_timezone.parse::<Tz>().unwrap_or_else(|_| get_default_timezone());
    tz.from_local_datetime(&day_end).unwrap().timestamp()
}

// ==================== 智能时区选择功能 ====================

/// 智能获取时区：优先使用地区时区，回退到全局时区
///
/// # 参数
///
/// * `region_timezone`: 可选的地区时区字符串
///
/// # 返回值
///
/// 返回有效的时区对象
pub fn get_smart_timezone(region_timezone: Option<&str>) -> Tz {
    match region_timezone {
        Some(tz_str) => {
            tz_str.parse::<Tz>().unwrap_or_else(|_| {
                eprintln!("Warning: Invalid region timezone '{}', using global default", tz_str);
                get_default_timezone()
            })
        }
        None => get_default_timezone()
    }
}

/// 智能获取当前时间：优先使用地区时区，回退到全局时区
pub fn now_smart(region_timezone: Option<&str>) -> DateTime<Tz> {
    let tz = get_smart_timezone(region_timezone);
    tz.from_utc_datetime(&Utc::now().naive_utc())
}

/// 智能获取时间戳：优先使用地区时区，回退到全局时区
pub fn timestamp_smart(region_timezone: Option<&str>) -> i64 {
    now_smart(region_timezone).timestamp()
}

/// 智能获取时间戳（毫秒）：优先使用地区时区，回退到全局时区
pub fn timestamp_millis_smart(region_timezone: Option<&str>) -> i64 {
    now_smart(region_timezone).timestamp_millis()
}

/// 智能计算缓存过期时间：优先使用地区时区，回退到全局时区
pub fn calculate_cache_expiration_smart(
    region_timezone: Option<&str>,
    expiration_hours: u64
) -> i64 {
    let current_time = now_smart(region_timezone);
    current_time.timestamp() + (expiration_hours * 3600) as i64
}

/// 智能格式化时间：优先使用地区时区，回退到全局时区
pub fn format_time_smart(region_timezone: Option<&str>, format: &str) -> String {
    now_smart(region_timezone).format(format).to_string()
}

// ==================== 缓存时间一致性检查 ====================

/// 检查Redis TTL和MongoDB过期时间是否一致
///
/// # 参数
///
/// * `region_timezone`: 地区时区
/// * `expiration_hours`: 过期小时数
///
/// # 返回值
///
/// 返回 (redis_ttl_seconds, mongodb_expiration_timestamp)
pub fn get_consistent_cache_expiration(region_timezone: &str, expiration_hours: u64) -> (u64, i64) {
    // Redis TTL：相对时间（秒）
    let redis_ttl = expiration_hours * 3600;

    // MongoDB过期时间戳：绝对时间（基于地区时区）
    let mongodb_expiration = calculate_region_cache_expiration(region_timezone, expiration_hours);

    (redis_ttl, mongodb_expiration)
}

/// 验证缓存时间一致性
///
/// 检查Redis TTL和MongoDB过期时间是否在合理范围内一致
pub fn validate_cache_expiration_consistency(
    redis_ttl_seconds: u64,
    mongodb_expiration_timestamp: i64,
    region_timezone: &str
) -> bool {
    let current_timestamp = timestamp_for_region(region_timezone);
    let calculated_expiration = current_timestamp + redis_ttl_seconds as i64;

    // 允许5分钟的误差（考虑夏令时等因素）
    let time_diff = (calculated_expiration - mongodb_expiration_timestamp).abs();
    time_diff <= 300 // 5分钟
}

/// 获取地区时区的缓存调试信息
pub fn get_cache_debug_info(region_timezone: &str, expiration_hours: u64) -> String {
    let current_time = now_for_region(region_timezone);
    let expiration_time = current_time + chrono::Duration::hours(expiration_hours as i64);
    let (redis_ttl, mongodb_expiration) = get_consistent_cache_expiration(region_timezone, expiration_hours);

    format!(
        "地区时区: {}\n当前时间: {}\n过期时间: {}\nRedis TTL: {}秒\nMongoDB过期: {}",
        region_timezone,
        current_time.format("%Y-%m-%d %H:%M:%S %Z"),
        expiration_time.format("%Y-%m-%d %H:%M:%S %Z"),
        redis_ttl,
        mongodb_expiration
    )
}