<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\SeoRegion;
use App\Models\SeoSite;

class FixRegionDisplay extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:region-display';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复地区显示问题';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== 修复地区显示问题 ===');
        
        // 1. 检查seo_regions表
        $this->info('1. 检查seo_regions表...');
        $regionCount = SeoRegion::count();
        
        if ($regionCount == 0) {
            $this->warn('seo_regions表为空，正在添加默认地区记录...');
            $this->createDefaultRegions();
        } else {
            $this->info("✅ 找到 {$regionCount} 个地区记录");
        }
        
        // 2. 显示地区列表
        $this->info('2. 当前地区列表:');
        $regions = SeoRegion::where('status', 1)->get();
        foreach ($regions as $region) {
            $this->line("  - {$region->code}: {$region->name}");
        }
        
        // 3. 检查seo_site表的region_code字段
        $this->info('3. 检查seo_site表的region_code分布:');
        $distribution = DB::table('seo_site')
            ->select(DB::raw('COALESCE(region_code, "NULL") as region_code, COUNT(*) as count'))
            ->groupBy('region_code')
            ->orderBy('count', 'desc')
            ->get();
        
        foreach ($distribution as $item) {
            $this->line("  - {$item->region_code}: {$item->count} 个网站");
        }
        
        // 4. 修复空的region_code
        $this->info('4. 修复空的region_code值...');
        $updated = DB::table('seo_site')
            ->whereNull('region_code')
            ->orWhere('region_code', '')
            ->update(['region_code' => 'default']);
        
        if ($updated > 0) {
            $this->info("✅ 修复了 {$updated} 个空的region_code记录");
        } else {
            $this->info("✅ 没有需要修复的空记录");
        }
        
        // 5. 测试地区显示逻辑
        $this->info('5. 测试地区显示逻辑...');
        $testSites = SeoSite::limit(5)->get();
        
        foreach ($testSites as $site) {
            $regionCode = $site->region_code ?: 'default';
            $region = SeoRegion::where('code', $regionCode)->where('status', 1)->first();
            
            if ($region) {
                $this->line("  - {$site->host} → {$region->name} ({$regionCode}) ✅");
            } else {
                $this->error("  - {$site->host} → {$regionCode} (未找到地区) ❌");
            }
        }
        
        // 6. 清除缓存
        $this->info('6. 清除相关缓存...');
        \Artisan::call('cache:clear');
        \Artisan::call('config:clear');
        \Artisan::call('view:clear');
        $this->info('✅ 缓存清除完成');
        
        $this->info('');
        $this->info('✅ 地区显示问题修复完成！');
        $this->info('请刷新浏览器页面查看效果。');
        
        return 0;
    }
    
    /**
     * 创建默认地区记录
     */
    protected function createDefaultRegions()
    {
        $defaultRegions = [
            [
                'code' => 'default',
                'name' => '默认地区',
                'language' => 'en',
                'timezone' => 'UTC',
                'config' => '{}',
                'status' => 1,
            ],
            [
                'code' => 'br',
                'name' => '巴西',
                'language' => 'pt',
                'timezone' => 'America/Sao_Paulo',
                'config' => '{"currency": "BRL", "domain_suffix": ".com.br"}',
                'status' => 1,
            ],
            [
                'code' => 'in',
                'name' => '印度',
                'language' => 'hi',
                'timezone' => 'Asia/Kolkata',
                'config' => '{"currency": "INR", "domain_suffix": ".in"}',
                'status' => 1,
            ],
            [
                'code' => 'pk',
                'name' => '巴基斯坦',
                'language' => 'ur',
                'timezone' => 'Asia/Karachi',
                'config' => '{"currency": "PKR", "domain_suffix": ".com.pk"}',
                'status' => 1,
            ],
            [
                'code' => 'us',
                'name' => '美国',
                'language' => 'en',
                'timezone' => 'America/New_York',
                'config' => '{"currency": "USD", "domain_suffix": ".com"}',
                'status' => 1,
            ],
            [
                'code' => 'th',
                'name' => '泰国',
                'language' => 'th',
                'timezone' => 'Asia/Bangkok',
                'config' => '{"currency": "THB", "domain_suffix": ".co.th"}',
                'status' => 1,
            ],
            [
                'code' => 'vn',
                'name' => '越南',
                'language' => 'vi',
                'timezone' => 'Asia/Ho_Chi_Minh',
                'config' => '{"currency": "VND", "domain_suffix": ".com.vn"}',
                'status' => 1,
            ],
        ];
        
        foreach ($defaultRegions as $regionData) {
            SeoRegion::create($regionData);
            $this->line("  ✅ 创建地区: {$regionData['name']} ({$regionData['code']})");
        }
        
        $this->info('✅ 默认地区记录创建完成');
    }
}
