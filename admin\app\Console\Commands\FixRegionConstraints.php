<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use App\Models\SeoRegion;
use App\Models\SeoSite;

class FixRegionConstraints extends Command
{
    protected $signature = 'regions:fix-constraints';
    protected $description = '修复地区外键约束问题';

    public function handle()
    {
        $this->info('开始修复地区外键约束问题...');
        
        // 1. 确保基础地区数据存在
        $this->ensureBasicRegions();
        
        // 2. 修复无效的region_code
        $this->fixInvalidRegionCodes();
        
        // 3. 验证修复结果
        $this->verifyFix();
        
        $this->info('地区外键约束修复完成！');
        return 0;
    }
    
    /**
     * 确保基础地区数据存在
     */
    private function ensureBasicRegions()
    {
        $this->info('1. 检查基础地区数据...');
        
        $basicRegions = [
            [
                'code' => 'default',
                'name' => '默认地区',
                'language' => 'en',
                'timezone' => 'UTC',
                'config' => json_encode(['currency' => 'USD', 'domain_suffix' => '.com', 'country_code' => 'US']),
                'status' => 1,
                'priority' => 1
            ],
            [
                'code' => 'br',
                'name' => '巴西',
                'language' => 'pt',
                'timezone' => 'America/Sao_Paulo',
                'config' => json_encode(['currency' => 'BRL', 'domain_suffix' => '.com.br', 'country_code' => 'BR']),
                'status' => 1,
                'priority' => 10
            ],
            [
                'code' => 'pk',
                'name' => '巴基斯坦',
                'language' => 'en',
                'timezone' => 'Asia/Karachi',
                'config' => json_encode(['currency' => 'PKR', 'domain_suffix' => '.pk', 'country_code' => 'PK']),
                'status' => 1,
                'priority' => 30
            ],
            [
                'code' => 'in',
                'name' => '印度',
                'language' => 'hi',
                'timezone' => 'Asia/Kolkata',
                'config' => json_encode(['currency' => 'INR', 'domain_suffix' => '.in', 'country_code' => 'IN']),
                'status' => 1,
                'priority' => 20
            ],
            [
                'code' => 'us',
                'name' => '美国',
                'language' => 'en',
                'timezone' => 'America/New_York',
                'config' => json_encode(['currency' => 'USD', 'domain_suffix' => '.com', 'country_code' => 'US']),
                'status' => 1,
                'priority' => 40
            ],
            [
                'code' => 'other',
                'name' => '其他',
                'language' => 'en',
                'timezone' => 'UTC',
                'config' => json_encode(['currency' => 'USD', 'domain_suffix' => '.com', 'country_code' => 'XX']),
                'status' => 1,
                'priority' => 999
            ]
        ];
        
        $addedCount = 0;
        foreach ($basicRegions as $region) {
            $exists = SeoRegion::where('code', $region['code'])->exists();
            if (!$exists) {
                $region['created_at'] = now();
                $region['updated_at'] = now();
                
                DB::table('seo_regions')->insert($region);
                $this->info("  添加地区: {$region['name']} ({$region['code']})");
                $addedCount++;
            }
        }
        
        if ($addedCount > 0) {
            $this->info("✅ 添加了 $addedCount 个基础地区");
        } else {
            $this->info("✅ 基础地区数据已存在");
        }
    }
    
    /**
     * 修复无效的region_code
     */
    private function fixInvalidRegionCodes()
    {
        $this->info('2. 检查无效的region_code...');
        
        // 获取所有有效的地区代码
        $validRegionCodes = SeoRegion::pluck('code')->toArray();
        
        // 查找无效的region_code
        $invalidSites = SeoSite::whereNotIn('region_code', $validRegionCodes)
                              ->orWhereNull('region_code')
                              ->orWhere('region_code', '')
                              ->get();
        
        if ($invalidSites->count() > 0) {
            $this->warn("发现 {$invalidSites->count()} 个网站使用了无效的region_code:");
            
            $invalidCodes = $invalidSites->groupBy('region_code');
            foreach ($invalidCodes as $code => $sites) {
                $this->line("  - '{$code}': {$sites->count()} 个网站");
            }
            
            // 修复无效的region_code
            $updatedCount = SeoSite::whereNotIn('region_code', $validRegionCodes)
                                  ->orWhereNull('region_code')
                                  ->orWhere('region_code', '')
                                  ->update(['region_code' => 'default']);
            
            $this->info("✅ 已将 $updatedCount 个网站的region_code修复为'default'");
        } else {
            $this->info("✅ 所有网站的region_code都是有效的");
        }
    }
    
    /**
     * 验证修复结果
     */
    private function verifyFix()
    {
        $this->info('3. 验证修复结果...');
        
        // 统计各地区的网站数量
        $stats = DB::table('seo_regions as r')
                   ->leftJoin('seo_site as s', 'r.code', '=', 's.region_code')
                   ->select('r.code', 'r.name', DB::raw('COUNT(s.id) as site_count'))
                   ->groupBy('r.code', 'r.name')
                   ->orderBy('r.priority')
                   ->get();
        
        $this->table(['地区代码', '地区名称', '网站数量'], $stats->map(function($stat) {
            return [$stat->code, $stat->name, $stat->site_count];
        })->toArray());
        
        // 检查是否还有无效的region_code
        $invalidCount = SeoSite::whereNotIn('region_code', SeoRegion::pluck('code'))->count();
        
        if ($invalidCount > 0) {
            $this->error("⚠️  仍有 $invalidCount 个网站使用无效的region_code");
        } else {
            $this->info("✅ 所有网站的region_code都已修复");
        }
    }
}
