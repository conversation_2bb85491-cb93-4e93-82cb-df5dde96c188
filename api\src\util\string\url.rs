use chrono::{Datelike, Timelike};
use lazy_static::lazy_static;
use rand::prelude::*;
use rand::seq::SliceRandom;
use regex::Regex;

lazy_static! {
    static ref RE: Regex = Regex::new(r"\{([\p{Han}a-zA-Z]+)(\d+([-_]\d+)?)?\}").unwrap();
    static ref LETTERS: Vec<char> = ('a'..='z').collect();
    static ref LETTERS_UPPER: Vec<char> = ('A'..='Z').collect();
    static ref NUMBERS: Vec<char> = ('0'..='9').collect();
    static ref LETTERS_UPPER_LOWER: Vec<char> = LETTERS
        .iter()
        .chain(LETTERS_UPPER.iter())
        .cloned()
        .collect();
    static ref LETTERS_UPPER_NUMBER: Vec<char> = LETTERS_UPPER
        .iter()
        .chain(NUMBERS.iter())
        .cloned()
        .collect();
    static ref LETTERS_UPPER_LOWER_NUMBER: Vec<char> = LETTERS
        .iter()
        .chain(LETTERS_UPPER.iter())
        .chain(NUMBERS.iter())
        .cloned()
        .collect();
    static ref LETTERS_NUMBER: Vec<char> = LETTERS.iter().chain(NUMBERS.iter()).cloned().collect();
}
pub fn replace(url: &str, rng: &mut ThreadRng) -> String {
    let result = RE.replace_all(url, |caps: &regex::Captures| {
        let tag = caps.get(1).map_or("", |m| m.as_str());
        let param = caps.get(2).map_or("", |m| m.as_str());
        let mut generated_length = 6;
        let params: Vec<usize> = param
            .split(|c| c == '-' || c == '_')
            .filter_map(|s| s.parse().ok())
            .collect();
        if let Some(min) = params.get(0) {
            generated_length = *min;
            if let Some(max) = params.get(1) {
                generated_length = rng.gen_range(*min..=*max);
            }
        }

        match tag {
            "日期" => crate::util::time::now().format("%Y-%m-%d").to_string(),
            "数字" => random_chars(rng, &NUMBERS, generated_length),
            "字母" => random_chars(rng, &LETTERS, generated_length),
            "大写字母" => random_chars(rng, &LETTERS_UPPER, generated_length),
            "大小写字母" => random_chars(rng, &LETTERS_UPPER_LOWER, generated_length),
            "大写字母数字" => random_chars(rng, &LETTERS_UPPER_NUMBER, generated_length),
            "大小写字母数字" => {
                random_chars(rng, &LETTERS_UPPER_LOWER_NUMBER, generated_length)
            }
            "数字字母" => random_chars(rng, &LETTERS_NUMBER, generated_length),
            "年" => crate::util::time::now().year().to_string(),
            "月" => format!("{:02}", crate::util::time::now().month()),
            "日" => format!("{:02}", crate::util::time::now().day()),
            "时" => format!("{:02}", crate::util::time::now().hour()),
            "分" => format!("{:02}", crate::util::time::now().minute()),
            "秒" => format!("{:02}", crate::util::time::now().second()),
            "随机字符" => random_chars(rng, &LETTERS_NUMBER, generated_length),
            "关键词" => {
                let keyword = crate::RESOURCE.get_random_data(rng, "keyword");
                crate::util::url::custom_encode(&keyword)
            },
            _ => tag.to_string(),
        }
    });

    result.into_owned()
}

// 支持地区资源的URL替换函数
pub fn replace_with_resource(url: &str, rng: &mut ThreadRng, resource: &crate::entities::Resource) -> String {
    let result = RE.replace_all(url, |caps: &regex::Captures| {
        let tag = caps.get(1).map_or("", |m| m.as_str());
        let param = caps.get(2).map_or("", |m| m.as_str());
        let mut generated_length = 6;
        let params: Vec<usize> = param
            .split(|c| c == '-' || c == '_')
            .filter_map(|s| s.parse().ok())
            .collect();
        if let Some(min) = params.get(0) {
            generated_length = *min;
            if let Some(max) = params.get(1) {
                generated_length = rng.gen_range(*min..=*max);
            }
        }

        match tag {
            "日期" => crate::util::time::now().format("%Y-%m-%d").to_string(),
            "数字" => random_chars(rng, &NUMBERS, generated_length),
            "字母" => random_chars(rng, &LETTERS, generated_length),
            "大写字母" => random_chars(rng, &LETTERS_UPPER, generated_length),
            "大小写字母" => random_chars(rng, &LETTERS_UPPER_LOWER, generated_length),
            "大写字母数字" => random_chars(rng, &LETTERS_UPPER_NUMBER, generated_length),
            "大小写字母数字" => {
                random_chars(rng, &LETTERS_UPPER_LOWER_NUMBER, generated_length)
            }
            "数字字母" => random_chars(rng, &LETTERS_NUMBER, generated_length),
            "年" => crate::util::time::now().year().to_string(),
            "月" => format!("{:02}", crate::util::time::now().month()),
            "日" => format!("{:02}", crate::util::time::now().day()),
            "时" => format!("{:02}", crate::util::time::now().hour()),
            "分" => format!("{:02}", crate::util::time::now().minute()),
            "秒" => format!("{:02}", crate::util::time::now().second()),
            "随机字符" => random_chars(rng, &LETTERS_NUMBER, generated_length),
            "关键词" => {
                let keyword = resource.get_random_data(rng, "keyword"); // 使用地区资源
                crate::util::url::custom_encode(&keyword)
            },
            _ => tag.to_string(),
        }
    });

    result.into_owned()
}

pub fn random_chars(rng: &mut impl Rng, chars: &[char], n: usize) -> String {
    (0..n).map(|_| *chars.choose(rng).unwrap()).collect()
}

pub fn get_top_level_domain(host: &str) -> Result<String, Box<dyn std::error::Error>> {
    let domains: Vec<&str> = host.split('.').collect();
    if domains.len() < 3 {
        return Err("Invalid domain".into());
    }
    let tld = domains[1..].join(".");
    Ok(tld)
}

pub fn replace_url_tags(url: &str) -> String {
    let url = escape_regex_chars(url);
    let result = RE.replace_all(&url, |caps: &regex::Captures| {
        let tag = caps.get(1).map_or("", |m| m.as_str());
        let param = caps.get(2).map_or("", |m| m.as_str());
        let params: Vec<&str> = param.split(|c| c == '-' || c == '_').collect();
        let min: &str = params.get(0).unwrap_or(&"");
        let max: &str = if params.len() == 2 { params[1] } else { min };

        let pattern = match tag {
            "日期" => r"\d{4}-\d{2}-\d{2}",
            "数字" => r"\d",
            "字母" => r"[a-z]",
            "大写字母" => r"[A-Z]",
            "大小写字母" => r"[a-zA-Z]",
            "大写字母数字" => r"[A-Z0-9]",
            "大小写字母数字" => r"[a-zA-Z0-9]",
            "数字字母" => r"[0-9a-z]",
            "年" | "月" | "日" | "时" | "分" | "秒" => r"\d",
            "关键词" => r".",
            _ => "",
        };

        if !min.is_empty() && !max.is_empty() && min != max {
            format!(r"({}){{{},{}}}", pattern, min, max)
        } else if !min.is_empty() {
            format!(r"({}){{{}}}", pattern, min)
        } else {
            format!(r"({}+)", pattern)
        }
    });
    result.to_string()
}
fn escape_regex_chars(uri: &str) -> String {
    let regex_special_chars = ['.', '^', '$', '*', '+', '?', '(', ')', '[', ']', '\\', '|'];
    let mut escaped_uri = String::with_capacity(uri.len());
    for c in uri.chars() {
        if regex_special_chars.contains(&c) {
            escaped_uri.push('\\');
        }
        escaped_uri.push(c);
    }
    escaped_uri
}
