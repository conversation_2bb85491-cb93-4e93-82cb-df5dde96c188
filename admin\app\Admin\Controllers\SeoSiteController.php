<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SeoSite;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tab;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use App\Models\SeoMoban;
use App\Models\Mongo;
use App\Models\SeoRegion;
use App\Admin\Actions\Grid\DelCacheAcion;
use App\Admin\Actions\Grid\RefreshAppAction;
use App\Admin\Actions\Grid\RefreshDataAction;
use App\Admin\Actions\Grid\BatchEnableWebsite;
use App\Admin\Actions\Grid\BatchDisableWebsite;
use App\Admin\Actions\Grid\BatchEditWebsite;
class SeoSiteController extends AdminController
{
    public $state = 1;
    public $host = "";
    public function __construct(Request $request)
    {
        if(isset($request->state)){
            $this->state = (int)$request->state;            
        }else{
            $this->state = 1;
        }
    }
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        // 添加调试代码，输出未通过网站数量
        \App\Models\SeoSite::debug();
        
        return Grid::make(\App\Models\SeoSite::class, function (Grid $grid) {
            // $mongo = new Mongo();
            // $coll =  $mongo->getCollection("cache");
            $grid->model()->where('state', $this->state);
            $grid->column('id')->sortable();
            $grid->column('host')->sortable();
            $grid->column('region_code', '地区')->display(function ($regionCode) {
                // 检查原始值
                if (empty($regionCode) || is_null($regionCode)) {
                    return "<span style='background-color: #f0ad4e; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>未设置</span>";
                }

                // 查找地区信息（先查询激活状态的，如果没有再查询所有状态的）
                $region = \App\Models\SeoRegion::where('code', $regionCode)->where('status', 1)->first();

                if (!$region) {
                    // 如果激活状态的找不到，查询所有状态的
                    $region = \App\Models\SeoRegion::where('code', $regionCode)->first();
                    if ($region && $region->status != 1) {
                        return "<span style='background-color: #d9534f; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>🚫 {$region->name} (已禁用)</span>";
                    }
                }

                if ($region) {
                    // 添加国旗图标
                    $flags = [
                        'br' => '🇧🇷', 'pk' => '🇵🇰', 'in' => '🇮🇳', 'us' => '🇺🇸',
                        'id' => '🇮🇩', 'th' => '🇹🇭', 'mx' => '🇲🇽', 'ng' => '🇳🇬',
                        'bd' => '🇧🇩', 'ph' => '🇵🇭', 'vn' => '🇻🇳', 'jp' => '🇯🇵',
                        'default' => '🌍', 'other' => '🌍'
                    ];
                    $flag = $flags[$regionCode] ?? '🌍';
                    return "<span style='background-color: #337ab7; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>{$flag} {$region->name}</span>";
                }

                // 如果找不到地区，显示原始代码
                return "<span style='background-color: #777; color: #fff; padding: 2px 6px; border-radius: 3px; font-size: 11px;'>❓ {$regionCode}</span>";
            })->width(120);
            $grid->column('link_rules')->display(function ($val) {
                $len = 40;
                if(strlen($val) > $len){
                    return mb_substr($val, 0, $len)."...";
                }else{
                    return $val;
                }
            });
            $grid->column('jump_rules')->display(function ($val) {
                $len = 40;
                if(strlen($val) > $len){
                    return mb_substr($val, 0, $len)."...";
                }else{
                    return $val;
                }
            });
            $grid->column('open_home')->switch('', true)->width(70);
            $grid->column('open_link')->switch('', true)->width(70);
            $grid->column('open_page')->switch('', true)->width(70);
            $grid->column('open_cache')->switch('', true)->width(70);
            // $grid->column('flood')->switch('', true);
            $grid->column('state')->switch('', true)->width(60);  
            $grid->column('last_time')->display(function () {
                // 获取Redis中的最后爬取时间
                $keyName = 'lasttime:'.$this->host;

                // 从Redis获取数据
                $redisVal = null;
                try {
                    $redisVal = Redis::get($keyName);
                } catch (\Exception $e) {
                    // Redis获取失败，忽略错误
                }
                
                // 从Redis获取的值
                if (!empty($redisVal)) {
                    // 根据网站的地区代码获取对应时区
                    $region = \App\Models\SeoRegion::where('code', $this->region_code)->first();
                    $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

                    $date = new \DateTime();
                    $date->setTimestamp($redisVal);
                    $date->setTimezone(new \DateTimeZone($timezone));
                    return $date->format('Y-m-d H:i:s');
                }

                // 从数据库获取的值
                if (!empty($this->last_time)) {
                    // 根据网站的地区代码获取对应时区
                    $region = \App\Models\SeoRegion::where('code', $this->region_code)->first();
                    $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

                    $date = new \DateTime();
                    $date->setTimestamp($this->last_time);
                    $date->setTimezone(new \DateTimeZone($timezone));
                    return $date->format('Y-m-d H:i:s');
                }
                
                return null;
            })->width(110);
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('id');
                $filter->like('host');
                $filter->equal('region_code', '地区')->select(SeoRegion::getRegionOptions());
            });

            // $grid->column('缓存(redis/mongo)')->display(function () use ($coll) {
            //     $redisKey = $this->host.":*";
            //     $count = $coll->countDocuments(["site"=>$this->host]);
            //     $keys = Redis::keys("cache:".$redisKey);
            //     $keys2 = Redis::keys("unread_cache:".$redisKey);
            //     $cache_count = count($keys) + count($keys2);
            //     return $cache_count."/".$count;
            // })->width(100);
            $grid->header(function(){
                $tab = Tab::make();
                $tab->addLink('列表', "?state=1", $this->state==1);
                //$grid->model()->where('state', 0);
                $tab->addLink('未通过', "?state=0",$this->state==0);
                // $tab->addLink('更新缓存', "?refresh=1");
                return $tab;
            });

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new RefreshAppAction());
                $tools->append(new \App\Admin\Actions\Grid\RefreshTotalAction());
                $tools->append(new \App\Admin\Actions\Grid\RefreshDataAction());
            });
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                // 自定义编辑按钮，添加return_url参数
                $id = $actions->getKey();
                $currentUrl = request()->fullUrl();
                $editUrl = admin_url("seo/site/{$id}/edit?return_url=" . urlencode($currentUrl));

                // 禁用默认编辑按钮，使用自定义的文字按钮，放在前面
                $actions->disableEdit();
                $actions->prepend('<a href="'.$editUrl.'" class="btn btn-outline-primary"><i class="fa fa-edit"></i> 编辑</a>');

                $actions->append(new DelCacheAcion());
            });

            // 🔧 批量操作：启用、禁用、编辑
            $grid->batchActions([
                new BatchEnableWebsite(),
                new BatchDisableWebsite(),
                new BatchEditWebsite(),
            ]);



            
            // $grid->wrap(function(Renderable $view) {
                
            //    $tab = Tab::make();
            //    $tab->add('列表', $view);               
            //    return $tab; 
            // });


        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, \App\Models\SeoSite::class, function (Show $show) {
            $show->field('id');
            $show->field('host');
            $show->field('https');
            $show->field('link_rules');
            $show->field('jump_rules');
            $show->field('open_home');
            $show->field('open_link');
            $show->field('open_page');
            $show->field('open_cache');
            $show->field('state');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(\App\Models\SeoSite::class, function (Form $form) {
            // 确保表单使用默认的action，不被其他地方影响

            // 检查是否有返回地区参数或预设地区代码
            $returnRegion = request('return_region');
            $presetRegion = request('region_code');

            // 获取来源页面URL（优先使用return_url参数，其次使用referer）
            $returnUrl = request('return_url');
            if (!$returnUrl) {
                $referer = request()->header('referer');
                if ($referer && strpos($referer, admin_url('')) === 0 && !str_contains($referer, '/edit')) {
                    $returnUrl = $referer;
                }
            }

            // 用于保存跳转URL的变量
            $finalRedirectUrl = null;

            // 设置提交回调，获取并保存跳转URL
            $form->submitted(function (Form $form) use ($returnRegion, $returnUrl, &$finalRedirectUrl) {
                // 从表单中获取隐藏的return_url字段
                $formReturnUrl = $form->input('_return_url');

                // 确定跳转URL的优先级
                if ($returnRegion) {
                    $finalRedirectUrl = admin_url("seo/website-management?region={$returnRegion}");
                } elseif ($formReturnUrl && strpos($formReturnUrl, admin_url('')) === 0) {
                    $finalRedirectUrl = $formReturnUrl;
                } elseif ($returnUrl && strpos($returnUrl, admin_url('')) === 0) {
                    $finalRedirectUrl = $returnUrl;
                } else {
                    $finalRedirectUrl = admin_url('seo/website-management');
                }

                // 删除不需要保存到数据库的字段
                $form->deleteInput('_return_url');
            });

            // 设置保存成功回调
            $form->saved(function (Form $form, $result) use (&$finalRedirectUrl) {
                    // 保存成功后刷新API缓存
                    try {
                        \App\Services\ApiConfigService::fullCacheSync();
                        \Log::info('网站配置保存后API缓存刷新成功');
                    } catch (\Exception $e) {
                        \Log::warning('网站配置保存后API缓存刷新失败: ' . $e->getMessage());
                    }

                    // 使用预先确定的跳转URL
                    if ($finalRedirectUrl) {
                        return $form->response()->success('网站配置保存成功！API缓存已同步')->redirect($finalRedirectUrl);
                    }

                    // 如果没有预设跳转URL，使用默认跳转
                    return $form->response()->success('网站配置保存成功！API缓存已同步')->redirect(admin_url('seo/website-management'));
                });

            $form->display('id');

            // 添加隐藏字段保存来源URL
            $form->hidden('_return_url')->value($returnUrl);

            // 添加缓存统计显示（异步加载，避免影响表单保存）
            $form->html(function() use ($form) {
                // 获取当前编辑的网站host
                $host = $form->model()->host ?? '';
                if (empty($host)) {
                    return '<div id="cache-stats">缓存统计: 等待保存后显示</div>';
                }

                // 使用AJAX异步加载缓存统计，避免在表单渲染时执行可能失败的操作
                $ajaxUrl = admin_url('api/cache-stats/' . urlencode($host));
                return <<<HTML
                <div id="cache-stats">
                    <span class="text-muted">缓存统计: 加载中...</span>
                    <script>
                        $(document).ready(function() {
                            $.ajax({
                                url: '{$ajaxUrl}',
                                method: 'GET',
                                timeout: 5000,
                                success: function(data) {
                                    $('#cache-stats').html('缓存统计: ' + data.stats);
                                },
                                error: function() {
                                    $('#cache-stats').html('<span class="text-warning">缓存统计: 获取失败</span>');
                                }
                            });
                        });
                    </script>
                </div>
                HTML;
            },'缓存数');
            $form->text('host', '域名')
                ->required()
                ->rules('required|regex:/^[a-zA-Z0-9._-]+\.[a-zA-Z]{2,}$/')
                ->help('请输入有效的域名，如：example.com 或 sub_domain.example.com');

            // 获取预设的地区代码
            $presetRegion = request('region_code');
            $defaultRegion = $presetRegion ?: 'default';

            $form->select('region_code', '地区')
                ->options(SeoRegion::getRegionOptions())
                ->default($defaultRegion)
                ->required()
                ->help('选择网站所属的地区' . ($presetRegion ? " (已预选: {$presetRegion})" : ''));
            $form->radio('https', 'HTTPS')->options(['1'=> '开启','0' => '关闭'])->default('0')->help('
                <strong>HTTPS状态说明：</strong><br>
                • 系统会根据网站访问请求自动判断并更新HTTPS状态<br>
                • 当用户通过https://访问网站时，系统自动设置为开启<br>
                • 当用户通过http://访问网站时，系统自动设置为关闭<br>
                • 此处设置仅为初始状态，实际状态以访问请求为准
            ');
            $form->html(function ()  {
                $html = '';
                // 获取模板数据
                $templates = SeoMoban::all();
                foreach ($templates as $template) {
                    $jsonData = htmlspecialchars($template->toJson());
                    $html .= <<<HTML
                        <button type="button" class="btn btn-primary btn-sm template-button" data-template-data='{$jsonData}'>
                            {$template->name}
                        </button>
                    HTML;
                }

                $html .= <<<HTML
                    <script>
                        function setRadioState(radioName, targetValue) {
                            var radios = $('input[name="' + radioName + '"]');
                            radios.filter('[value="' + targetValue + '"]').prop('checked', true);
                            radios.filter('[value!="' + targetValue + '"]').prop('checked', false);
                        }
                        $('.template-button').on('click', function() {
                            var templateData = $(this).data('template-data');
                            $('textarea[name="link_rules"]').val(templateData.link_rules);
                            setRadioState('open_home', templateData.open_home);
                            setRadioState('open_link', templateData.open_link);
                            setRadioState('open_page', templateData.open_page);
                            setRadioState('open_cache', templateData.open_cache);
                            setRadioState('state', true);
                        });
                    </script>
                HTML;

                return $html;
            });

            
            $form->textarea('link_rules', '链接规则')
                ->rows(5)
                ->help("
                    <strong>URL生成规则（每行一个）：</strong><br>
                    • 标签：{数字}{字母}{大写字母}{大小写字母}{大写字母数字}{数字字母}{随机字符}{日期}{年}{月}{日}{时}{分}{秒}{关键词}<br>
                    • 默认长度6，可自定义：{数字4} {数字2_6} {数字2-6}<br>
                    • 示例：/{关键词}/{数字4} 或 /{字母6}/{数字3}<br>
                    • <span class='text-info'>关键词中的空格会自动转换为短横线(-)</span>
                ");
            $form->textarea('jump_rules', '跳转规则')
                ->rows(3)
                ->help('
                    <strong>跳转规则配置：</strong><br>
                    • 默认包含生成规则<br>
                    • 标签：{任意字符}<br>
                    • 用于配置页面跳转逻辑
                ');
            $form->radio('open_home', '开启首页')->options(['1'=> '开启','0' => '关闭'])->default('0')->help('开启后允许首页访问，默认关闭');
            $form->radio('open_link', '开启轮链')->options(['1'=> '开启','0' => '关闭'])->default('1')->help('开启后显示轮链内容');
            $form->radio('open_page', '开启页面')->options(['1'=> '开启','0' => '关闭'])->default('1')->help('开启后允许内页访问');
            $form->radio('open_cache', '开启缓存')->options(['1'=> '开启','0' => '关闭'])->default('1')->help('开启后缓存页面内容');
            $form->radio('state', '网站状态')->options(['1'=> '启用','0' => '禁用'])->default('1')->help('只有启用的网站才能正常访问');
        });
    }

    /**
     * 获取缓存统计信息（AJAX接口）
     */
    public function getCacheStats($host)
    {
        try {
            // 解码URL编码的host
            $host = urldecode($host);

            $redisCount = 0;
            $mongoCount = 0;

            // 尝试获取Redis统计
            try {
                $redisKey = $host . ":*";
                $keys = \Illuminate\Support\Facades\Redis::keys("cache:" . $redisKey);
                $keys2 = \Illuminate\Support\Facades\Redis::keys("unread_cache:" . $redisKey);
                $redisCount = count($keys) + count($keys2);
            } catch (\Exception $e) {
                // Redis缓存统计获取失败，忽略错误
            }

            // 尝试获取MongoDB统计
            try {
                $mongo = new \App\Models\Mongo();
                $coll = $mongo->getCollection("cache");
                $mongoCount = $coll->countDocuments(["site" => $host]);
            } catch (\Exception $e) {
                // MongoDB缓存统计获取失败，忽略错误
            }

            return response()->json([
                'success' => true,
                'stats' => sprintf("redis:%d/mongo:%d", $redisCount, $mongoCount)
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'stats' => '获取失败'
            ]);
        }
    }



    /**
     * 删除网站 - 支持单个和批量删除
     */
    public function destroy($id)
    {
        try {
            // 🔧 支持批量删除：处理多个ID（逗号分隔）
            $ids = is_string($id) ? explode(',', $id) : [$id];
            $ids = array_filter(array_map('trim', $ids)); // 清理空值

            if (empty($ids)) {
                return \Dcat\Admin\Admin::json()->error("没有要删除的网站");
            }

            $deletedHosts = [];
            $failedCount = 0;

            foreach ($ids as $singleId) {
                try {
                    $site = \App\Models\SeoSite::find($singleId);
                    if ($site) {
                        $host = $site->host;
                        $site->delete();
                        $deletedHosts[] = $host;
                        \Log::info("删除网站成功: {$host}");
                    } else {
                        $failedCount++;
                        \Log::warning("网站不存在: ID {$singleId}");
                    }
                } catch (\Exception $e) {
                    $failedCount++;
                    \Log::error("删除网站失败 ID {$singleId}: " . $e->getMessage());
                }
            }

            // 生成响应消息
            $deletedCount = count($deletedHosts);
            $totalCount = count($ids);

            if ($deletedCount > 0) {
                if ($deletedCount == 1) {
                    $message = "网站 {$deletedHosts[0]} 删除成功";
                } else {
                    $message = "成功删除 {$deletedCount} 个网站";
                    if ($failedCount > 0) {
                        $message .= "，失败 {$failedCount} 个";
                    }
                }
                return \Dcat\Admin\Admin::json()->success($message)->refresh();
            } else {
                return \Dcat\Admin\Admin::json()->error("删除失败");
            }

        } catch (\Exception $e) {
            \Log::error("删除操作失败: " . $e->getMessage());
            return \Dcat\Admin\Admin::json()->error('删除失败: ' . $e->getMessage());
        }
    }



    /**
     * 刷新API项目缓存
     */
    private function refreshApiCache()
    {
        try {
            // 使用ApiConfigService统一管理API连接
            \App\Services\ApiConfigService::fullCacheSync();
            \Log::info('API缓存刷新成功');
        } catch (\Exception $e) {
            \Log::warning('刷新API缓存失败: ' . $e->getMessage());
        }
    }

    /**
     * 批量编辑页面 - 创建可编辑的批量编辑表单
     */
    public function batchEdit(Request $request)
    {
        $ids = explode(',', $request->get('ids', ''));
        $ids = array_filter($ids);

        if (empty($ids)) {
            return redirect()->back()->with('error', '没有选择要编辑的网站');
        }

        // 获取选中的网站信息
        $websites = SeoSite::whereIn('id', $ids)->get(['id', 'host']);

        if ($websites->isEmpty()) {
            return redirect()->back()->with('error', '选中的网站不存在');
        }

        // 处理表单提交
        if ($request->isMethod('post')) {
            return $this->processBatchEdit($request, $ids);
        }

        // 创建批量编辑表单
        $form = $this->createBatchEditForm($websites, $ids);

        return Content::make()
            ->title('批量编辑网站')
            ->description('共' . count($websites) . '个网站')
            ->body($form);
    }









    /**
     * 创建批量编辑表单 - 完全独立的表单，不影响普通编辑
     */
    protected function createBatchEditForm($websites, $ids)
    {
        // 显示信息
        $displayIds = $websites->take(3)->pluck('id')->implode(', ');
        if (count($websites) > 3) {
            $displayIds .= ' 等';
        }

        $displayHosts = $websites->take(3)->pluck('host')->implode(', ');
        if (count($websites) > 3) {
            $displayHosts .= ' 等';
        }

        // 获取地区选项
        $regionOptions = SeoRegion::getRegionOptions();

        // 获取模板数据
        $templates = SeoMoban::all();

        // 创建HTML表单
        $html = '<div class="box box-info">
            <div class="box-header with-border">
                <h3 class="box-title">批量编辑网站配置</h3>
            </div>
            <form method="POST" action="' . admin_url('seo/site/batch-edit') . '" class="form-horizontal">
                ' . csrf_field() . '
                <input type="hidden" name="ids" value="' . implode(',', $ids) . '">

                <div class="box-body">
                    <!-- ID显示 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">ID</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">' . $displayIds . '</p>
                        </div>
                    </div>

                    <!-- 缓存统计 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">缓存数</label>
                        <div class="col-sm-8">
                            <p class="form-control-static">批量编辑: 共选中 ' . count($websites) . ' 个网站</p>
                        </div>
                    </div>

                    <!-- 域名显示 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">域名</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" value="' . $displayHosts . '" readonly>
                            <span class="help-block">批量编辑模式：显示选中网站的域名（只读）</span>
                        </div>
                    </div>

                    <!-- 地区选择 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">地区</label>
                        <div class="col-sm-8">
                            <select name="region_code" class="form-control">
                                <option value="">-- 不修改 --</option>';

        foreach ($regionOptions as $code => $name) {
            $html .= '<option value="' . $code . '">' . $name . '</option>';
        }

        $html .= '</select>
                            <span class="help-block">选择后将统一设置所有选中网站的地区，留空表示不修改</span>
                        </div>
                    </div>

                    <!-- 模板按钮 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">模板</label>
                        <div class="col-sm-8">';

        foreach ($templates as $template) {
            $jsonData = htmlspecialchars($template->toJson());
            $html .= '<button type="button" class="btn btn-primary btn-sm template-button" data-template-data=\'' . $jsonData . '\'>' . $template->name . '</button> ';
        }

        $html .= '</div>
                    </div>

                    <!-- 链接规则 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">链接规则</label>
                        <div class="col-sm-8">
                            <textarea name="link_rules" class="form-control" rows="5" placeholder="留空表示不修改"></textarea>
                            <span class="help-block">
                                <strong>URL生成规则（每行一个）：</strong><br>
                                • 标签：{数字}{字母}{大写字母}{大小写字母}{大写字母数字}{数字字母}{随机字符}{日期}{年}{月}{日}{时}{分}{秒}{关键词}<br>
                                • 默认长度6，可自定义：{数字4} {数字2_6} {数字2-6}<br>
                                • 示例：/{关键词}/{数字4} 或 /{字母6}/{数字3}<br>
                                • <span class="text-info">关键词中的空格会自动转换为短横线(-)</span><br>
                                • <strong class="text-warning">批量编辑：填写后将覆盖所有选中网站的链接规则，留空表示不修改</strong>
                            </span>
                        </div>
                    </div>

                    <!-- 跳转规则 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">跳转规则</label>
                        <div class="col-sm-8">
                            <textarea name="jump_rules" class="form-control" rows="3" placeholder="留空表示不修改"></textarea>
                            <span class="help-block">
                                <strong>跳转规则配置：</strong><br>
                                • 默认包含生成规则<br>
                                • 标签：{任意字符}<br>
                                • 用于配置页面跳转逻辑<br>
                                • <strong class="text-warning">批量编辑：填写后将覆盖所有选中网站的跳转规则，留空表示不修改</strong>
                            </span>
                        </div>
                    </div>

                    <!-- 开关设置 -->
                    <div class="form-group">
                        <label class="col-sm-2 control-label">开启首页</label>
                        <div class="col-sm-8">
                            <label class="radio-inline"><input type="radio" name="open_home" value="" checked> 不修改</label>
                            <label class="radio-inline"><input type="radio" name="open_home" value="1"> 开启</label>
                            <label class="radio-inline"><input type="radio" name="open_home" value="0"> 关闭</label>
                            <span class="help-block">开启后允许首页访问，批量编辑：选择后将统一设置所有选中网站</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">开启轮链</label>
                        <div class="col-sm-8">
                            <label class="radio-inline"><input type="radio" name="open_link" value="" checked> 不修改</label>
                            <label class="radio-inline"><input type="radio" name="open_link" value="1"> 开启</label>
                            <label class="radio-inline"><input type="radio" name="open_link" value="0"> 关闭</label>
                            <span class="help-block">开启后显示轮链内容，批量编辑：选择后将统一设置所有选中网站</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">开启页面</label>
                        <div class="col-sm-8">
                            <label class="radio-inline"><input type="radio" name="open_page" value="" checked> 不修改</label>
                            <label class="radio-inline"><input type="radio" name="open_page" value="1"> 开启</label>
                            <label class="radio-inline"><input type="radio" name="open_page" value="0"> 关闭</label>
                            <span class="help-block">开启后允许内页访问，批量编辑：选择后将统一设置所有选中网站</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">开启缓存</label>
                        <div class="col-sm-8">
                            <label class="radio-inline"><input type="radio" name="open_cache" value="" checked> 不修改</label>
                            <label class="radio-inline"><input type="radio" name="open_cache" value="1"> 开启</label>
                            <label class="radio-inline"><input type="radio" name="open_cache" value="0"> 关闭</label>
                            <span class="help-block">开启后缓存页面内容，批量编辑：选择后将统一设置所有选中网站</span>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">网站状态</label>
                        <div class="col-sm-8">
                            <label class="radio-inline"><input type="radio" name="state" value="" checked> 不修改</label>
                            <label class="radio-inline"><input type="radio" name="state" value="1"> 启用</label>
                            <label class="radio-inline"><input type="radio" name="state" value="0"> 禁用</label>
                            <span class="help-block">只有启用的网站才能正常访问，批量编辑：选择后将统一设置所有选中网站</span>
                        </div>
                    </div>
                </div>

                <div class="box-footer">
                    <div class="col-sm-offset-2 col-sm-8">
                        <button type="submit" class="btn btn-primary">保存修改</button>
                        <a href="' . admin_url('seo/site') . '" class="btn btn-default">取消</a>
                    </div>
                </div>
            </form>
        </div>

        <script>
            function setRadioState(radioName, targetValue) {
                var radios = $(\'input[name="\' + radioName + \'"]\');
                radios.filter(\'[value="\' + targetValue + \'"]\').prop(\'checked\', true);
                radios.filter(\'[value!="\' + targetValue + \'"]\').prop(\'checked\', false);
            }

            $(\'.template-button\').on(\'click\', function() {
                var templateData = $(this).data(\'template-data\');
                $(\'textarea[name="link_rules"]\').val(templateData.link_rules);
                setRadioState(\'open_home\', templateData.open_home);
                setRadioState(\'open_link\', templateData.open_link);
                setRadioState(\'open_page\', templateData.open_page);
                setRadioState(\'open_cache\', templateData.open_cache);
                setRadioState(\'state\', \'1\');
            });
        </script>';

        return $html;
    }

    /**
     * 处理批量编辑
     */
    protected function processBatchEdit(Request $request, $ids)
    {
        // 如果传入的是字符串，转换为数组
        if (is_string($ids)) {
            $ids = explode(',', $request->input('ids', ''));
            $ids = array_filter($ids);
        }

        if (empty($ids)) {
            return $this->response()->error('没有选择要编辑的网站');
        }

        $updateData = [];

        // 只更新非空且不为默认值的字段
        if ($request->filled('region_code') && $request->input('region_code') !== '') {
            $updateData['region_code'] = $request->input('region_code');
        }

        if ($request->filled('state') && $request->input('state') !== '') {
            $updateData['state'] = (int)$request->input('state');
        }

        if ($request->filled('open_cache') && $request->input('open_cache') !== '') {
            $updateData['open_cache'] = (int)$request->input('open_cache');
        }

        if ($request->filled('open_page') && $request->input('open_page') !== '') {
            $updateData['open_page'] = (int)$request->input('open_page');
        }

        if ($request->filled('open_home') && $request->input('open_home') !== '') {
            $updateData['open_home'] = (int)$request->input('open_home');
        }

        if ($request->filled('open_link') && $request->input('open_link') !== '') {
            $updateData['open_link'] = (int)$request->input('open_link');
        }

        if ($request->filled('link_rules') && trim($request->input('link_rules')) !== '') {
            $updateData['link_rules'] = $request->input('link_rules');
        }

        if ($request->filled('jump_rules') && trim($request->input('jump_rules')) !== '') {
            $updateData['jump_rules'] = $request->input('jump_rules');
        }

        if (empty($updateData)) {
            return $this->response()->error('没有要更新的数据');
        }

        try {
            $count = SeoSite::whereIn('id', $ids)->update($updateData);

            // 刷新API缓存
            try {
                \App\Services\ApiConfigService::fullCacheSync();
            } catch (\Exception $e) {
                \Log::warning('批量编辑后API缓存刷新失败: ' . $e->getMessage());
            }

            // 清除批量编辑session
            session()->forget(['batch_edit_mode', 'batch_edit_websites', 'batch_edit_ids', 'batch_edit_display_ids', 'batch_edit_display_hosts']);

            return $this->response()->success("成功更新 {$count} 个网站，API缓存已同步")->redirect(admin_url('seo/site'));

        } catch (\Exception $e) {
            // 清除批量编辑session
            session()->forget(['batch_edit_mode', 'batch_edit_websites', 'batch_edit_ids', 'batch_edit_display_ids', 'batch_edit_display_hosts']);
            return $this->response()->error('更新失败: ' . $e->getMessage());
        }
    }

}
