<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Models\SeoTotal;
use App\Models\SeoSite;
use App\Models\SeoRegion;

class CheckDataConsistency extends Command
{
    protected $signature = 'data:check-consistency {--region=} {--site=} {--days=7}';
    protected $description = '检查统计数据的一致性';

    public function handle()
    {
        $this->info('开始检查数据一致性...');
        
        $region = $this->option('region');
        $site = $this->option('site');
        $days = (int) $this->option('days');
        
        // 检查Redis中的统计数据
        $this->checkRedisData($region, $site, $days);
        
        // 检查MySQL中的统计数据
        $this->checkMysqlData($region, $site, $days);
        
        // 检查数据趋势
        $this->checkDataTrends($region, $site, $days);
        
        $this->info('数据一致性检查完成');
    }
    
    private function checkRedisData($region, $site, $days)
    {
        $this->info('=== Redis数据检查 ===');
        
        $total_keys = Redis::keys("total:*");
        $this->info('Redis中total键总数: ' . count($total_keys));
        
        $today = date('Ymd');
        $today_keys = array_filter($total_keys, function($key) use ($today) {
            return strpos($key, "total:$today:") === 0;
        });
        
        $this->info('今日Redis键数量: ' . count($today_keys));
        
        // 按网站分组统计
        $site_stats = [];
        foreach ($today_keys as $key) {
            $parts = explode(':', $key);
            if (count($parts) >= 4) {
                $date = $parts[1];
                $host = $parts[2];
                $type = $parts[3];
                $count = Redis::get($key);
                
                if (!isset($site_stats[$host])) {
                    $site_stats[$host] = ['spider' => 0, 'jump' => 0];
                }
                
                if ($type == 0) {
                    $site_stats[$host]['spider'] += $count;
                } elseif ($type == 1) {
                    $site_stats[$host]['jump'] += $count;
                }
            }
        }
        
        $this->table(['网站', '蜘蛛', '跳转'], array_map(function($host, $stats) {
            return [$host, $stats['spider'], $stats['jump']];
        }, array_keys($site_stats), $site_stats));
    }
    
    private function checkMysqlData($region, $site, $days)
    {
        $this->info('=== MySQL数据检查 ===');
        
        $query = SeoTotal::query();
        
        if ($region) {
            $sites = SeoSite::where('region_code', $region)->pluck('host');
            $query->whereIn('site', $sites);
        }
        
        if ($site) {
            $query->where('site', $site);
        }
        
        $start_date = date('Ymd', strtotime("-$days days"));
        $query->where('date', '>=', $start_date);
        
        $records = $query->orderBy('date', 'desc')->get();
        
        $this->info('MySQL记录总数: ' . $records->count());
        
        // 今日数据
        $today = date('Ymd');
        $today_records = $records->where('date', $today);
        
        $this->info('今日MySQL记录数: ' . $today_records->count());
        
        $today_spider = $today_records->sum('spider');
        $today_jump = $today_records->sum('jump');
        
        $this->info("今日总计 - 蜘蛛: $today_spider, 跳转: $today_jump");
        
        // 显示最近的记录
        $recent_records = $records->take(10);
        $table_data = $recent_records->map(function($record) {
            return [
                $record->site,
                $record->date,
                $record->spider,
                $record->jump,
                date('Y-m-d H:i:s', strtotime($record->updated_at))
            ];
        })->toArray();
        
        $this->table(['网站', '日期', '蜘蛛', '跳转', '更新时间'], $table_data);
    }
    
    private function checkDataTrends($region, $site, $days)
    {
        $this->info('=== 数据趋势检查 ===');
        
        $query = SeoTotal::query();
        
        if ($region) {
            $sites = SeoSite::where('region_code', $region)->pluck('host');
            $query->whereIn('site', $sites);
        }
        
        if ($site) {
            $query->where('site', $site);
        }
        
        $start_date = date('Ymd', strtotime("-$days days"));
        $query->where('date', '>=', $start_date);
        
        $daily_stats = $query->selectRaw('date, SUM(spider) as total_spider, SUM(jump) as total_jump')
                            ->groupBy('date')
                            ->orderBy('date', 'desc')
                            ->get();
        
        $this->info('每日统计趋势:');
        
        $table_data = $daily_stats->map(function($stat) {
            $date_formatted = date('Y-m-d', strtotime($stat->date));
            return [
                $date_formatted,
                $stat->total_spider,
                $stat->total_jump,
                $stat->total_spider + $stat->total_jump
            ];
        })->toArray();
        
        $this->table(['日期', '蜘蛛', '跳转', '总计'], $table_data);
        
        // 检查异常数据
        $this->checkAnomalies($daily_stats);
    }
    
    private function checkAnomalies($daily_stats)
    {
        $this->info('=== 异常数据检查 ===');
        
        $anomalies = [];
        $prev_stat = null;
        
        foreach ($daily_stats as $stat) {
            if ($prev_stat) {
                // 检查数据是否异常下降
                $spider_change = $stat->total_spider - $prev_stat->total_spider;
                $jump_change = $stat->total_jump - $prev_stat->total_jump;
                
                // 如果下降超过50%，标记为异常
                if ($prev_stat->total_spider > 0 && $spider_change < -($prev_stat->total_spider * 0.5)) {
                    $anomalies[] = [
                        'date' => $stat->date,
                        'type' => '蜘蛛数据异常下降',
                        'change' => $spider_change,
                        'percentage' => round(($spider_change / $prev_stat->total_spider) * 100, 2) . '%'
                    ];
                }
                
                if ($prev_stat->total_jump > 0 && $jump_change < -($prev_stat->total_jump * 0.5)) {
                    $anomalies[] = [
                        'date' => $stat->date,
                        'type' => '跳转数据异常下降',
                        'change' => $jump_change,
                        'percentage' => round(($jump_change / $prev_stat->total_jump) * 100, 2) . '%'
                    ];
                }
            }
            
            $prev_stat = $stat;
        }
        
        if (empty($anomalies)) {
            $this->info('✅ 未发现异常数据');
        } else {
            $this->warn('⚠️  发现异常数据:');
            $this->table(['日期', '类型', '变化量', '变化百分比'], $anomalies);
        }
    }
}
