{{-- 详细统计页面 --}}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">
                        @if($region)
                            @php
                                $flags = [
                                    'br' => '🇧🇷', 'pk' => '🇵🇰', 'in' => '🇮🇳', 'us' => '🇺🇸',
                                    'id' => '🇮🇩', 'th' => '🇹🇭', 'mx' => '🇲🇽', 'ng' => '🇳🇬',
                                    'bd' => '🇧🇩', 'ph' => '🇵🇭', 'vn' => '🇻🇳', 'jp' => '🇯🇵',
                                    'default' => '🌍'
                                ];
                                $flag = $flags[$region->code] ?? '🌍';
                            @endphp
                            {{ $flag }} {{ $region->name }} - 详细流量统计
                        @else
                            🌐 全部地区 - 详细流量统计
                        @endif
                    </h4>
                    <div class="card-tools">
                        <span class="badge badge-info">最近 {{ $days }} 天</span>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 总览统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="info-box bg-red">
                                <span class="info-box-icon"><i class="fa fa-external-link"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">今日跳转量</span>
                                    <span class="info-box-number">{{ number_format($trafficStats['today_jump']) }}</span>
                                    <small>次</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-orange">
                                <span class="info-box-icon"><i class="fa fa-bug"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">今日蜘蛛量</span>
                                    <span class="info-box-number">{{ number_format($trafficStats['today_spider']) }}</span>
                                    <small>次</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-teal">
                                <span class="info-box-icon"><i class="fa fa-line-chart"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总跳转量</span>
                                    <span class="info-box-number">{{ number_format($trafficStats['total_jump']) }}</span>
                                    <small>{{ $days }}天累计</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="info-box bg-navy">
                                <span class="info-box-icon"><i class="fa fa-search"></i></span>
                                <div class="info-box-content">
                                    <span class="info-box-text">总蜘蛛量</span>
                                    <span class="info-box-number">{{ number_format($trafficStats['total_spider']) }}</span>
                                    <small>{{ $days }}天累计</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 图表区域 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">📈 流量趋势图</h5>
                                </div>
                                <div class="card-body">
                                    <canvas id="trafficChart" height="100"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 快速统计概览 -->
                    <div class="row mt-4">
                        @php
                            $totalSites = count($siteStats);
                            $activeSites = collect($siteStats)->where('site.state', 1)->count();
                            $totalTodayJump = collect($siteStats)->sum('today_jump');
                            $totalTodaySpider = collect($siteStats)->sum('today_spider');
                            $totalYesterdayJump = collect($siteStats)->sum('yesterday_jump');
                            $totalYesterdaySpider = collect($siteStats)->sum('yesterday_spider');
                            $jumpGrowth = $totalYesterdayJump > 0 ? round((($totalTodayJump - $totalYesterdayJump) / $totalYesterdayJump) * 100, 1) : 0;
                            $spiderGrowth = $totalYesterdaySpider > 0 ? round((($totalTodaySpider - $totalYesterdaySpider) / $totalYesterdaySpider) * 100, 1) : 0;
                        @endphp

                        <div class="col-md-3">
                            <div class="card bg-gradient-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3 class="mb-0">{{ number_format($totalTodayJump) }}</h3>
                                            <p class="mb-0">今日总跳转</p>
                                            <small class="opacity-75">
                                                昨日: {{ number_format($totalYesterdayJump) }}
                                                @if($jumpGrowth != 0)
                                                    <span class="ml-1">
                                                        @if($jumpGrowth > 0)
                                                            <i class="fa fa-arrow-up"></i> +{{ $jumpGrowth }}%
                                                        @else
                                                            <i class="fa fa-arrow-down"></i> {{ $jumpGrowth }}%
                                                        @endif
                                                    </span>
                                                @endif
                                            </small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-external-link fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-gradient-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3 class="mb-0">{{ number_format($totalTodaySpider) }}</h3>
                                            <p class="mb-0">今日总蜘蛛</p>
                                            <small class="opacity-75">
                                                昨日: {{ number_format($totalYesterdaySpider) }}
                                                @if($spiderGrowth != 0)
                                                    <span class="ml-1">
                                                        @if($spiderGrowth > 0)
                                                            <i class="fa fa-arrow-up"></i> +{{ $spiderGrowth }}%
                                                        @else
                                                            <i class="fa fa-arrow-down"></i> {{ $spiderGrowth }}%
                                                        @endif
                                                    </span>
                                                @endif
                                            </small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-spider fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-gradient-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3 class="mb-0">{{ $activeSites }}</h3>
                                            <p class="mb-0">活跃网站</p>
                                            <small class="opacity-75">总计: {{ $totalSites }} 个网站</small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-globe fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-3">
                            <div class="card bg-gradient-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            @php
                                                $avgJump = $activeSites > 0 ? round($totalTodayJump / $activeSites) : 0;
                                            @endphp
                                            <h3 class="mb-0">{{ number_format($avgJump) }}</h3>
                                            <p class="mb-0">平均跳转</p>
                                            <small class="opacity-75">每个活跃网站</small>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-chart-bar fa-2x opacity-75"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 站点详细统计表格 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">🌐 站点详细统计</h5>
                                    <div class="card-tools">
                                        <span class="badge badge-info">按跳转量优先排序</span>
                                        <span class="badge badge-secondary ml-1">今日 vs 昨日对比</span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-hover" id="siteStatsTable">
                                            <thead class="thead-dark">
                                                <tr>
                                                    <th width="22%">
                                                        <i class="fa fa-globe mr-1"></i>网站域名
                                                    </th>
                                                    <th width="13%" class="text-center">
                                                        <i class="fa fa-external-link text-danger mr-1"></i>今日跳转
                                                    </th>
                                                    <th width="13%" class="text-center">
                                                        <i class="fa fa-spider text-warning mr-1"></i>今日蜘蛛
                                                    </th>
                                                    <th width="13%" class="text-center">
                                                        <i class="fa fa-history text-muted mr-1"></i>昨日跳转
                                                    </th>
                                                    <th width="13%" class="text-center">
                                                        <i class="fa fa-history text-muted mr-1"></i>昨日蜘蛛
                                                    </th>
                                                    <th width="16%" class="text-center">
                                                        <i class="fa fa-chart-line mr-1"></i>变化趋势
                                                    </th>
                                                    <th width="10%" class="text-center">
                                                        <i class="fa fa-clock mr-1"></i>最后爬取
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($siteStats as $index => $stat)
                                                    @php
                                                        // 根据跳转量设置行的背景色
                                                        $rowClass = '';
                                                        if ($stat['today_jump'] > 1000) {
                                                            $rowClass = 'table-success'; // 绿色 - 高流量
                                                        } elseif ($stat['today_jump'] > 100) {
                                                            $rowClass = 'table-info'; // 蓝色 - 中等流量
                                                        } elseif ($stat['today_jump'] > 0) {
                                                            $rowClass = 'table-warning'; // 黄色 - 低流量
                                                        } else {
                                                            $rowClass = 'table-light'; // 灰色 - 无流量
                                                        }


                                                    @endphp
                                                    <tr class="{{ $rowClass }}">
                                                        <td>
                                                            <div class="domain-cell">
                                                                <strong class="text-primary domain-name">{{ $stat['site']->host }}</strong>
                                                            </div>
                                                        </td>
                                                        <td class="text-center">
                                                            @if($stat['today_jump'] > 0)
                                                                <div class="stat-number-container">
                                                                    <span class="stat-number text-danger font-weight-bold">
                                                                        {{ number_format($stat['today_jump']) }}
                                                                    </span>
                                                                    <div class="stat-bar bg-danger" style="width: {{ min(100, ($stat['today_jump'] / max(1, collect($siteStats)->max('today_jump'))) * 100) }}%"></div>
                                                                </div>
                                                            @else
                                                                <span class="text-muted">0</span>
                                                            @endif
                                                        </td>
                                                        <td class="text-center">
                                                            @if($stat['today_spider'] > 0)
                                                                <div class="stat-number-container">
                                                                    <span class="stat-number text-warning font-weight-bold">
                                                                        {{ number_format($stat['today_spider']) }}
                                                                    </span>
                                                                    <div class="stat-bar bg-warning" style="width: {{ min(100, ($stat['today_spider'] / max(1, collect($siteStats)->max('today_spider'))) * 100) }}%"></div>
                                                                </div>
                                                            @else
                                                                <span class="text-muted">0</span>
                                                            @endif
                                                        </td>
                                                        <td class="text-center">
                                                            <small class="text-muted">
                                                                {{ number_format($stat['yesterday_jump']) }}
                                                            </small>
                                                        </td>
                                                        <td class="text-center">
                                                            <small class="text-muted">
                                                                {{ number_format($stat['yesterday_spider']) }}
                                                            </small>
                                                        </td>
                                                        <td class="text-center">
                                                            @if($stat['jump_change'] > 0)
                                                                <div class="change-indicator change-up">
                                                                    <i class="fa fa-arrow-up"></i>
                                                                    <div class="change-text">
                                                                        <strong>+{{ number_format($stat['jump_change']) }}</strong>
                                                                        @if($stat['jump_change_percent'] != 0)
                                                                            <small class="d-block">(+{{ $stat['jump_change_percent'] }}%)</small>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            @elseif($stat['jump_change'] < 0)
                                                                <div class="change-indicator change-down">
                                                                    <i class="fa fa-arrow-down"></i>
                                                                    <div class="change-text">
                                                                        <strong>{{ number_format($stat['jump_change']) }}</strong>
                                                                        @if($stat['jump_change_percent'] != 0)
                                                                            <small class="d-block">({{ $stat['jump_change_percent'] }}%)</small>
                                                                        @endif
                                                                    </div>
                                                                </div>
                                                            @else
                                                                <div class="change-indicator change-neutral">
                                                                    <i class="fa fa-minus"></i>
                                                                    <div class="change-text">
                                                                        <strong>0</strong>
                                                                        <small class="d-block">无变化</small>
                                                                    </div>
                                                                </div>
                                                            @endif
                                                        </td>
                                                        <td class="text-center">
                                                            @if($stat['site']->last_time > 0)
                                                                @php
                                                                    $lastTime = $stat['site']->last_time;
                                                                    $now = time();
                                                                    $diff = $now - $lastTime;

                                                                    // 计算时间差
                                                                    if ($diff < 3600) { // 1小时内
                                                                        $timeAgo = floor($diff / 60) . '分钟前';
                                                                        $timeClass = 'text-success';
                                                                        $timeIcon = 'fa-clock text-success';
                                                                    } elseif ($diff < 86400) { // 24小时内
                                                                        $timeAgo = floor($diff / 3600) . '小时前';
                                                                        $timeClass = 'text-warning';
                                                                        $timeIcon = 'fa-clock text-warning';
                                                                    } elseif ($diff < 604800) { // 7天内
                                                                        $timeAgo = floor($diff / 86400) . '天前';
                                                                        $timeClass = 'text-danger';
                                                                        $timeIcon = 'fa-clock text-danger';
                                                                    } else { // 超过7天
                                                                        $timeAgo = date('m-d', $lastTime);
                                                                        $timeClass = 'text-muted';
                                                                        $timeIcon = 'fa-calendar text-muted';
                                                                    }
                                                                @endphp
                                                                <div class="last-crawl-info">
                                                                    <div class="{{ $timeClass }} font-weight-bold">
                                                                        <i class="fa {{ $timeIcon }} mr-1"></i>
                                                                        {{ $timeAgo }}
                                                                    </div>
                                                                    <small class="text-muted d-block">
                                                                        {{ date('m-d H:i', $lastTime) }}
                                                                    </small>
                                                                </div>
                                                            @else
                                                                <div class="last-crawl-info">
                                                                    <div class="text-muted">
                                                                        <i class="fa fa-question-circle mr-1"></i>
                                                                        未爬取
                                                                    </div>
                                                                    <small class="text-muted d-block">-</small>
                                                                </div>
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="7" class="text-center text-muted py-5">
                                                            <div class="empty-state">
                                                                <i class="fa fa-globe fa-3x mb-3 text-muted"></i>
                                                                <h5>该地区暂无网站数据</h5>
                                                                <p>请先添加网站到该地区</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 每日汇总数据表格 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">📊 每日汇总数据</h5>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead>
                                                <tr>
                                                    <th>日期</th>
                                                    <th>跳转量</th>
                                                    <th>蜘蛛量</th>
                                                    <th>总访问量</th>
                                                    <th>跳转率</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($dailyStats as $date => $stats)
                                                    @php
                                                        $total = $stats['jump'] + $stats['spider'];
                                                        $jumpRate = $total > 0 ? round(($stats['jump'] / $total) * 100, 1) : 0;
                                                        $formattedDate = \Carbon\Carbon::createFromFormat('Ymd', $date)->format('Y-m-d');
                                                        $dayOfWeek = \Carbon\Carbon::createFromFormat('Ymd', $date)->format('l');
                                                    @endphp
                                                    <tr>
                                                        <td>
                                                            {{ $formattedDate }}
                                                            <small class="text-muted d-block">{{ $dayOfWeek }}</small>
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-danger">{{ number_format($stats['jump']) }}</span>
                                                        </td>
                                                        <td>
                                                            <span class="badge badge-warning">{{ number_format($stats['spider']) }}</span>
                                                        </td>
                                                        <td>
                                                            <strong>{{ number_format($total) }}</strong>
                                                        </td>
                                                        <td>
                                                            <div class="progress" style="height: 20px;">
                                                                <div class="progress-bar bg-danger" style="width: {{ $jumpRate }}%">
                                                                    {{ $jumpRate }}%
                                                                </div>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                @empty
                                                    <tr>
                                                        <td colspan="5" class="text-center text-muted">
                                                            暂无数据
                                                        </td>
                                                    </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 表格整体样式 */
#siteStatsTable {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

#siteStatsTable .thead-dark th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    border: none;
    padding: 15px 10px;
    font-size: 0.9em;
}

#siteStatsTable td {
    vertical-align: middle;
    padding: 12px 10px;
    border-color: #e9ecef;
}

/* 行背景色 */
.table-success {
    background-color: rgba(40, 167, 69, 0.1) !important;
    border-left: 4px solid #28a745;
}

.table-info {
    background-color: rgba(23, 162, 184, 0.1) !important;
    border-left: 4px solid #17a2b8;
}

.table-warning {
    background-color: rgba(255, 193, 7, 0.1) !important;
    border-left: 4px solid #ffc107;
}

.table-light {
    background-color: rgba(248, 249, 250, 0.8) !important;
    border-left: 4px solid #dee2e6;
}

/* 数据显示容器 */
.stat-number-container {
    position: relative;
    padding: 8px 12px;
    background: rgba(255,255,255,0.8);
    border-radius: 6px;
    margin: 0 auto;
    max-width: 120px;
}

.stat-number {
    font-size: 1.1em;
    font-weight: 700;
    display: block;
    position: relative;
    z-index: 2;
}

.stat-bar {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    border-radius: 0 0 6px 6px;
    opacity: 0.6;
}

/* 变化指示器 */
.change-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border-radius: 8px;
    margin: 0 auto;
    max-width: 100px;
}

.change-up {
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(40, 167, 69, 0.2) 100%);
    color: #28a745;
    border: 1px solid rgba(40, 167, 69, 0.3);
}

.change-down {
    background: linear-gradient(135deg, rgba(220, 53, 69, 0.1) 0%, rgba(220, 53, 69, 0.2) 100%);
    color: #dc3545;
    border: 1px solid rgba(220, 53, 69, 0.3);
}

.change-neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
    border: 1px solid rgba(108, 117, 125, 0.3);
}

.change-indicator i {
    font-size: 1.2em;
    margin-right: 6px;
}

.change-text {
    text-align: center;
    line-height: 1.2;
}





/* 空状态样式 */
.empty-state {
    padding: 40px 20px;
}

.empty-state i {
    opacity: 0.5;
}

.empty-state h5 {
    color: #6c757d;
    margin-bottom: 10px;
}

.empty-state p {
    color: #adb5bd;
    margin-bottom: 0;
}

/* 域名链接样式 */
.text-primary {
    color: #007bff !important;
    font-weight: 600;
}



/* 响应式调整 */
@media (max-width: 768px) {
    #siteStatsTable th,
    #siteStatsTable td {
        font-size: 0.8em;
        padding: 8px 5px;
    }

    .stat-number-container {
        max-width: 80px;
        padding: 6px 8px;
    }

    .stat-number {
        font-size: 0.9em;
    }

    .change-indicator {
        max-width: 80px;
        padding: 6px;
    }

    .rank-badge {
        min-width: 30px;
        height: 30px;
        font-size: 0.8em;
    }
}

/* 最后爬取时间样式 */
.last-crawl-info {
    padding: 6px 8px;
    border-radius: 6px;
    background: rgba(255,255,255,0.8);
    margin: 0 auto;
    max-width: 100px;
    text-align: center;
}

.last-crawl-info .text-success {
    color: #28a745 !important;
}

.last-crawl-info .text-warning {
    color: #ffc107 !important;
}

.last-crawl-info .text-danger {
    color: #dc3545 !important;
}

.last-crawl-info .text-muted {
    color: #6c757d !important;
}

.last-crawl-info small {
    font-size: 0.75em;
    line-height: 1.2;
}

/* 悬停效果 */
#siteStatsTable tbody tr:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.2s ease;
}

/* 动画效果 */
.stat-number-container,
.change-indicator,
.last-crawl-info {
    transition: all 0.2s ease;
}

.stat-number-container:hover,
.last-crawl-info:hover {
    transform: scale(1.05);
}

/* 域名样式 */
.domain-cell {
    padding: 8px 0;
}

.domain-name {
    font-size: 1.1em;
    font-weight: 600;
    color: #2c3e50 !important;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 准备图表数据
const chartData = {!! json_encode($dailyStats) !!};
const labels = [];
const jumpData = [];
const spiderData = [];

// 处理数据并按日期排序
const sortedDates = Object.keys(chartData).sort();
sortedDates.forEach(date => {
    const formattedDate = new Date(date.substring(0,4), date.substring(4,6)-1, date.substring(6,8));
    labels.push(formattedDate.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }));
    jumpData.push(chartData[date].jump || 0);
    spiderData.push(chartData[date].spider || 0);
});

// 创建图表
const ctx = document.getElementById('trafficChart').getContext('2d');
const trafficChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: labels,
        datasets: [{
            label: '跳转量',
            data: jumpData,
            borderColor: 'rgb(220, 53, 69)',
            backgroundColor: 'rgba(220, 53, 69, 0.1)',
            tension: 0.4
        }, {
            label: '蜘蛛量',
            data: spiderData,
            borderColor: 'rgb(255, 193, 7)',
            backgroundColor: 'rgba(255, 193, 7, 0.1)',
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            title: {
                display: true,
                text: '最近{{ $days }}天流量趋势'
            },
            legend: {
                position: 'top',
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    callback: function(value) {
                        return new Intl.NumberFormat().format(value);
                    }
                }
            }
        },
        interaction: {
            intersect: false,
        }
    }
});
</script>
