<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Models\SeoSite;
use App\Models\Mongo;
use Illuminate\Support\Facades\Redis;

class DelCacheAcion extends RowAction
{
    /**
     * @return string
     */
	protected $title = '清空缓存';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        //dump($this->getKey());
        $site = SeoSite::find($this->getKey());
        if (empty($site)){
            return $this->response()->error('删除失败');
        }
        $mongo = new Mongo();
        $mongo->getCollection("cache")->deleteMany(['site' => $site->host]);
        $keys = Redis::keys("cache:".$site->host.":*");
        $keys2 = Redis::keys("unread_cache:".$site->host.":*");
        $keys = array_merge($keys, $keys2);
        foreach ($keys as $key) {
            Redis::del($key);
        }
        // Redis::del("cache:".$site->host.":*");
        return $this->response()
            ->success($site->host.':缓存删除成功');
    }

    /**
	 * @return string|array|void
	 */
	public function confirm()
	{
		// return ['Confirm?', 'contents'];
	}

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
