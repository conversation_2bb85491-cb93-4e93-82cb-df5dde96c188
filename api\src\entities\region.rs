use serde::{Deserialize, Serialize};
use sqlx::{MySqlPool, Row};
use std::collections::HashMap;
use chrono::{DateTime, Utc};

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Region {
    pub id: i64,
    pub code: String,
    pub name: String,
    pub language: String,
    pub timezone: String,
    pub config: HashMap<String, serde_json::Value>,
    pub data_version: String,
    pub fallback_region: String,
    pub priority: i32,
    pub status: bool,
    pub jump_script: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

impl Region {
    /// 从数据库行创建Region实例
    pub fn from_row(row: &sqlx::mysql::MySqlRow) -> Result<Self, sqlx::Error> {
        let config_str: String = row.try_get("config").unwrap_or_else(|_| "{}".to_string());
        let config: HashMap<String, serde_json::Value> = serde_json::from_str(&config_str)
            .unwrap_or_else(|_| HashMap::new());

        Ok(Region {
            id: row.get("id"),
            code: row.get("code"),
            name: row.get("name"),
            language: row.get("language"),
            timezone: row.get("timezone"),
            config,
            data_version: row.get("data_version"),
            fallback_region: row.get("fallback_region"),
            priority: row.get("priority"),
            status: row.get::<i8, _>("status") != 0,
            jump_script: row.try_get("jump_script").ok(),
            created_at: Utc::now(), // 暂时使用当前时间，实际应该从数据库获取
            updated_at: Utc::now(), // 暂时使用当前时间，实际应该从数据库获取
        })
    }

    /// 获取所有启用的地区
    pub async fn get_active_regions(pool: &MySqlPool) -> Result<Vec<Region>, sqlx::Error> {
        let rows = sqlx::query("SELECT * FROM seo_regions WHERE status = 1 ORDER BY priority ASC, code ASC")
            .fetch_all(pool)
            .await?;

        let mut regions = Vec::new();
        for row in rows {
            if let Ok(region) = Region::from_row(&row) {
                regions.push(region);
            }
        }

        Ok(regions)
    }

    /// 根据代码获取地区
    pub async fn get_by_code(pool: &MySqlPool, code: &str) -> Result<Option<Region>, sqlx::Error> {
        let row = sqlx::query("SELECT * FROM seo_regions WHERE code = ? AND status = 1")
            .bind(code)
            .fetch_optional(pool)
            .await?;

        match row {
            Some(row) => Ok(Some(Region::from_row(&row)?)),
            None => Ok(None),
        }
    }

    /// 获取地区的配置值
    pub fn get_config_value(&self, key: &str) -> Option<&serde_json::Value> {
        self.config.get(key)
    }

    /// 获取地区的配置字符串值
    pub fn get_config_string(&self, key: &str, default: &str) -> String {
        self.config.get(key)
            .and_then(|v| v.as_str())
            .unwrap_or(default)
            .to_string()
    }

    /// 检查地区是否有效
    pub fn is_valid(&self) -> bool {
        self.status && !self.code.is_empty() && !self.name.is_empty()
    }

    /// 获取数据路径 - 新路径结构: app/{region_code}/data
    pub fn get_data_path(&self, _base_path: &str) -> String {
        format!("app/{}/data", self.code)
    }

    /// 获取模板路径 - 新路径结构: app/{region_code}/template
    pub fn get_template_path(&self, _base_path: &str) -> String {
        format!("app/{}/template", self.code)
    }

    /// 获取特定类型的模板路径
    pub fn get_template_type_path(&self, template_type: &str) -> String {
        format!("app/{}/template/{}", self.code, template_type)
    }
}

#[derive(Debug, Clone)]
pub struct RegionManager {
    pool: MySqlPool,
    regions: std::sync::Arc<tokio::sync::RwLock<HashMap<String, Region>>>,
    default_region: String,
}

impl RegionManager {
    /// 创建新的地区管理器
    pub fn new(pool: MySqlPool, default_region: String) -> Self {
        Self {
            pool,
            regions: std::sync::Arc::new(tokio::sync::RwLock::new(HashMap::new())),
            default_region,
        }
    }

    /// 初始化地区管理器，加载所有地区
    pub async fn initialize(&self) -> Result<(), sqlx::Error> {
        let regions = Region::get_active_regions(&self.pool).await?;
        let mut region_map = self.regions.write().await;
        
        region_map.clear();
        for region in regions {
            region_map.insert(region.code.clone(), region);
        }

        println!("地区管理器初始化完成，加载了 {} 个地区", region_map.len());
        Ok(())
    }

    /// 获取地区
    pub async fn get_region(&self, code: &str) -> Option<Region> {
        let regions = self.regions.read().await;
        regions.get(code).cloned()
    }

    /// 获取地区或默认地区
    pub async fn get_region_or_default(&self, code: &str) -> Option<Region> {
        let regions = self.regions.read().await;
        
        // 先尝试获取指定地区
        if let Some(region) = regions.get(code) {
            return Some(region.clone());
        }
        
        // 如果没有找到，尝试获取默认地区
        if code != self.default_region {
            if let Some(region) = regions.get(&self.default_region) {
                return Some(region.clone());
            }
        }
        
        None
    }

    /// 获取所有地区
    pub async fn get_all_regions(&self) -> Vec<Region> {
        let regions = self.regions.read().await;
        regions.values().cloned().collect()
    }

    /// 重新加载地区配置
    pub async fn reload(&self) -> Result<(), sqlx::Error> {
        self.initialize().await
    }

    /// 获取地区数量
    pub async fn get_region_count(&self) -> usize {
        let regions = self.regions.read().await;
        regions.len()
    }

    /// 检查地区是否存在
    pub async fn region_exists(&self, code: &str) -> bool {
        let regions = self.regions.read().await;
        regions.contains_key(code)
    }

    /// 获取默认地区代码
    pub fn get_default_region_code(&self) -> &str {
        &self.default_region
    }
}

impl Region {
    /// 根据地区代码查找地区
    pub async fn find_by_code(pool: &MySqlPool, code: &str) -> Result<Option<Self>, sqlx::Error> {
        let row = sqlx::query("SELECT * FROM seo_regions WHERE code = ? AND status = 1")
            .bind(code)
            .fetch_optional(pool)
            .await?;

        match row {
            Some(row) => Ok(Some(Self::from_row(&row)?)),
            None => Ok(None),
        }
    }
}
