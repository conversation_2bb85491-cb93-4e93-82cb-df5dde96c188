<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SpiderLog;

class ImportSpiderLogs extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'spider:import {--limit=10000 : 每次导入的最大记录数}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '从Redis导入蜘蛛爬行日志到MySQL数据库';

    /**
     * 创建命令
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始从Redis导入蜘蛛爬行日志...');
        
        $limit = (int)$this->option('limit');
        $startTime = microtime(true);
        
        $count = SpiderLog::importFromRedis($limit);
        
        $endTime = microtime(true);
        $timeUsed = round($endTime - $startTime, 2);
        
        $this->info("导入完成！共导入 {$count} 条记录，耗时 {$timeUsed} 秒。");
        
        return 0;
    }
} 