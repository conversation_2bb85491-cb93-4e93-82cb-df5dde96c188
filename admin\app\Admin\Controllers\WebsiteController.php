<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Website;
use App\Models\Website as WebsiteModel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\File;

class WebsiteController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {

        return Grid::make(new Website(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('host_list');
            $grid->column('list_rules');
            $grid->column('detail_rules');
            $grid->column('https')->bool();
            $grid->column('open_cache')->bool();
            $grid->column('state')->bool();
            $grid->column('www')->bool();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
            });
            // $grid->tools(function (Grid\Tools $tools) {
            //     $tools->append('<a class="btn btn-sm btn-success" href="?state=1&refresh=1">更新缓存</a>'); 
            // });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Website(), function (Show $show) {
            $show->field('id');
            $show->field('host_list');
            $show->field('list_rules');
            $show->field('detail_rules');
            $show->field('https');
            $show->field('open_cache');
            $show->field('state');
            $show->field('www');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        $self = $this;
        return Form::make(new Website(), function (Form $form) use($self) {
            $form->display('id');
            $form->textarea('host_list');
            $form->text('list_rules');
            $form->text('detail_rules');
            $form->radio('https')->options(['1' => '开启', '0' => '关闭'])->default('0');
            $form->radio('open_cache')->options(['1' => '开启', '0' => '关闭'])->default('0');
            $form->radio('state')->options(['1' => '开启', '0' => '关闭'])->default('0');
            $form->radio('www')->options(['1' => '开启', '0' => '关闭'])->default('0');
           
        });
    }

}
