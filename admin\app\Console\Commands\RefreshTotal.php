<?php

namespace App\Console\Commands;

use App\Models\SeoSite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;
use App\Models\SeoTotal; // 替换成你实际的模型路径

class RefreshTotal extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:total';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '从Redis更新统计数据到数据库';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        //php artisan refresh:total
        $this->info('开始更新统计数据...');

        // 获取并处理爬行数据
        $total_list = Redis::keys("total:*");
        $this->info('找到 ' . count($total_list) . ' 条爬行记录');

        $processed_count = 0;
        $error_count = 0;

        foreach ($total_list as $keyName) {
            try {
                $count = Redis::get($keyName);
                $info = explode(':', $keyName);

                // 确保键格式正确
                if (count($info) < 4) {
                    $this->warn("跳过无效的键: $keyName");
                    $error_count++;
                    continue;
                }

                $date = $info[1];
                $site = $info[2];
                $t = $info[3];

                // 验证数据有效性
                if (!is_numeric($count) || $count <= 0) {
                    $this->warn("跳过无效数据: $keyName, 数量: $count");
                    $error_count++;
                    // 删除无效的Redis键
                    Redis::del($keyName);
                    continue;
                }

                $this->info("处理: $site, 日期: $date, 类型: $t, 数量: $count");

                // 🔧 修复：使用累加模式而不是覆盖模式
                $existing = SeoTotal::where(['date' => $date, 'site' => $site])->first();

                if ($existing) {
                    // 更新现有记录（累加）
                    if ($t == 1) {
                        $old_jump = $existing->jump;
                        $existing->jump += $count;
                        $this->info("累加跳转: $site, 原值: $old_jump, 增加: $count, 新值: {$existing->jump}");
                    } else {
                        $old_spider = $existing->spider;
                        $existing->spider += $count;
                        $this->info("累加蜘蛛: $site, 原值: $old_spider, 增加: $count, 新值: {$existing->spider}");
                    }
                    $existing->save();
                } else {
                    // 创建新记录
                    $data = [
                        'date' => $date,
                        'site' => $site,
                        'jump' => $t == 1 ? $count : 0,
                        'spider' => $t == 0 ? $count : 0,
                    ];
                    SeoTotal::create($data);
                    $this->info("创建新记录: $site, 日期: $date");
                }

                // 🔧 修复：同步成功后立即删除Redis数据，避免重复计算
                Redis::del($keyName);
                $this->info("删除Redis数据: $keyName");
                $processed_count++;

            } catch (\Exception $e) {
                $this->error("处理键 $keyName 时出错: " . $e->getMessage());
                $error_count++;
            }
        }

        $this->info("统计数据处理完成 - 成功: $processed_count, 错误: $error_count");

        // 获取并处理最后爬行时间
        $lasttime_list = Redis::keys('lasttime:*');
        $this->info('找到 ' . count($lasttime_list) . ' 条最后爬行时间记录');

        $lasttime_processed = 0;
        $lasttime_errors = 0;

        foreach ($lasttime_list as $keyName) {
            try {
                $time = Redis::get($keyName);
                $info = explode(':', $keyName);

                // 确保键格式正确
                if (count($info) < 2) {
                    $this->warn("跳过无效的键: $keyName");
                    $lasttime_errors++;
                    continue;
                }

                $site = $info[1];

                // 验证时间戳有效性
                if (!is_numeric($time) || $time <= 0) {
                    $this->warn("跳过无效时间戳: $keyName, 值: $time");
                    Redis::del($keyName);
                    $lasttime_errors++;
                    continue;
                }

                $this->info("更新最后爬行时间: $site, 时间: " . date('Y-m-d H:i:s', $time));

                // 🔧 修复：使用正确的字段名 last_time
                $seoSite = \App\Models\SeoSite::where('host', $site)->first();
                if ($seoSite) {
                    $seoSite->last_time = $time;
                    $seoSite->save();
                    $this->info("更新成功: $site");
                    $lasttime_processed++;
                } else {
                    $this->warn("未找到网站记录: $site");
                    $lasttime_errors++;
                }
            } catch (\Exception $e) {
                $this->error("处理最后爬行时间 $keyName 时出错: " . $e->getMessage());
                $lasttime_errors++;
            }
        }

        $this->info("最后爬行时间处理完成 - 成功: $lasttime_processed, 错误: $lasttime_errors");
        $this->info('所有数据更新完成!');
        return 1;
    }
}
