<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Http\Request;
use App\Models\SeoRegion;
use App\Models\SeoSite;

class BatchEditWebsite extends BatchAction
{
    use HasPermissions;

    /**
     * @return string
     */
    public function title()
    {
        return '批量编辑';
    }

    /**
     * 处理批量编辑操作
     */
    public function handle(Request $request)
    {
        $keys = $this->getKey();

        if (empty($keys)) {
            return $this->response()->error('请选择要编辑的网站');
        }

        // 根据当前URL决定重定向到哪个批量编辑页面
        $currentUrl = $request->url();
        if (strpos($currentUrl, 'website-management') !== false) {
            $url = admin_url('seo/website-management/batch-edit?ids=' . implode(',', $keys));
        } else {
            $url = admin_url('seo/site/batch-edit?ids=' . implode(',', $keys));
        }

        return $this->response()->redirect($url);
    }

    /**
     * 权限检查
     */
    protected function authorize($user): bool
    {
        return true; // 根据需要调整权限
    }
}
