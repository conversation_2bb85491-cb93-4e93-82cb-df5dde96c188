<?php

namespace App\Models; // 替换成你实际的命名空间

use MongoDB\Client;
use MongoDB\Database;
use MongoDB\Collection;
use <PERSON><PERSON><PERSON>\Mongodb\Eloquent\Model;
class Mongo extends Model {

    private  $client;
    private  $database;

    public function __construct() 
    {
        $host = env("MONGO_HOST","127.0.0.1");
        $port = env("MONGO_PORT","27017");
        $database = env("MONGO_DATABASE","");
        $username = env("MONGO_USERNAME","");
        $password = env("MONGO_PASSWORD","");
        // 构建连接字符串
        $uri = "mongodb://";
        if (!empty($username) && !empty($password)) {
            $uri .= "$username:$password@"; 
        }
        $uri .= "$host:$port"; 
        if (!empty($database)) { 
            $uri .= "/$database"; 
        }

        // 添加 authSource 参数
        if (!empty($username)) {
            $uri .= "?authSource=$username";
        }
            
        $this->client = new Client($uri);
        $this->database = $this->client->selectDatabase($database);
    }

    public function getCollection($coll): Collection
    {
        return $this->database->selectCollection($coll);
    }

    // public function insertOne(array $document)
    // {
    //     return $this->getCollection()->insertOne($document);
    // }

    // public function find(array $filter = [], array $options = [])
    // {
    //     return $this->getCollection()->find($filter, $options);
    // }

    // public function findOne(array $filter = [], array $options = [])
    // {
    //     return $this->getCollection()->findOne($filter, $options);
    // }

    // public function updateOne(array $filter, array $update, array $options = [])
    // {
    //     return $this->getCollection()->updateOne($filter, $update, $options);
    // }

    // public function deleteOne(array $filter, array $options = [])
    // {
    //     return $this->getCollection()->deleteOne($filter, $options);
    // }

}