# API项目多地区时区控制使用指南

## 🌍 概述

API项目现在支持针对每个地区设置不同的时区，并能正确处理MongoDB和Redis的缓存过期时间。

## 🚀 主要功能

### 1. 地区时区管理
- 每个地区可以设置独立的时区
- 支持动态更新地区时区配置
- 自动从数据库同步地区时区信息

### 2. 时区感知的缓存管理
- MongoDB TTL索引基于地区时区计算过期时间
- Redis缓存过期时间考虑地区时区差异
- 缓存时间戳使用地区特定时区

### 3. API端点
- 完整的地区时区管理API
- 支持批量更新和统计查询
- 提供时区测试和验证功能

## 📋 API端点列表

### 基础操作
```
GET    /api/timezone/region-timezones           # 获取所有地区时区
GET    /api/timezone/region-timezones/{code}    # 获取特定地区时区
PUT    /api/timezone/region-timezones/{code}    # 更新地区时区
DELETE /api/timezone/region-timezones/{code}    # 删除地区时区缓存
```

### 批量操作
```
POST   /api/timezone/region-timezones/batch-update  # 批量更新地区时区
POST   /api/timezone/region-timezones/reload        # 重新加载配置
```

### 统计和测试
```
GET    /api/timezone/region-timezones/stats         # 获取统计信息
GET    /api/timezone/region-timezones/{code}/test   # 测试地区时区功能
```

## 💡 使用示例

### 1. 获取所有地区时区
```bash
curl -X GET "http://localhost:8080/api/timezone/region-timezones"
```

响应示例：
```json
[
  {
    "region_code": "br",
    "timezone": "America/Sao_Paulo",
    "current_time": "2024-07-06 15:30:45",
    "timestamp": 1720287045
  },
  {
    "region_code": "in",
    "timezone": "Asia/Kolkata",
    "current_time": "2024-07-07 00:00:45",
    "timestamp": 1720287045
  }
]
```

### 2. 更新地区时区
```bash
curl -X PUT "http://localhost:8080/api/timezone/region-timezones/br" \
  -H "Content-Type: application/json" \
  -d '{"timezone": "America/Sao_Paulo"}'
```

### 3. 批量更新地区时区
```bash
curl -X POST "http://localhost:8080/api/timezone/region-timezones/batch-update" \
  -H "Content-Type: application/json" \
  -d '{
    "updates": {
      "br": "America/Sao_Paulo",
      "in": "Asia/Kolkata",
      "us": "America/New_York"
    }
  }'
```

### 4. 测试地区时区功能
```bash
curl -X GET "http://localhost:8080/api/timezone/region-timezones/br/test"
```

响应示例：
```json
{
  "region_code": "br",
  "timezone": "America/Sao_Paulo",
  "current_time": "2024-07-06 15:30:45 -03",
  "timestamp": 1720287045,
  "day_boundaries": {
    "start": 1720224000,
    "end": 1720310399
  },
  "cache_expiration": {
    "1_hour": 1720290645,
    "24_hours": 1720373445
  }
}
```

## 🔧 代码集成示例

### 1. 在路由处理中使用地区时区
```rust
// 获取地区时区
let region_timezone = app_state.region_timezone_context
    .get_region_timezone(&region_code).await;

// 使用地区时区创建缓存
let cache_item = CacheItem::new_with_region_timezone(
    cache_name,
    content,
    site,
    expiration_days,
    &region_timezone
);

// 使用地区时区设置Redis缓存
app_state.redis.set_page_with_region(
    &request_info, 
    &content, 
    &region_timezone
).await;
```

### 2. 时区工具函数使用
```rust
use crate::util::region_timezone_context::RegionTimezoneUtils;

// 获取地区当前时间
let current_time = RegionTimezoneUtils::now_for_region_code(
    &app_state.region_timezone_context,
    "br"
).await;

// 计算地区缓存过期时间
let expiration = RegionTimezoneUtils::calculate_cache_expiration_for_region(
    &app_state.region_timezone_context,
    "br",
    24 // 24小时
).await;

// 检查是否在地区时区下过期
let is_expired = RegionTimezoneUtils::is_expired_in_region_code(
    &app_state.region_timezone_context,
    timestamp,
    "br"
).await;
```

## 🛠️ 配置说明

### 1. 环境变量
```bash
# 默认时区（全局回退时区）
TIME_ZONE=UTC

# 默认地区代码
DEFAULT_REGION=default
```

### 2. 数据库配置
确保 `seo_regions` 表包含 `timezone` 字段：
```sql
ALTER TABLE seo_regions ADD COLUMN timezone VARCHAR(50) DEFAULT 'UTC';
```

### 3. 支持的时区格式
使用标准的IANA时区标识符，例如：
- `America/Sao_Paulo` (巴西)
- `Asia/Kolkata` (印度)
- `America/New_York` (美国东部)
- `Europe/London` (英国)
- `Asia/Shanghai` (中国)

## 📊 性能优化建议

### 1. 缓存策略
- 地区时区信息在内存中缓存，避免频繁数据库查询
- 使用 `RegionTimezoneContext` 管理时区映射
- 支持热更新，无需重启服务

### 2. MongoDB TTL优化
- 使用地区时区计算的过期时间创建TTL索引
- 自动清理过期缓存，减少存储空间
- 支持不同地区的不同过期策略

### 3. Redis缓存优化
- 地区特定的缓存过期时间
- 支持地区时区感知的缓存键管理
- 批量操作减少网络开销

## 🔍 故障排除

### 1. 时区无效错误
```
Error: Invalid timezone 'Invalid/Timezone'
```
解决方案：使用有效的IANA时区标识符

### 2. 地区不存在
```
Error: Region 'unknown' not found
```
解决方案：确保地区在数据库中存在且状态为启用

### 3. 缓存时间异常
检查地区时区配置是否正确，使用测试API验证：
```bash
curl -X GET "http://localhost:8080/api/timezone/region-timezones/{region_code}/test"
```

## 📈 监控和统计

### 1. 获取统计信息
```bash
curl -X GET "http://localhost:8080/api/timezone/region-timezones/stats"
```

### 2. 重新加载配置
```bash
curl -X POST "http://localhost:8080/api/timezone/region-timezones/reload"
```

这个多地区时区控制系统提供了完整的时区管理功能，确保缓存过期时间和时间戳处理都能正确反映各地区的本地时间。
