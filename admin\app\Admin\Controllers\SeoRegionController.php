<?php

namespace App\Admin\Controllers;

use App\Models\SeoRegion;
use App\Admin\Repositories\SeoRegion as SeoRegionRepository;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Illuminate\Http\Request;

class SeoRegionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SeoRegionRepository(), function (Grid $grid) {
            // 添加统计字段到查询
            $grid->model()->select('seo_regions.*')
                         ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code) as site_count')
                         ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code AND seo_site.state = 1) as active_site_count')
                         ->orderBy('priority')
                         ->orderBy('code');
            $grid->column('id')->sortable();
            $grid->column('code', '地区代码')->label('primary');
            $grid->column('name', '地区名称')->limit(20);
            $grid->column('language', '语言')->using([
                'en' => 'English',
                'zh' => '中文', 
                'es' => 'Español',
                'pt' => 'Português',
                'hi' => 'हिन्दी',
                'ar' => 'العربية',
                'fr' => 'Français',
                'de' => 'Deutsch',
                'ja' => '日本語',
                'ko' => '한국어',
                'ru' => 'Русский',
                'it' => 'Italiano'
            ]);
            $grid->column('timezone', '时区')->limit(20);
            $grid->column('priority', '优先级')->sortable();
            $grid->column('site_count', '网站总数')->badge('info');
            $grid->column('active_site_count', '启用网站')->badge('success');
            $grid->column('jump_script', '跳转脚本')->display(function ($value) {
                if (empty($value)) {
                    return '<span class="text-muted">未设置</span>';
                }
                $preview = mb_substr(strip_tags($value), 0, 50);
                return '<span title="' . htmlspecialchars($value) . '">' . $preview . '...</span>';
            });
            $grid->column('status', '状态')->switch();
            $grid->column('created_at', '创建时间')->sortable();
            $grid->column('updated_at', '更新时间')->sortable();

            // 筛选器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('code', '地区代码');
                $filter->like('name', '地区名称');
                $filter->equal('language', '语言')->select(SeoRegion::getSupportedLanguages());
                $filter->equal('status', '状态')->select([1 => '启用', 0 => '禁用']);
            });

            // 🔧 只保留批量启用和禁用，删除使用Dcat Admin默认功能
            $grid->batchActions([
                new \App\Admin\Actions\Grid\BatchEnableRegion(),
                new \App\Admin\Actions\Grid\BatchDisableRegion(),
            ]);

            // 工具栏
            $grid->tools([
                new \App\Admin\Actions\Grid\RegionStatsAction(),
            ]);

            // 🔧 启用删除按钮，支持批量删除
            // $grid->disableDeleteButton(); // 注释掉这行，启用删除功能
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
        });
    }

    /**
     * 删除地区 - 支持单个和批量删除
     */
    public function destroy($id)
    {
        try {
            // 🔧 支持批量删除：处理多个ID（逗号分隔）
            $ids = is_string($id) ? explode(',', $id) : [$id];
            $ids = array_filter(array_map('trim', $ids)); // 清理空值

            if (empty($ids)) {
                return \Dcat\Admin\Admin::json()->error("没有要删除的地区");
            }

            // 检查是否包含重要地区
            $regions = \App\Models\SeoRegion::whereIn('id', $ids)->get();
            $protectedCodes = ['default', 'DEFAULT'];
            $deletedNames = [];
            $failedCount = 0;
            $protectedCount = 0;

            foreach ($regions as $region) {
                try {
                    // 检查是否是受保护的地区
                    if (in_array($region->code, $protectedCodes)) {
                        $protectedCount++;
                        \Log::warning("尝试删除受保护的地区: {$region->code}");
                        continue;
                    }

                    // 检查是否有关联的网站
                    $siteCount = \App\Models\SeoSite::where('region_code', $region->code)->count();
                    if ($siteCount > 0) {
                        $failedCount++;
                        \Log::warning("地区 {$region->code} 有 {$siteCount} 个关联网站，无法删除");
                        continue;
                    }

                    $name = $region->name;
                    $region->delete();
                    $deletedNames[] = $name;
                    \Log::info("删除地区成功: {$name} ({$region->code})");

                } catch (\Exception $e) {
                    $failedCount++;
                    \Log::error("删除地区失败 {$region->code}: " . $e->getMessage());
                }
            }

            // 生成响应消息
            $deletedCount = count($deletedNames);
            $totalCount = count($regions);

            if ($deletedCount > 0) {
                if ($deletedCount == 1) {
                    $message = "地区 {$deletedNames[0]} 删除成功";
                } else {
                    $message = "成功删除 {$deletedCount} 个地区";
                }

                if ($protectedCount > 0) {
                    $message .= "，跳过 {$protectedCount} 个受保护地区";
                }
                if ($failedCount > 0) {
                    $message .= "，{$failedCount} 个地区有关联网站无法删除";
                }

                return \Dcat\Admin\Admin::json()->success($message)->refresh();
            } else {
                $message = "删除失败";
                if ($protectedCount > 0) {
                    $message = "无法删除受保护的地区（default）";
                } elseif ($failedCount > 0) {
                    $message = "选中的地区都有关联网站，无法删除";
                }
                return \Dcat\Admin\Admin::json()->error($message);
            }

        } catch (\Exception $e) {
            \Log::error("删除地区操作失败: " . $e->getMessage());
            return \Dcat\Admin\Admin::json()->error('删除失败: ' . $e->getMessage());
        }
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SeoRegion(), function (Show $show) {
            $show->field('id');
            $show->field('code', '地区代码');
            $show->field('name', '地区名称');
            $show->field('language', '语言');
            $show->field('timezone', '时区');
            $show->field('config', '配置')->json();
            $show->field('data_version', '数据版本');
            $show->field('fallback_region', '回退地区');
            $show->field('priority', '优先级');
            $show->field('jump_script', '跳转脚本')->code();
            $show->field('status', '状态')->using([1 => '启用', 0 => '禁用'])->label();
            $show->field('created_at', '创建时间');
            $show->field('updated_at', '更新时间');

            // 显示统计信息
            $show->divider();
            $show->field('stats', '统计信息')->as(function () {
                $stats = $this->getStats();
                return "总网站数: {$stats['total_sites']}, 启用: {$stats['active_sites']}, 禁用: {$stats['inactive_sites']}";
            });

            // 显示目录状态
            $show->field('directories', '目录状态')->as(function () {
                $dataDir = $this->hasDataDirectory() ? '✓' : '✗';
                $templateDir = $this->hasTemplateDirectory() ? '✓' : '✗';
                return "数据目录: {$dataDir}, 模板目录: {$templateDir}";
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SeoRegionRepository(), function (Form $form) {
            $form->display('id');
            
            $form->text('code', '地区代码')
                ->required()
                ->rules('required|string|max:10|regex:/^[a-z]{2,10}$/')
                ->help('2-10位小写字母，如: br, in, default');
                
            $form->text('name', '地区名称')
                ->required()
                ->rules('required|string|max:100');
                
            $form->select('language', '语言')
                ->options(SeoRegion::getSupportedLanguages())
                ->default('en')
                ->required();
                
            $form->select('timezone', '时区')
                ->options(SeoRegion::getSupportedTimezones())
                ->default('UTC')
                ->required();
                
            $form->number('priority', '优先级')
                ->default(100)
                ->min(1)
                ->max(999)
                ->help('数字越小优先级越高');
                
            $form->text('data_version', '数据版本')
                ->default('1.0')
                ->rules('string|max:50');
                
            $form->select('fallback_region', '回退地区')
                ->options(function () {
                    return SeoRegion::getRegionOptions();
                })
                ->help('当前地区资源不可用时使用的回退地区');
                
            $form->switch('status', '状态')
                ->default(1);

            // 跳转脚本配置
            $form->textarea('jump_script', '跳转脚本')
                ->rows(8)
                ->placeholder('请输入跳转脚本代码，支持JavaScript...')
                ->help('该地区的跳转脚本，用于网站跳转逻辑控制');

            // 配置JSON编辑器
            $form->embeds('config', '地区配置', function ($form) {
                $form->text('currency', '货币代码')->placeholder('如: USD, BRL, INR');
                $form->text('domain_suffix', '域名后缀')->placeholder('如: .com, .com.br, .in');
                $form->text('country_code', '国家代码')->placeholder('如: US, BR, IN');
                $form->number('gmt_offset', 'GMT偏移')->placeholder('如: -3, +5.5');
            });

            $form->divider();
            
            $form->display('created_at', '创建时间');
            $form->display('updated_at', '更新时间');

            // 保存后的处理
            $form->saved(function (Form $form, $result) {
                $model = $form->model();

                // 创建目录
                if ($model->exists) {
                    $model->createDataDirectory();
                    $model->createTemplateDirectory();
                }

                return $result;
            });
        });
    }

    /**
     * 地区统计页面
     */
    public function stats(Content $content)
    {
        return $content
            ->title('地区统计')
            ->description('查看各地区使用情况')
            ->body(view('admin.region-stats', [
                'regions' => SeoRegion::select('seo_regions.*')
                    ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code) as site_count')
                    ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code AND seo_site.state = 1) as active_site_count')
                    ->orderBy('priority')
                    ->orderBy('code')
                    ->get()
            ]));
    }

    /**
     * 批量创建目录
     */
    public function batchCreateDirectories(Request $request)
    {
        $codes = $request->input('codes', []);
        $results = [];
        
        foreach ($codes as $code) {
            $region = SeoRegion::where('code', $code)->first();
            if ($region) {
                $dataResult = $region->createDataDirectory();
                $templateResult = $region->createTemplateDirectory();
                $results[$code] = [
                    'data_directory' => $dataResult,
                    'template_directory' => $templateResult
                ];
            }
        }
        
        return response()->json([
            'status' => true,
            'message' => '目录创建完成',
            'data' => $results
        ]);
    }

    /**
     * 重新加载地区资源
     */
    public function reloadRegion(Request $request, $code)
    {
        try {
            // 这里可以调用API来重新加载地区资源
            // 暂时返回成功状态
            return response()->json([
                'status' => true,
                'message' => "地区 {$code} 资源重新加载成功"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => "重新加载失败: " . $e->getMessage()
            ]);
        }
    }

    /**
     * 导出地区配置
     */
    public function exportConfig(Request $request)
    {
        $regions = SeoRegion::all();
        $config = [];
        
        foreach ($regions as $region) {
            $config[$region->code] = [
                'name' => $region->name,
                'language' => $region->language,
                'timezone' => $region->timezone,
                'config' => $region->config,
                'data_version' => $region->data_version,
                'fallback_region' => $region->fallback_region,
                'priority' => $region->priority,
                'status' => $region->status
            ];
        }
        
        return response()->json($config)
            ->header('Content-Disposition', 'attachment; filename="regions-config.json"');
    }

    /**
     * 导入地区配置
     */
    public function importConfig(Request $request)
    {
        $request->validate([
            'config_file' => 'required|file|mimes:json'
        ]);
        
        try {
            $content = file_get_contents($request->file('config_file')->path());
            $config = json_decode($content, true);
            
            if (!$config) {
                throw new \Exception('无效的JSON格式');
            }
            
            $imported = 0;
            foreach ($config as $code => $data) {
                SeoRegion::updateOrCreate(
                    ['code' => $code],
                    $data
                );
                $imported++;
            }
            
            return response()->json([
                'status' => true,
                'message' => "成功导入 {$imported} 个地区配置"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => "导入失败: " . $e->getMessage()
            ]);
        }
    }
}
