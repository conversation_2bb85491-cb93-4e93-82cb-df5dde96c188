use crate::entities::RequestInfo;
// use crate::util::compress::{Decompress, Zstd};
use crate::GLOBAL_CONFIG;
use deadpool_redis::{redis::AsyncCommands, Manager, Pool, PoolError};
// use redis::FromRedisValue;
use std::{sync::Arc, time::Duration};
use tokio::time::timeout;

// use super::mongo;

#[derive(Clone)]
pub struct RedisClient {
    pool: Arc<Pool>,
}

impl RedisClient {
    pub async fn new() -> Result<Self, PoolError> {
        let redis_config = &GLOBAL_CONFIG.redis;

        let redis_url = from_url();
        let manager = Manager::new(redis_url)?;
        
        // 优化连接池配置
        let pool = Pool::builder(manager)
            .max_size(redis_config.max_pool)
            .build()
            .expect("Failed to create pool");
            
        // 不再预先创建所有连接，只检查一个连接确保Redis可用
        let mut conn = pool.get().await?;
        let pong: String = redis::cmd("PING")
            .query_async(&mut conn)
            .await
            .map_err(PoolError::from)?;
        println!("Redis ping: {}", pong);

        Ok(RedisClient {
            pool: Arc::new(pool),
        })
    }

    pub async fn get(&self, key: &str) -> Option<String> {
        match timeout(Duration::from_secs(2), self.pool.get()).await {
            Ok(Ok(mut conn)) => conn.get(key).await.ok(),
            _ => None,
        }
    }

    pub async fn get_page(&self, req_info: &RequestInfo) -> (String, bool) {
        let new_expiration = GLOBAL_CONFIG.cache_config.cache_expiration * 60 * 60;
        let script = format!(
            r#"
            local page_cache = redis.call('GET', KEYS[1])
            if page_cache then
                redis.call('EXPIRE', KEYS[1], {})
                if redis.call('EXISTS', KEYS[2]) == 1 then
                    redis.call('DEL', KEYS[2])
                    return {{page_cache, '1'}}
                end
                return {{page_cache, ''}}
            end
            return {{'', ''}}
        "#,
            new_expiration
        );
        let page_cache_key = format!("cache:{}", &req_info.cache_name);
        let unread_cache_key = format!("unread_cache:{}", &req_info.cache_name);

        let keys = vec![page_cache_key, unread_cache_key];
        match self.run_lua_script(&script, &keys, &[]).await {
            Ok(results) => {
                let page_cache = match results.get(0) {
                    Some(b) => {
                        if b.is_empty() {
                            String::new()
                        } else {
                            let content = String::from_utf8(b.to_vec()).unwrap();
                            content
                        }
                    }
                    None => String::new(),
                };
                let write_mongo = match results.get(1) {
                    Some(read) => {
                        if read.is_empty() {
                            // println!("no write_mongo");
                            false
                        } else {
                            // println!("write_mongo");
                            true
                        }
                        // let content = String::from_utf8(read.to_vec()).unwrap();
                        // print!("{}", content);
                        // if content == "1" {
                        //     println!("write_mongo");
                        //     true
                        // } else {
                        //     println!("no write_mongo");
                        //     false
                        // }
                    }
                    None => false,
                };

                (page_cache, write_mongo)
            }
            Err(_) => (String::new(), false),
        }
    }
    pub async fn set_page(&self, req_info: &RequestInfo, value: &Vec<u8>) -> Result<(), PoolError> {
        let key = format!("cache:{}", req_info.cache_name);
        let key2 = format!("unread_cache:{}", req_info.cache_name);
        let seconds = 60 * 60 * GLOBAL_CONFIG.cache_config.first_cache_expiration;
        let mut conn = self.pool.get().await?;
        conn.set_ex::<&str, &[u8], ()>(&key, value, seconds)
            .await
            .map_err(PoolError::from)?;
        conn.set_ex::<&str, i64, ()>(&key2, 1, seconds)
            .await
            .map_err(PoolError::from)?;
        Ok(())
    }

    pub async fn set(&self, key: &str, value: &str, ttl: Option<u64>) -> Result<(), PoolError> {
        let mut conn = self.pool.get().await?;
        if let Some(seconds) = ttl {
            conn.set_ex::<&str, &str, ()>(key, value, seconds)
                .await
                .map_err(PoolError::from)?;
        } else {
            conn.set::<&str, &str, ()>(key, value)
                .await
                .map_err(PoolError::from)?;
        }
        // match ttl {
        //     Some(seconds) => {
        //         conn.set_ex(key, value, seconds)
        //             .await
        //             .map_err(PoolError::from)?;
        //     }
        //     None => {
        //         conn.set(key, value).await.map_err(PoolError::from)?;
        //     }
        // }
        Ok(())
    }

    pub async fn del(&self, key: &str) -> Result<(), PoolError> {
        let mut conn = self.pool.get().await?;
        let _: i64 = conn.del(key).await.map_err(PoolError::from)?;
        Ok(())
    }

    pub async fn del_with_lua_script(&self, pattern: &str) -> Result<(), PoolError> {
        let mut conn = self.pool.get().await?;

        let script = redis::Script::new(
            r#"
                local keys = redis.call('keys', ARGV[1])
                for i=1,#keys do
                    redis.call('del', keys[i])
                end
                return #keys
            "#,
        );

        let _deleted_count: i64 = script
            .arg(pattern)
            .invoke_async(&mut *conn)
            .await
            .map_err(PoolError::from)?;

        Ok(())
    }

    pub async fn run_lua_script(
        &self,
        script: &str,
        keys: &[String],
        args: &[String],
    ) -> Result<Vec<Vec<u8>>, PoolError> {
        let mut conn = self.pool.get().await?;
        let script = redis::Script::new(script);
        let values: Vec<Vec<u8>> = match script.key(keys).arg(args).invoke_async(&mut *conn).await {
            Ok(v) => v,
            Err(err) => {
                eprintln!("Failed to run_lua_script: {:?}", err);
                vec![]
            }
        };
        Ok(values)
    }

    /// 添加统计（使用全局时区）
    ///
    /// 注意：这个方法主要用于向后兼容，建议使用 add_total_with_region
    ///
    /// 参数：
    /// - site: 网站域名
    /// - stat_type: 统计类型 (0=蜘蛛访问, 1=跳转)
    pub async fn add_total(&self, site: &str, stat_type: i8) {
        // 使用配置的时区获取当前时间用于日期格式化
        let now = crate::util::time::now();
        // 格式化日期为YYYYMMDD
        let date = now.format("%Y%m%d").to_string();

        let total_key = format!("total:{}:{}:{}", date, site, stat_type);
        let last_key = format!("lasttime:{}", site);
        // 使用全局时区时间戳（注意：这可能导致时区不一致）
        let now_timestamp = crate::util::time::timestamp();

        // 🔧 修复：延长过期时间到48小时，确保数据不会在同步前丢失
        let expiration = 48 * 60 * 60; // 48小时过期

        // 🔧 修复：添加重试机制和错误处理
        let mut retry_count = 0;
        const MAX_RETRIES: u8 = 3;

        while retry_count < MAX_RETRIES {
            let script = format!(
                r#"
                local total_key = ARGV[1]
                local last_key = ARGV[2]
                local expiration = ARGV[3]
                redis.call('INCR', total_key)
                redis.call('EXPIRE', total_key, expiration)
                redis.call('SET', last_key, {}, 'EX', expiration)
            "#,
                now_timestamp
            );

            let args = vec![total_key.clone(), last_key.clone(), expiration.to_string()];

            match self.run_lua_script(&script, &[], &args).await {
                Ok(_) => {
                    // 成功，退出重试循环
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    if retry_count >= MAX_RETRIES {
                        eprintln!("Redis统计写入失败，已重试{}次: {:?}", MAX_RETRIES, e);
                    } else {
                        // 短暂延迟后重试
                        tokio::time::sleep(tokio::time::Duration::from_millis(100 * retry_count as u64)).await;
                    }
                }
            }
        }
    }



    // ==================== 地区时区支持方法 ====================

    /// 获取页面缓存（支持地区时区）
    pub async fn get_page_with_region(&self, req_info: &RequestInfo, _region_timezone: &str) -> (String, bool) {
        // 🔧 修复：直接使用配置的过期小时数，不需要复杂计算
        let expiration_hours = GLOBAL_CONFIG.cache_config.cache_expiration;
        let new_expiration = expiration_hours * 60 * 60; // 转换为秒

        let script = format!(
            r#"
            local page_cache = redis.call('GET', KEYS[1])
            if page_cache then
                redis.call('EXPIRE', KEYS[1], {})
                if redis.call('EXISTS', KEYS[2]) == 1 then
                    redis.call('DEL', KEYS[2])
                    return {{page_cache, '1'}}
                end
                return {{page_cache, ''}}
            end
            return {{'', ''}}
        "#,
            new_expiration.max(60) // 确保至少60秒过期时间
        );

        let page_cache_key = format!("cache:{}", &req_info.cache_name);
        let unread_cache_key = format!("unread_cache:{}", &req_info.cache_name);

        match self.run_lua_script(&script, &[page_cache_key, unread_cache_key], &[]).await {
            Ok(result) => {
                if result.len() >= 2 {
                    let cache_content = String::from_utf8_lossy(&result[0]).to_string();
                    let is_unread = String::from_utf8_lossy(&result[1]) == "1";
                    return (cache_content, is_unread);
                }
                ("".to_string(), false)
            }
            Err(_) => ("".to_string(), false),
        }
    }

    /// 设置页面缓存（支持地区时区）
    pub async fn set_page_with_region(&self, req_info: &RequestInfo, content: &str, region_timezone: &str) {
        let expiration_hours = GLOBAL_CONFIG.cache_config.cache_expiration;

        // 🔧 使用一致性检查确保Redis和MongoDB过期时间一致
        let (redis_ttl, _mongodb_expiration) = crate::util::time::get_consistent_cache_expiration(
            region_timezone,
            expiration_hours
        );
        let expiration_seconds = redis_ttl;

        let page_cache_key = format!("cache:{}", &req_info.cache_name);
        let unread_cache_key = format!("unread_cache:{}", &req_info.cache_name);

        // 设置页面缓存
        let _ = self.set_with_expiration(&page_cache_key, content, expiration_seconds).await;
        // 设置未读标记
        let _ = self.set_with_expiration(&unread_cache_key, "1", expiration_seconds).await;
    }

    /// 添加统计（支持地区时区）
    ///
    /// 这个方法使用网站所属地区的时区来记录爬行时间
    /// 确保时间戳与网站地区时间一致
    ///
    /// 参数：
    /// - site: 网站域名
    /// - stat_type: 统计类型 (0=蜘蛛访问, 1=跳转)
    /// - region_timezone: 地区时区
    pub async fn add_total_with_region(&self, site: &str, stat_type: i8, region_timezone: &str) {
        // 使用地区时区获取当前时间
        let region_now = crate::util::time::now_for_region(region_timezone);
        let date = region_now.format("%Y%m%d").to_string();

        // 修复：使用stat_type作为键的类型标识符
        let total_key = format!("total:{}:{}:{}", date, site, stat_type);
        let last_key = format!("lasttime:{}", site);

        // 🎯 关键：使用地区时区的时间戳，但存储为UTC时间戳便于跨时区比较
        // 这样既保持了地区时间的语义，又便于系统处理
        let region_timestamp = crate::util::time::timestamp_for_region(region_timezone);

        // 🔧 修复：延长过期时间到48小时，确保数据不会在同步前丢失
        let expiration = 48 * 60 * 60; // 48小时过期，给admin项目足够时间同步

        // 🔧 修复：添加重试机制和错误处理
        let mut retry_count = 0;
        const MAX_RETRIES: u8 = 3;

        while retry_count < MAX_RETRIES {
            let script = format!(
                r#"
                local total_key = ARGV[1]
                local last_key = ARGV[2]
                local expiration = ARGV[3]
                redis.call('INCR', total_key)
                redis.call('EXPIRE', total_key, expiration)
                redis.call('SET', last_key, {}, 'EX', expiration)
            "#,
                region_timestamp
            );

            let args = vec![total_key.clone(), last_key.clone(), expiration.to_string()];

            match self.run_lua_script(&script, &[], &args).await {
                Ok(_) => {
                    // 成功，退出重试循环
                    break;
                }
                Err(e) => {
                    retry_count += 1;
                    if retry_count >= MAX_RETRIES {
                        eprintln!("Redis统计写入失败，已重试{}次: {:?}", MAX_RETRIES, e);
                    } else {
                        // 短暂延迟后重试
                        tokio::time::sleep(tokio::time::Duration::from_millis(100 * retry_count as u64)).await;
                    }
                }
            }
        }
    }

    /// 检查缓存是否在地区时区下过期
    pub async fn is_cache_expired_in_region(&self, cache_key: &str, region_timezone: &str) -> bool {
        match self.get_ttl(cache_key).await {
            Some(ttl) if ttl > 0 => {
                // 缓存还有TTL，检查是否在地区时区下已过期
                let current_timestamp = crate::util::time::timestamp_for_region(region_timezone);
                let cache_expiry = current_timestamp + ttl;
                let region_day_end = crate::util::time::get_region_day_end_timestamp(region_timezone);
                cache_expiry < region_day_end
            }
            _ => true, // 没有TTL或已过期
        }
    }

    /// 获取缓存TTL
    async fn get_ttl(&self, key: &str) -> Option<i64> {
        match timeout(Duration::from_secs(2), self.pool.get()).await {
            Ok(Ok(mut conn)) => {
                match redis::cmd("TTL").arg(key).query_async::<i64>(&mut conn).await {
                    Ok(ttl) => Some(ttl),
                    Err(_) => None,
                }
            }
            _ => None,
        }
    }

    /// 设置带过期时间的键值
    async fn set_with_expiration(&self, key: &str, value: &str, expiration_seconds: u64) -> bool {
        match timeout(Duration::from_secs(2), self.pool.get()).await {
            Ok(Ok(mut conn)) => {
                match redis::cmd("SETEX")
                    .arg(key)
                    .arg(expiration_seconds)
                    .arg(value)
                    .query_async::<String>(&mut conn)
                    .await
                {
                    Ok(_) => true,
                    Err(_) => false,
                }
            }
            _ => false,
        }
    }
}
pub fn from_url() -> String {
    let redis_config = &GLOBAL_CONFIG.redis;
    if redis_config.password.is_empty() || redis_config.username.is_empty() {
        format!(
            "redis://{}:{}/{}",
            redis_config.host, redis_config.port, redis_config.db
        )
    } else {
        format!(
            "redis://{}:{}@{}:{}/{}",
            redis_config.username,
            redis_config.password,
            redis_config.host,
            redis_config.port,
            redis_config.db
        )
    }
}
