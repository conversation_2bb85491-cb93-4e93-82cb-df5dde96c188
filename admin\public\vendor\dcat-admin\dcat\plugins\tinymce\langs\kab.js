tinymce.addI18n('kab',{
"Redo": "Err-d",
"Undo": "Semmet",
"Cut": "<PERSON><PERSON><PERSON>",
"Copy": "N\u0263el",
"Paste": "Sente\u1e0d",
"Select all": "Fren kulec",
"New document": "Attaftar amaynut",
"Ok": "Ih",
"Cancel": "Semmet",
"Visual aids": "Visual aids",
"Bold": "Tira tazurant",
"Italic": "Tira yeknan",
"Underline": "Aderrer",
"Strikethrough": "Strikethrough",
"Superscript": "Superscript",
"Subscript": "Subscript",
"Clear formatting": "Clear formatting",
"Align left": "Tarigla \u0263er zelma\u1e0d",
"Align center": "Di tlemast",
"Align right": "tarigla \u0263er zelma\u1e0d",
"Justify": "Justify",
"Bullet list": "Tabdart s tlillac",
"Numbered list": "Tabdart s wu\u1e6d\u1e6dunen",
"Decrease indent": "Simc\u1e6du\u1e25 asi\u1e93i",
"Increase indent": "Sim\u0263ur asi\u1e93i",
"Close": "Mdel",
"Formats": "Imasalen",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.",
"Headers": "Izwal",
"Header 1": "Azwel 1",
"Header 2": "Azwel 2",
"Header 3": "Azwel 3",
"Header 4": "Azwel 4",
"Header 5": "Header 5",
"Header 6": "Azwel 6",
"Headings": "Izewlen",
"Heading 1": "Inixf 1",
"Heading 2": "Inixf 2",
"Heading 3": "Inixf 3",
"Heading 4": "Inixf 4",
"Heading 5": "Inixf 5",
"Heading 6": "Inixf 6",
"Preformatted": "Yettwamsel si tazwara",
"Div": "Div",
"Pre": "Pre",
"Code": "Tangalt",
"Paragraph": "taseddart",
"Blockquote": "Tanebdurt",
"Inline": "Inline",
"Blocks": "I\u1e25edran",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.",
"Fonts": "Tisefsa",
"Font Sizes": "Tiddi n tsefsit",
"Class": "Asmil",
"Browse for an image": "Snirem iwakken ad tferne\u1e0d tugna",
"OR": "Ih",
"Drop an image here": "Ssers tugna dagi",
"Upload": "Sili",
"Block": "Sew\u1e25el",
"Align": "Settef",
"Default": "Lex\u1e63as",
"Circle": "Tawinest",
"Disc": "A\u1e0debsi",
"Square": "Amku\u1e93",
"Lower Alpha": "Alpha ame\u1e93yan",
"Lower Greek": "Grik ame\u1e93yan",
"Lower Roman": "Ruman amectu\u1e25",
"Upper Alpha": "Alfa ameqran",
"Upper Roman": "Ruman ameqran",
"Anchor...": "Tamdeyt...",
"Name": "Isem",
"Id": "Id",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "id ilaq ad ibdu s usekkil, ad yettwa\u1e0dfer kan s isekkilen, im\u1e0danen, ijerri\u1e0den, tinqi\u1e0din, snat n tenqi\u1e0din ne\u0263 ijerri\u1e0den n wadda.",
"You have unsaved changes are you sure you want to navigate away?": "Ibeddilen ur twaskelsen ara teb\u0263i\u1e0d ad teff\u0263e\u1e0d ?",
"Restore last draft": "Restore last draft",
"Special character...": "Isekkilen uzzigen...",
"Source code": "Tangalt ta\u0263balut",
"Insert\/Edit code sample": "Ger\/\u1e92reg tangalt n umedya",
"Language": "Tutlayt",
"Code sample...": "Amedya n tengalt...",
"Color Picker": "Amelqa\u1e0d n yini",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Seg zelma\u1e0d \u0263er yefus",
"Right to left": "Seg yefus \u0263er zelma\u1e0d",
"Emoticons...": "Tigitin n u\u1e25ulfu...",
"Metadata and Document Properties": "Idfersefka akked yiraten n yisemli",
"Title": "Azwel",
"Keywords": "Awalen yufraren",
"Description": "Aglam",
"Robots": "Robots",
"Author": "Ameskar",
"Encoding": "Asettengel",
"Fullscreen": "Agdil a\u010duran",
"Action": "Tigawt",
"Shortcut": "Anegzum",
"Help": "Tallalt",
"Address": "Tansa",
"Focus to menubar": "Asa\u1e0des \u0263ef tfeggagt n wumu\u0263",
"Focus to toolbar": "Asa\u1e0des \u0263ef tfeggagt n ifecka",
"Focus to element path": "Asa\u1e0des \u0263ef ubrid n uferdis",
"Focus to contextual toolbar": "Asa\u1e0des \u0263ef tfeggagt n ifecka tanattalt",
"Insert link (if link plugin activated)": "Ger ase\u0263wen (ma yermed uzegrir n use\u0263wen)",
"Save (if save plugin activated)": "Sekles (ma yermed uzegrir save)",
"Find (if searchreplace plugin activated)": "Nadi (ma yermed uzegrir searchreplace)",
"Plugins installed ({0}):": "Izegriren yettwasbedden ({0}):",
"Premium plugins:": "Izegriren premium :",
"Learn more...": "\u1e92er ugar...",
"You are using {0}": "Tsseqdace\u1e0d {0}",
"Plugins": "Isi\u0263zifen",
"Handy Shortcuts": "Inegzumen",
"Horizontal line": "Ajerri\u1e0d aglawan",
"Insert\/edit image": "Ger\/\u1e92reg tugna",
"Image description": "Aglam n tugna",
"Source": "A\u0263balu",
"Dimensions": "Tisekta",
"Constrain proportions": "Constrain proportions",
"General": "Amatu",
"Advanced": "Ana\u1e93i",
"Style": "A\u0263anib",
"Vertical space": "Talunt taratakt",
"Horizontal space": "Talunt taglawant",
"Border": "Iri",
"Insert image": "Ger tugna",
"Image...": "Tugna...",
"Image list": "Tabdart n tugniwin",
"Rotate counterclockwise": "Tuzya mgal tamrilt",
"Rotate clockwise": "Tuzya yugdan tamrilt",
"Flip vertically": "Tuzya taratakt",
"Flip horizontally": "Tuzttya tagrawant",
"Edit image": "\u1e92reg tugna",
"Image options": "Tixti\u1e5biyin n tugna",
"Zoom in": "Zoom in",
"Zoom out": "Zoom out",
"Crop": "Rogner",
"Resize": "Beddel tiddi",
"Orientation": "Ta\u0263da",
"Brightness": "Tafat",
"Sharpen": "Affiner",
"Contrast": "Contrast",
"Color levels": "Iswiren n yini",
"Gamma": "Gamma",
"Invert": "Tti",
"Apply": "Snes",
"Back": "Tu\u0263alin",
"Insert date\/time": "Ger azemz\/asrag",
"Date\/time": "Azemz\/Asrag",
"Insert\/Edit Link": "Ger\/\u1e92reg as\u0263en",
"Insert\/edit link": "Ger\/\u1e93reg azday",
"Text to display": "A\u1e0dris ara yettwabeqq\u1e0den",
"Url": "Url",
"Open link in...": "Ldi as\u0263en di...",
"Current window": "Asfaylu amiran",
"None": "Ulac",
"New window": "Asfaylu amaynut",
"Remove link": "Kkes azday",
"Anchors": "Timdyin",
"Link...": "As\u0263en...",
"Paste or type a link": "Sente\u1e0d ne\u0263 sekcem ase\u0263wen",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "URL i teskecme\u1e0d tettban-d d tansa email. teb\u0263i\u1e0d ad s-ternu\u1e0d azwir mailto : ?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "URL i teskecme\u1e0d tettban-d d azday uffi\u0263. Teb\u0263i\u1e0d ad s-ternu\u1e0d azwir http:\/\/  ?",
"Link list": "Tabdart n is\u0263ewnen",
"Insert video": "Ger avidyu",
"Insert\/edit video": "Ger\/\u1e93reg avidyu",
"Insert\/edit media": "Ger\/\u1e92reg amiya",
"Alternative source": "A\u0263balu amlellay",
"Alternative source URL": "A\u0263balu n URL amlellay",
"Media poster (Image URL)": "Abeqqi\u1e0d n umidya (URL n tugna)",
"Paste your embed code below:": "Paste your embed code below:",
"Embed": "Embed",
"Media...": "Amidya...",
"Nonbreaking space": "Talunt ur nettwagzam ara",
"Page break": "Angaz n usebter",
"Paste as text": "Sente\u1e0d d a\u1e0dris",
"Preview": "Sken",
"Print...": "Siggez...",
"Save": "Sekles",
"Find": "Nadi",
"Replace with": "Semselsi s",
"Replace": "Semselsi",
"Replace all": "Semselsi kulec",
"Previous": "Uzwir",
"Next": "Win \u0263ers",
"Find and replace...": "Nadi semselsi...",
"Could not find the specified string.": "Ur d-nufi ara azrar i d-yettunefken.",
"Match case": "Match case",
"Find whole words only": "Af-d awal ummid kan",
"Spell check": "Selken ta\u0263dirawt",
"Ignore": "Zgel",
"Ignore all": "Zgel kulec",
"Finish": "Fak",
"Add to Dictionary": "Rnu-t s amawal",
"Insert table": "Ger tafelwit",
"Table properties": "Iraten n tfelwit",
"Delete table": "Kkes tafelwit",
"Cell": "Taxxamt",
"Row": "Adur",
"Column": "Tagejdit",
"Cell properties": "Iraten n texxamt",
"Merge cells": "Seddukel tixxamin",
"Split cell": "B\u1e0du tixxamin",
"Insert row before": "Ger adur deffir",
"Insert row after": "Ger adur sdat",
"Delete row": "Kkes tagejdit",
"Row properties": "Iraten n udur",
"Cut row": "Gzem adur",
"Copy row": "N\u0263el adur",
"Paste row before": "Sente\u1e0d adur sdat",
"Paste row after": "Sente\u1e0d adur deffir",
"Insert column before": "Sente\u1e0d tagejdit sdat",
"Insert column after": "Sente\u1e0d tagejdit deffir",
"Delete column": "Kkes tagejdit",
"Cols": "Tigejda",
"Rows": "Aduren",
"Width": "Tehri",
"Height": "Te\u0263zi",
"Cell spacing": "Tlunt ger texxamin",
"Cell padding": "Tama n texxamt",
"Show caption": "Sken taw\u1e6d\u1e6dfa",
"Left": "\u0194er zelma\u1e0d",
"Center": "Di tlemmast",
"Right": "\u0194er yefus",
"Cell type": "Anaw n texxamt",
"Scope": "Scope",
"Alignment": "Tarigla",
"H Align": "Tarigla taglawant",
"V Align": "Tarigla taratakt",
"Top": "Uksawen",
"Middle": "Di tlemmast",
"Bottom": "Uksar",
"Header cell": "Tasen\u1e6di\u1e0dt n texxamt",
"Row group": "Agraw n waduren",
"Column group": "Agraw n tgejda",
"Row type": "Anaw n wadur",
"Header": "Tasenti\u1e0dt",
"Body": "Tafka",
"Footer": "A\u1e0dar",
"Border color": "Ini n yiri",
"Insert template...": "Ger tane\u0263ruft...",
"Templates": "Timudimin",
"Template": "Tine\u0263rufin",
"Text color": "Ini n u\u1e0dris",
"Background color": "Ini n ugilal",
"Custom...": "Custom...",
"Custom color": "Custom color",
"No color": "Ulac ini",
"Remove color": "Kkes ini",
"Table of Contents": "Tafelwit n ugbur",
"Show blocks": "Beqqe\u1e0d i\u1e25edran",
"Show invisible characters": "Beqqe\u1e0d isekkilen uffiren",
"Word count": "Am\u1e0dan n wawalen",
"Count": "A\u0263rud",
"Document": "Isemli",
"Selection": "Tafrayt",
"Words": "Awalen",
"Words: {0}": "Words: {0}",
"{0} words": "{0} n wawalen",
"File": "Afaylu",
"Edit": "\u1e92reg",
"Insert": "Ger",
"View": "Tamu\u0263li",
"Format": "Amasal",
"Table": "Tafelwit",
"Tools": "Ifecka",
"Powered by {0}": "Iteddu s {0} ",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help",
"Image title": "Azwel n tugna",
"Border width": "Tehri n yiri",
"Border style": "A\u0263anib n yiri",
"Error": "Tucc\u1e0da",
"Warn": "\u0190eyyen",
"Valid": "Ame\u0263tu",
"To open the popup, press Shift+Enter": "Iwakken ad teldi\u1e0d asfaylu udhim, ssed Shift+Kcem",
"Rich Text Area. Press ALT-0 for help.": "Ta\u0263zut n u\u1e0dris anesba\u0263ur. Ssed ALT-0 i tallelt.",
"System Font": "Anagraw n tsefsa",
"Failed to upload image: {0}": "Tucc\u1e0da deg usili n tugna: {0}",
"Failed to load plugin: {0} from url {1}": "Tucc\u1e0da deg usili n usi\u0263zef: {0} seg url {1}",
"Failed to load plugin url: {0}": "Tucc\u1e0da deg usali n usi\u0263zef: {0}",
"Failed to initialize plugin: {0}": "Tucc\u1e0da deg wallus n uwennez n usi\u0263zef: {0}",
"example": "amedya",
"Search": "Nadi",
"All": "Akk",
"Currency": "Adrim",
"Text": "A\u1e0dris",
"Quotations": "Tinebdurin",
"Mathematical": "Inemhalen usnaken",
"Extended Latin": "Talatinit ye\u1e93len",
"Symbols": "Izamulen",
"Arrows": "Tineccabin",
"User Defined": "Yesbadu-t useqdac",
"dollar sign": "Azamul n dular",
"currency sign": "Azamul n wedrim",
"euro-currency sign": "azamul n euro",
"colon sign": "azamul n kulun",
"cruzeiro sign": "azamul n krutayru",
"french franc sign": "azamul n f\u1e5bank afransi",
"lira sign": "azamul n lira",
"mill sign": "azamul n mil",
"naira sign": "azamul n nayra",
"peseta sign": "azamul n pizi\u1e6da",
"rupee sign": "azamul n urupi",
"won sign": "azamul n wun",
"new sheqel sign": "azamul n ciqel amaynut",
"dong sign": "azamul n dung",
"kip sign": "azamul n kip",
"tugrik sign": "azamul n tugrik",
"drachma sign": "azamul n d\u1e5bacma",
"german penny symbol": "azamul n pini almani",
"peso sign": "azamul n pizu",
"guarani sign": "azamul n gwa\u1e5bani",
"austral sign": "azamul n ustral",
"hryvnia sign": "azamul n hrivniya",
"cedi sign": "azamul n siddi",
"livre tournois sign": "azamul lira aturnwa",
"spesmilo sign": "azamul n spismilu",
"tenge sign": "azamul n tingi",
"indian rupee sign": "azamul n urupi ahindi",
"turkish lira sign": "azamul n lira a\u1e6durki",
"nordic mark sign": "azamul n ma\u1e5bk n ugafa",
"manat sign": "azamul n mana\u1e6d",
"ruble sign": "azamul n rubl",
"yen character": "azamul n yan",
"yuan character": "azamul n yuwan",
"yuan character, in hong kong and taiwan": "asekkil n yuwan, di hunkung akked \u1e6daywan",
"yen\/yuan character variant one": "yan\/yuwan tasenfelt n usekkil yiwen",
"Loading emoticons...": "Asali n tignitin n u\u1e25ulfu...",
"Could not load emoticons": "Tucc\u1e0da deg usali n tignitin n u\u1e25ulfu",
"People": "L\u0263aci",
"Animals and Nature": "i\u0263ersiwen akked ugama",
"Food and Drink": "Tu\u010d\u010dit akked tisit",
"Activity": "Armud",
"Travel and Places": "Asikel akked ime\u1e0dqan",
"Objects": "Ti\u0263awsiwin",
"Flags": "Annayen",
"Characters": "Isekkilen",
"Characters (no spaces)": "Isekkilen (tallunin ur ttekkint ara)",
"{0} characters": "{0} n yisekkilen",
"Error: Form submit field collision.": "Tucc\u1e0da: amgirred n wurtan di tuzzna n tferkit.",
"Error: No form element found.": "Ulac aferdis n tferkit i yettwafen.",
"Update": "Leqqem",
"Color swatch": "Talemmict n yini",
"Turquoise": "Ajenjari",
"Green": "Azegzaw",
"Blue": "Anili",
"Purple": "Amidadi",
"Navy Blue": "Anili n yilel",
"Dark Turquoise": "Ajenjari a\u0263mayan",
"Dark Green": "Azegzaw a\u0263mayan",
"Medium Blue": "Anili alemmas",
"Medium Purple": "Amidadi alemmas",
"Midnight Blue": "Anili n yi\u1e0d",
"Yellow": "Awra\u0263",
"Orange": "A\u010d\u010dinawi",
"Red": "Azegga\u0263",
"Light Gray": "Amelli\u0263di afaw",
"Gray": "Amelli\u0263di",
"Dark Yellow": "Awra\u0263 a\u0263mayan",
"Dark Orange": "A\u010dinawi a\u0263mayan",
"Dark Red": "Azegga\u0263 a\u0263mayan",
"Medium Gray": "Amelli\u0263di alemmas",
"Dark Gray": "Amelli\u0263di a\u0263mayan",
"Light Green": "Azegzaw afaw",
"Light Yellow": "Awra\u0263 afaw",
"Light Red": "Azegga\u0263 afaw",
"Light Purple": "Amidadi afaw",
"Light Blue": "Anili afaw",
"Dark Purple": "Amidadi a\u0263mayan",
"Dark Blue": "Anili a\u0263mayan",
"Black": "Aberkan",
"White": "Amellal",
"Switch to or from fullscreen mode": "Kcem ne\u0263 ffe\u0263 agdil a\u010d\u010duran",
"Open help dialog": "Ldi tankult n udiwenni n tallelt",
"history": "Amazray",
"styles": "i\u0263unab",
"formatting": "amsal",
"alignment": "aderrec",
"indentation": "asi\u1e93i",
"permanent pen": "imru ama\u0263lal",
"comments": "iwenniten",
"Format Painter": "Sisleg amsal",
"Insert\/edit iframe": "Ger\/\u1e92reg akatar",
"Capitalization": "Selket s asekkil ameqran",
"lowercase": "asekkil amec\u1e6du\u1e25",
"UPPERCASE": "ASEKKIL AMEQRAN",
"Title Case": "Taj\u1e5but n uzwel",
"Permanent Pen Properties": "Iraten n yimru ama\u0263lal",
"Permanent pen properties...": "Iraten n yimru ama\u0263lal...",
"Font": "Tasefsit",
"Size": "Tiddi",
"More...": "Ugar...",
"Spellcheck Language": "Tutlayt n umse\u0263ti n tira",
"Select...": "Fren...",
"Preferences": "Imenyafen",
"Yes": "Ih",
"No": "Ala",
"Keyboard Navigation": "Tunigin s unasiw",
"Version": "Lqem",
"Anchor": "Tamdeyt",
"Special character": "Askil uslig",
"Code sample": "Tikkest n tengalt",
"Color": "Ini",
"Emoticons": "Emoticons",
"Document properties": "Iraten n warat",
"Image": "Tugna",
"Insert link": "Ger azday",
"Target": "Target",
"Link": "Ase\u0263wen",
"Poster": "Poster",
"Media": "Amidya",
"Print": "Siggez",
"Prev": "Win yezrin",
"Find and replace": "Nadi semselsi",
"Whole words": "Awal ummid",
"Spellcheck": "Ase\u0263ti n tira",
"Caption": "Caption",
"Insert template": "Ger tamuddimt"
});