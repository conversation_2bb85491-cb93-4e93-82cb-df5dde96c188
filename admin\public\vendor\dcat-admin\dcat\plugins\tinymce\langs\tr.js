tinymce.addI18n('tr',{
"Redo": "<PERSON><PERSON>",
"Undo": "<PERSON><PERSON> al",
"Cut": "<PERSON><PERSON>",
"Copy": "<PERSON><PERSON><PERSON>",
"Paste": "Yap\u0131\u015ft\u0131r",
"Select all": "T\u00fcm\u00fcn\u00fc se\u00e7",
"New document": "Yeni dok\u00fcman",
"Ok": "Tamam",
"Cancel": "\u0130ptal",
"Visual aids": "G\u00f6rsel ara\u00e7lar",
"Bold": "Kal\u0131n",
"Italic": "\u0130talik",
"Underline": "Alt\u0131 \u00e7izili",
"Strikethrough": "\u00dcst\u00fc \u00e7izgili",
"Superscript": "\u00dcst simge",
"Subscript": "Alt simge",
"Clear formatting": "Bi\u00e7imi temizle",
"Align left": "<PERSON>a hizala",
"Align center": "<PERSON><PERSON><PERSON>",
"Align right": "Sa\u011fa hizala",
"Justify": "\u0130ki yana yasla",
"Bullet list": "S\u0131ras\u0131z liste",
"Numbered list": "S\u0131ral\u0131 liste",
"Decrease indent": "Girintiyi azalt",
"Increase indent": "Girintiyi art\u0131r",
"Close": "Kapat",
"Formats": "Bi\u00e7imler",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Taray\u0131c\u0131n\u0131z panoya direk eri\u015fimi desteklemiyor. L\u00fctfen Ctrl+X\/C\/V klavye k\u0131sayollar\u0131n\u0131 kullan\u0131n.",
"Headers": "Ba\u015fl\u0131klar",
"Header 1": "Ba\u015fl\u0131k 1",
"Header 2": "Ba\u015fl\u0131k 2",
"Header 3": "Ba\u015fl\u0131k 3",
"Header 4": "Ba\u015fl\u0131k 4",
"Header 5": "Ba\u015fl\u0131k 5",
"Header 6": "Ba\u015fl\u0131k 6",
"Headings": "Ba\u015fl\u0131klar",
"Heading 1": "Ba\u015fl\u0131k 1",
"Heading 2": "Ba\u015fl\u0131k 2",
"Heading 3": "Ba\u015fl\u0131k 3",
"Heading 4": "Ba\u015fl\u0131k 4",
"Heading 5": "Ba\u015fl\u0131k 5",
"Heading 6": "Ba\u015fl\u0131k 6",
"Preformatted": "\u00d6nceden bi\u00e7imlendirilmi\u015f",
"Div": "Div",
"Pre": "Pre",
"Code": "Kod",
"Paragraph": "Paragraf",
"Blockquote": "Blockquote",
"Inline": "Sat\u0131r i\u00e7i",
"Blocks": "Bloklar",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "D\u00fcz metin modunda yap\u0131\u015ft\u0131r. Bu se\u00e7ene\u011fi kapatana kadar i\u00e7erikler d\u00fcz metin olarak yap\u0131\u015ft\u0131r\u0131l\u0131r.",
"Fonts": "Yaz\u0131 Tipleri",
"Font Sizes": "Yaz\u0131tipi B\u00fcy\u00fckl\u00fc\u011f\u00fc",
"Class": "S\u0131n\u0131f",
"Browse for an image": "Bir resim aray\u0131n",
"OR": "VEYA",
"Drop an image here": "Buraya bir resim koyun",
"Upload": "Y\u00fckle",
"Block": "Blok",
"Align": "Hizala",
"Default": "Varsay\u0131lan",
"Circle": "Daire",
"Disc": "Disk",
"Square": "Kare",
"Lower Alpha": "K\u00fc\u00e7\u00fck Harf",
"Lower Greek": "K\u00fc\u00e7\u00fck Yunan Harfleri",
"Lower Roman": "K\u00fc\u00e7\u00fck Roman Harfleri ",
"Upper Alpha": "B\u00fcy\u00fck Harf",
"Upper Roman": "B\u00fcy\u00fck Roman Harfleri ",
"Anchor...": "\u00c7apa...",
"Name": "\u0130sim",
"Id": "Kimlik",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id bir harf ile ba\u015flamal\u0131d\u0131r ve harf, rakam, \u00e7izgi, nokta, iki nokta \u00fcst\u00fcste veya alt \u00e7izgi kullan\u0131labilir.",
"You have unsaved changes are you sure you want to navigate away?": "Kaydedilmemi\u015f de\u011fi\u015fiklikler var, sayfadan ayr\u0131lmak istedi\u011finize emin misiniz?",
"Restore last draft": "Son tasla\u011f\u0131 geri y\u00fckle",
"Special character...": "\u00d6zel karakter...",
"Source code": "Kaynak kodu",
"Insert\/Edit code sample": "\u00d6rnek kod ekle\/d\u00fczenle",
"Language": "Dil",
"Code sample...": "Kod \u00f6rne\u011fi...",
"Color Picker": "Renk Se\u00e7ici",
"R": "R",
"G": "G",
"B": "B",
"Left to right": "Soldan sa\u011fa",
"Right to left": "Sa\u011fdan sola",
"Emoticons...": "\u0130fadeler...",
"Metadata and Document Properties": "\u00d6nbilgi ve Belge \u00d6zellikleri",
"Title": "Ba\u015fl\u0131k",
"Keywords": "Anahtar kelimeler",
"Description": "A\u00e7\u0131klama",
"Robots": "Robotlar",
"Author": "Yazar",
"Encoding": "Kodlama",
"Fullscreen": "Tam ekran",
"Action": "Eylem",
"Shortcut": "K\u0131sayol",
"Help": "Yard\u0131m",
"Address": "Adres",
"Focus to menubar": "Men\u00fcye odaklan",
"Focus to toolbar": "Ara\u00e7 tak\u0131m\u0131na odaklan",
"Focus to element path": "\u00d6\u011fe yoluna odaklan",
"Focus to contextual toolbar": "Ba\u011flamsal ara\u00e7 tak\u0131m\u0131na odaklan",
"Insert link (if link plugin activated)": "Ba\u011flant\u0131 ekle (Ba\u011flant\u0131 eklentisi aktif ise)",
"Save (if save plugin activated)": "Kaydet (Kay\u0131t eklentisi aktif ise)",
"Find (if searchreplace plugin activated)": "Bul (Bul\/De\u011fi\u015ftir eklentisi aktif ise)",
"Plugins installed ({0}):": "Eklentiler y\u00fcklendi ({0}):",
"Premium plugins:": "Premium eklentiler:",
"Learn more...": "Detayl\u0131 bilgi...",
"You are using {0}": "\u015eu an {0} kullan\u0131yorsunuz",
"Plugins": "Plugins",
"Handy Shortcuts": "Handy Shortcuts",
"Horizontal line": "Yatay \u00e7izgi",
"Insert\/edit image": "Resim ekle\/d\u00fczenle",
"Image description": "Resim a\u00e7\u0131klamas\u0131",
"Source": "Kaynak",
"Dimensions": "Boyutlar",
"Constrain proportions": "Oranlar\u0131 koru",
"General": "Genel",
"Advanced": "Geli\u015fmi\u015f",
"Style": "Stil",
"Vertical space": "Dikey bo\u015fluk",
"Horizontal space": "Yatay bo\u015fluk",
"Border": "Kenarl\u0131k",
"Insert image": "Resim ekle",
"Image...": "Resim...",
"Image list": "G\u00f6rsel listesi",
"Rotate counterclockwise": "Saatin tersi y\u00f6n\u00fcnde d\u00f6nd\u00fcr",
"Rotate clockwise": "Saat y\u00f6n\u00fcnde d\u00f6nd\u00fcr",
"Flip vertically": "Dikine \u00e7evir",
"Flip horizontally": "Enine \u00e7evir",
"Edit image": "Resmi d\u00fczenle",
"Image options": "Resim ayarlar\u0131",
"Zoom in": "Yak\u0131nla\u015ft\u0131r",
"Zoom out": "Uzakla\u015ft\u0131r",
"Crop": "K\u0131rp",
"Resize": "Yeniden Boyutland\u0131r",
"Orientation": "Oryantasyon",
"Brightness": "Parlakl\u0131k",
"Sharpen": "Keskinle\u015ftir",
"Contrast": "Kontrast",
"Color levels": "Renk d\u00fczeyleri",
"Gamma": "Gama",
"Invert": "Ters \u00c7evir",
"Apply": "Uygula",
"Back": "Geri",
"Insert date\/time": "Tarih\/saat ekle",
"Date\/time": "Tarih\/saat",
"Insert\/Edit Link": "Ba\u011flant\u0131 Ekle\/D\u00fczenle",
"Insert\/edit link": "Ba\u011flant\u0131 ekle\/d\u00fczenle",
"Text to display": "Yaz\u0131y\u0131 g\u00f6r\u00fcnt\u00fcle",
"Url": "Url",
"Open link in...": "Ba\u011flant\u0131y\u0131 a\u00e7...",
"Current window": "Mevcut pencere",
"None": "Hi\u00e7biri",
"New window": "Yeni pencere",
"Remove link": "Ba\u011flant\u0131y\u0131 kald\u0131r",
"Anchors": "\u00c7apalar",
"Link...": "Ba\u011flant\u0131...",
"Paste or type a link": "Bir ba\u011flant\u0131 yaz\u0131n yada yap\u0131\u015ft\u0131r\u0131n",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "Girdi\u011finiz URL bir e-posta adresi gibi g\u00f6r\u00fcn\u00fcyor. Gerekli olan mailto: \u00f6nekini eklemek ister misiniz?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "Girdi\u011finiz URL bir d\u0131\u015f ba\u011flant\u0131 gibi g\u00f6r\u00fcn\u00fcyor. Gerekli olan http:\/\/ \u00f6nekini eklemek ister misiniz?",
"Link list": "Ba\u011flant\u0131 listesi",
"Insert video": "Video ekle",
"Insert\/edit video": "Video ekle\/d\u00fczenle",
"Insert\/edit media": "Medya ekle\/d\u00fczenle",
"Alternative source": "Alternatif kaynak",
"Alternative source URL": "Alternatif kaynak URL",
"Media poster (Image URL)": "Medya posteri (Resim URL)",
"Paste your embed code below:": "Video g\u00f6mme kodunu a\u015fa\u011f\u0131ya yap\u0131\u015ft\u0131r\u0131n\u0131z:",
"Embed": "G\u00f6mme",
"Media...": "Medya...",
"Nonbreaking space": "B\u00f6l\u00fcnemez bo\u015fluk",
"Page break": "Sayfa sonu",
"Paste as text": "Metin olarak yap\u0131\u015ft\u0131r",
"Preview": "\u00d6nizleme",
"Print...": "Yazd\u0131r...",
"Save": "Kaydet",
"Find": "Bul",
"Replace with": "Bununla de\u011fi\u015ftir",
"Replace": "De\u011fi\u015ftir",
"Replace all": "T\u00fcm\u00fcn\u00fc de\u011fi\u015ftir",
"Previous": "Geri",
"Next": "Sonraki",
"Find and replace...": "Bul ve de\u011fi\u015ftir...",
"Could not find the specified string.": "Herhangi bir sonu\u00e7 bulunamad\u0131.",
"Match case": "B\u00fcy\u00fck\/k\u00fc\u00e7\u00fck harf duyarl\u0131",
"Find whole words only": "Sadece t\u00fcm kelimeyi ara",
"Spell check": "Yaz\u0131m denetimi",
"Ignore": "Yoksay",
"Ignore all": "T\u00fcm\u00fcn\u00fc yoksay",
"Finish": "Bitir",
"Add to Dictionary": "S\u00f6zl\u00fc\u011fe Ekle",
"Insert table": "Tablo ekle",
"Table properties": "Tablo \u00f6zellikleri",
"Delete table": "Tablo sil",
"Cell": "H\u00fccre",
"Row": "Sat\u0131r",
"Column": "S\u00fctun",
"Cell properties": "H\u00fccre \u00f6zellikleri",
"Merge cells": "H\u00fccreleri birle\u015ftir",
"Split cell": "H\u00fccre b\u00f6l",
"Insert row before": "\u00dcste sat\u0131r ekle",
"Insert row after": "Alta sat\u0131r ekle ",
"Delete row": "Sat\u0131r sil",
"Row properties": "Sat\u0131r \u00f6zellikleri",
"Cut row": "Sat\u0131r\u0131 kes",
"Copy row": "Sat\u0131r\u0131 kopyala",
"Paste row before": "\u00dcste sat\u0131r yap\u0131\u015ft\u0131r",
"Paste row after": "Alta sat\u0131r yap\u0131\u015ft\u0131r",
"Insert column before": "Sola s\u00fctun ekle",
"Insert column after": "Sa\u011fa s\u00fctun ekle",
"Delete column": "S\u00fctun sil",
"Cols": "S\u00fctunlar",
"Rows": "Sat\u0131rlar",
"Width": "Geni\u015flik",
"Height": "Y\u00fckseklik",
"Cell spacing": "H\u00fccre aral\u0131\u011f\u0131",
"Cell padding": "H\u00fccre dolgusu",
"Show caption": "Ba\u015fl\u0131\u011f\u0131 g\u00f6ster",
"Left": "Sol",
"Center": "Orta",
"Right": "Sa\u011f",
"Cell type": "H\u00fccre tipi",
"Scope": "Kapsam",
"Alignment": "Hizalama",
"H Align": "Yatay Hizalama",
"V Align": "Dikey Hizalama",
"Top": "\u00dcst",
"Middle": "Orta",
"Bottom": "Alt",
"Header cell": "Ba\u015fl\u0131k h\u00fccresi",
"Row group": "Sat\u0131r grubu",
"Column group": "S\u00fctun grubu",
"Row type": "Sat\u0131r tipi",
"Header": "Ba\u015fl\u0131k",
"Body": "G\u00f6vde",
"Footer": "Alt",
"Border color": "Kenarl\u0131k rengi",
"Insert template...": "\u015eablon ekle...",
"Templates": "\u015eablonlar",
"Template": "Taslak",
"Text color": "Yaz\u0131 rengi",
"Background color": "Arka plan rengi",
"Custom...": "\u00d6zel...",
"Custom color": "\u00d6zel renk",
"No color": "Renk yok",
"Remove color": "Rengi kald\u0131r",
"Table of Contents": "\u0130\u00e7erik tablosu",
"Show blocks": "Bloklar\u0131 g\u00f6ster",
"Show invisible characters": "G\u00f6r\u00fcnmez karakterleri g\u00f6ster",
"Word count": "Kelime say\u0131s\u0131",
"Count": "Say\u0131m",
"Document": "Belge",
"Selection": "Se\u00e7im",
"Words": "S\u00f6zc\u00fck",
"Words: {0}": "Kelime: {0}",
"{0} words": "{0} words",
"File": "Dosya",
"Edit": "D\u00fczenle",
"Insert": "Ekle",
"View": "G\u00f6r\u00fcn\u00fcm",
"Format": "Bi\u00e7im",
"Table": "Tablo",
"Tools": "Ara\u00e7lar",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Zengin Metin Alan\u0131. Men\u00fc i\u00e7in ALT-F9 tu\u015funa bas\u0131n\u0131z. Ara\u00e7 \u00e7ubu\u011fu i\u00e7in ALT-F10 tu\u015funa bas\u0131n\u0131z. Yard\u0131m i\u00e7in ALT-0 tu\u015funa bas\u0131n\u0131z.",
"Image title": "Resim ba\u015fl\u0131\u011f\u0131",
"Border width": "Kenar geni\u015fli\u011fi",
"Border style": "Kenar sitili",
"Error": "Hata",
"Warn": "Uyar\u0131",
"Valid": "Ge\u00e7erli",
"To open the popup, press Shift+Enter": "Popup'\u0131 a\u00e7mak i\u00e7in Shift+Enter'a bas\u0131n",
"Rich Text Area. Press ALT-0 for help.": "Zengin Metin Alan\u0131. Yard\u0131m i\u00e7in Alt-0'a bas\u0131n.",
"System Font": "Sistem Yaz\u0131 Tipi",
"Failed to upload image: {0}": "Resim y\u00fcklenemedi: {0}",
"Failed to load plugin: {0} from url {1}": "Eklenti y\u00fcklenemedi: {1} url\u2019sinden {0}",
"Failed to load plugin url: {0}": "Url eklentisi y\u00fcklenemedi: {0}",
"Failed to initialize plugin: {0}": "Eklenti ba\u015flat\u0131lamad\u0131: {0}",
"example": "\u00f6rnek",
"Search": "Ara",
"All": "T\u00fcm\u00fc",
"Currency": "Para birimi",
"Text": "Metin",
"Quotations": "Al\u0131nt\u0131",
"Mathematical": "Matematik",
"Extended Latin": "Uzat\u0131lm\u0131\u015f Latin",
"Symbols": "Semboller",
"Arrows": "Oklar",
"User Defined": "Kullan\u0131c\u0131 Tan\u0131ml\u0131",
"dollar sign": "dolar i\u015fareti",
"currency sign": "para birimi i\u015fareti",
"euro-currency sign": "euro para birimi i\u015fareti",
"colon sign": "colon i\u015fareti",
"cruzeiro sign": "cruzeiro i\u015fareti",
"french franc sign": "frans\u0131z frang\u0131 i\u015fareti",
"lira sign": "lira i\u015fareti",
"mill sign": "mill i\u015fareti",
"naira sign": "naira i\u015fareti",
"peseta sign": "peseta i\u015fareti",
"rupee sign": "rupi i\u015fareti",
"won sign": "won i\u015fareti",
"new sheqel sign": "yeni \u015fekel i\u015fareti",
"dong sign": "dong i\u015fareti",
"kip sign": "kip i\u015fareti",
"tugrik sign": "tugrik i\u015fareti",
"drachma sign": "drahma i\u015fareti",
"german penny symbol": "alman kuru\u015f sembol\u00fc",
"peso sign": "peso i\u015fareti",
"guarani sign": "guarani i\u015fareti",
"austral sign": "austral i\u015fareti",
"hryvnia sign": "hrivniya i\u015fareti",
"cedi sign": "cedi i\u015fareti",
"livre tournois sign": "livre tournois i\u015fareti",
"spesmilo sign": "spesmilo i\u015fareti",
"tenge sign": "tenge i\u015fareti",
"indian rupee sign": "hindistan rupisi i\u015fareti",
"turkish lira sign": "t\u00fcrk liras\u0131 i\u015fareti",
"nordic mark sign": "nordic i\u015fareti",
"manat sign": "manat i\u015fareti",
"ruble sign": "ruble i\u015fareti",
"yen character": "yen karakteri",
"yuan character": "yuan karakteri",
"yuan character, in hong kong and taiwan": "yuan karakteri, hong kong ve tayvan'da kullan\u0131lan",
"yen\/yuan character variant one": "yen\/yuan karakter de\u011fi\u015fkeni",
"Loading emoticons...": "\u0130fadeler y\u00fckleniyor...",
"Could not load emoticons": "\u0130fadeler y\u00fcklenemedi",
"People": "\u0130nsan",
"Animals and Nature": "Hayvanlar ve Do\u011fa",
"Food and Drink": "Yiyecek ve \u0130\u00e7ecek",
"Activity": "Etkinlik",
"Travel and Places": "Gezi ve Yerler",
"Objects": "Nesneler",
"Flags": "Bayraklar",
"Characters": "Karakter",
"Characters (no spaces)": "Karakter (bo\u015fluksuz)",
"{0} characters": "{0} karakter",
"Error: Form submit field collision.": "Hata: Form g\u00f6nderme alan\u0131 \u00e7at\u0131\u015fmas\u0131.",
"Error: No form element found.": "Hata: Form eleman\u0131 bulunamad\u0131.",
"Update": "G\u00fcncelle\u015ftir",
"Color swatch": "Renk \u00f6rne\u011fi",
"Turquoise": "Turkuaz",
"Green": "Ye\u015fil",
"Blue": "Mavi",
"Purple": "Mor",
"Navy Blue": "Lacivert",
"Dark Turquoise": "Koyu Turkuaz",
"Dark Green": "Koyu Ye\u015fil",
"Medium Blue": "Donuk Mavi",
"Medium Purple": "Orta Mor",
"Midnight Blue": "Gece Yar\u0131s\u0131 Mavisi",
"Yellow": "Sar\u0131",
"Orange": "Turuncu",
"Red": "K\u0131rm\u0131z\u0131",
"Light Gray": "A\u00e7\u0131k Gri",
"Gray": "Gri",
"Dark Yellow": "Koyu Sar\u0131",
"Dark Orange": "Koyu Turuncu",
"Dark Red": "Koyu K\u0131rm\u0131z\u0131",
"Medium Gray": "Orta Gri",
"Dark Gray": "Koyu Gri",
"Light Green": "A\u00e7\u0131k Ye\u015fil",
"Light Yellow": "A\u00e7\u0131k Sar\u0131",
"Light Red": "A\u00e7\u0131k K\u0131rm\u0131z\u0131",
"Light Purple": "A\u00e7\u0131k Mor",
"Light Blue": "A\u00e7\u0131k Mavi",
"Dark Purple": "Koyu Mor",
"Dark Blue": "Lacivert",
"Black": "Siyah",
"White": "Beyaz",
"Switch to or from fullscreen mode": "Tam ekran moduna ge\u00e7 veya \u00e7\u0131k",
"Open help dialog": "Yard\u0131m penceresini a\u00e7",
"history": "ge\u00e7mi\u015f",
"styles": "stiller",
"formatting": "bi\u00e7imlendirme",
"alignment": "hizalanma",
"indentation": "girinti",
"permanent pen": "kal\u0131c\u0131 kalem",
"comments": "yorumlar",
"Format Painter": "Bi\u00e7im Boyac\u0131s\u0131",
"Insert\/edit iframe": "\u0130frame ekle\/d\u00fczenle",
"Capitalization": "B\u00fcy\u00fck Harfle Yaz\u0131m",
"lowercase": "k\u00fc\u00e7\u00fck harf",
"UPPERCASE": "B\u00dcY\u00dcK HARF",
"Title Case": "\u0130lk Harfler B\u00fcy\u00fck",
"Permanent Pen Properties": "Kal\u0131c\u0131 Kalem \u00d6zellikleri",
"Permanent pen properties...": "Kal\u0131c\u0131 kalem \u00f6zellikleri...",
"Font": "Yaz\u0131 Tipi",
"Size": "Boyut",
"More...": "Devam\u0131...",
"Spellcheck Language": "Yaz\u0131m Denetimi Dili",
"Select...": "Se\u00e7...",
"Preferences": "Tercihler",
"Yes": "Evet",
"No": "Hay\u0131r",
"Keyboard Navigation": "Klavye Tu\u015flar\u0131",
"Version": "S\u00fcr\u00fcm",
"Anchor": "\u00c7apa",
"Special character": "\u00d6zel karakter",
"Code sample": "Code sample",
"Color": "Renk",
"Emoticons": "\u0130fadeler",
"Document properties": "Dok\u00fcman \u00f6zellikleri",
"Image": "Resim",
"Insert link": "Ba\u011flant\u0131 ekle",
"Target": "Hedef",
"Link": "Ba\u011flant\u0131",
"Poster": "Poster",
"Media": "Medya",
"Print": "Yazd\u0131r",
"Prev": "\u00d6nceki",
"Find and replace": "Bul ve de\u011fi\u015ftir",
"Whole words": "Tam kelimeler",
"Spellcheck": "Yaz\u0131m denetimi",
"Caption": "Ba\u015fl\u0131k",
"Insert template": "\u015eablon ekle"
});