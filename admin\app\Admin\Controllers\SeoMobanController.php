<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SeoMoban;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SeoMobanController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SeoMoban(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('link_rules');
            $grid->column('open_home')->display(function ($val) {
                if ($val == 1) {
                    return '开';
                } else {
                    return '关';
                }
            });
            $grid->column('open_link')->display(function ($val) {
                if ($val == 1) {
                    return '开';
                } else {
                    return '关';
                }
            });
            $grid->column('open_page')->display(function ($val) {
                if ($val == 1) {
                    return '开';
                } else {
                    return '关';
                }
            });
            $grid->column('open_cache')->display(function ($val) {
                if ($val == 1) {
                    return '开';
                } else {
                    return '关';
                }
            });
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SeoMoban(), function (Show $show) {
            $show->field('id');
            $show->field('name');            
            $show->field('link_rules');
            $show->field('open_home')->using(['1'=> '开启','0' => '关闭']);
            $show->field('open_link')->using(['1'=> '开启','0' => '关闭']);
            $show->field('open_page')->using(['1'=> '开启','0' => '关闭']);
            $show->field('open_cache')->using(['1'=> '开启','0' => '关闭']);
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SeoMoban(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->textarea('link_rules')->help("111");
            $form->radio('open_home')->options(['1'=> '开启','0' => '关闭'])->default('0');
            $form->radio('open_link')->options(['1'=> '开启','0' => '关闭'])->default('0');
            $form->radio('open_page')->options(['1'=> '开启','0' => '关闭'])->default('0');
            $form->radio('open_cache')->options(['1'=> '开启','0' => '关闭'])->default('0');
        });
    }
}
