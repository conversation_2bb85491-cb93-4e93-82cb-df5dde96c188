<?php

namespace App\Admin\Controllers;

use App\Models\SpiderLog;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Widgets\Tab;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Redis;
use Illuminate\Http\Request;

class SeoSpiderLogController extends AdminController
{
    /**
     * 网站过滤器状态
     */
    protected $site = null;
    
    /**
     * 日期过滤器状态
     */
    protected $date = null;
    
    /**
     * 数据源选择（redis或mysql）
     */
    protected $source = 'redis';
    
    public function __construct(Request $request)
    {
        if(isset($request->site) && !empty($request->site)){
            $this->site = $request->site;
        }
        
        if(isset($request->date) && !empty($request->date)){
            $this->date = $request->date;
        } else {
            $this->date = date('Ymd');
        }
        
        if(isset($request->source) && in_array($request->source, ['redis', 'mysql'])){
            $this->source = $request->source;
        } else {
            // 默认逻辑：如果日期是今天或昨天，使用Redis，否则使用MySQL
            $today = date('Ymd');
            $yesterday = date('Ymd', strtotime('-1 day'));
            
            if($this->date == $today || $this->date == $yesterday) {
                $this->source = 'redis';
            } else {
                $this->source = 'mysql';
            }
        }
    }
    
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        // 根据数据源选择使用不同的数据模型
        if($this->source == 'mysql') {
            return $this->mysqlGrid();
        } else {
            return $this->redisGrid();
        }
    }
    
    /**
     * 从MySQL读取数据的Grid
     */
    protected function mysqlGrid()
    {
        return Grid::make(\App\Models\SpiderLog::class, function (Grid $grid) {
            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用行操作列
            $grid->disableActions();
            // 禁用批量操作
            $grid->disableBatchActions();
            
            // 添加头部选项卡
            $grid->header(function(){
                return $this->buildTabs();
            });
            
            // 添加导入按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="javascript:void(0);" class="btn btn-primary btn-sm import-logs-btn"><i class="fa fa-download"></i> 导入到数据库</a>');
                
                // 添加JavaScript代码，处理按钮点击事件
                admin_script(<<<JS
                $('.import-logs-btn').click(function () {
                    Dcat.loading();
                    $.ajax({
                        url: '/admin/api/spider-import',
                        type: 'POST',
                        data: {_token: Dcat.token},
                        success: function (data) {
                            Dcat.loading(false);
                            if (data.status) {
                                Dcat.success(data.message);
                                setTimeout(function () {
                                    location.reload();
                                }, 1000);
                            } else {
                                Dcat.error(data.message);
                            }
                        },
                        error: function (xhr) {
                            Dcat.loading(false);
                            Dcat.error('导入失败，请稍后再试');
                        }
                    });
                });
                JS);
            });
            
            // 添加网站筛选器
            $this->addSiteFilter($grid);
            
            // 添加日期筛选器
            $this->addDateFilter($grid);
            
            // 添加数据源切换器
            $this->addSourceSwitcher($grid);
            
            // 定义列
            $grid->column('id', 'ID')->sortable();
            $grid->column('created_at', '时间')->sortable();
            $grid->column('site', '网站')->sortable();
            $grid->column('user_agent', 'User Agent')->limit(50);
            $grid->column('url', 'URL')->limit(50);
            $grid->column('ip', 'IP地址');
            
            // 设置默认排序
            $grid->model()->orderBy('created_at', 'desc');
            
            // 设置每页显示的记录数
            $grid->paginate(15);
            
            // 如果有日期筛选，添加到查询条件
            if ($this->date) {
                $start = date('Y-m-d 00:00:00', strtotime($this->date));
                $end = date('Y-m-d 23:59:59', strtotime($this->date));
                $grid->model()->whereBetween('created_at', [$start, $end]);
            }
            
            // 如果有网站筛选，添加到查询条件
            if ($this->site) {
                $grid->model()->where('site', $this->site);
            }
        });
    }
    
    /**
     * 从Redis读取数据的Grid
     */
    protected function redisGrid()
    {
        // 创建一个自定义数据仓库用于Redis数据
        $repository = new class() extends \Dcat\Admin\Repositories\Repository {
            protected $data = [];
            
            public function setData($data)
            {
                $this->data = $data;
                return $this;
            }
            
            public function get(\Dcat\Admin\Grid\Model $model)
            {
                // 处理分页
                if ($model->getPerPage() > 0) {
                    $currentPage = $model->getCurrentPage();
                    $perPage = $model->getPerPage();
                    
                    $offset = ($currentPage - 1) * $perPage;
                    $data = array_slice($this->data, $offset, $perPage);
                    
                    // 创建分页对象
                    $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                        $data,
                        count($this->data),
                        $perPage,
                        $currentPage
                    );
                    
                    return $paginator;
                }
                
                return collect($this->data);
            }
            
            public function getKeyName()
            {
                return 'id';
            }
        };
        
        // 从Redis获取蜘蛛日志数据
        $logs = $this->getSpiderLogs();
        
        // 为每条记录添加ID
        foreach ($logs as $key => $log) {
            $logs[$key]['id'] = $key + 1;
        }
        
        // 设置数据
        $repository->setData($logs);
        
        // 创建Grid
        return Grid::make($repository, function (Grid $grid) {
            // 禁用创建按钮
            $grid->disableCreateButton();
            // 禁用行操作列
            $grid->disableActions();
            // 禁用批量操作
            $grid->disableBatchActions();
            
            // 添加头部选项卡
            $grid->header(function(){
                return $this->buildTabs();
            });
            
            // 添加导入按钮
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append('<a href="javascript:void(0);" class="btn btn-primary btn-sm import-logs-btn"><i class="fa fa-download"></i> 导入到数据库</a>');
                
                // 添加JavaScript代码，处理按钮点击事件
                admin_script(<<<JS
                $('.import-logs-btn').click(function () {
                    Dcat.loading();
                    $.ajax({
                        url: '/admin/api/spider-import',
                        type: 'POST',
                        data: {_token: Dcat.token},
                        success: function (data) {
                            Dcat.loading(false);
                            if (data.status) {
                                Dcat.success(data.message);
                                setTimeout(function () {
                                    location.reload();
                                }, 1000);
                            } else {
                                Dcat.error(data.message);
                            }
                        },
                        error: function (xhr) {
                            Dcat.loading(false);
                            Dcat.error('导入失败，请稍后再试');
                        }
                    });
                });
                JS);
            });
            
            // 添加网站筛选器
            $this->addSiteFilter($grid);
            
            // 添加日期筛选器
            $this->addDateFilter($grid);
            
            // 添加数据源切换器
            $this->addSourceSwitcher($grid);
            
            // 定义列
            $grid->column('id', 'ID')->sortable();
            $grid->column('time', '时间')->sortable();
            $grid->column('site', '网站')->sortable();
            $grid->column('visit_count', '访问次数')->sortable();
            $grid->column('user_agent', 'User Agent')->limit(50);
            
            // 设置默认排序
            $grid->model()->orderBy('time', 'desc');
            
            // 设置每页显示的记录数
            $grid->paginate(15);
        });
    }
    
    /**
     * 构建选项卡
     */
    protected function buildTabs()
    {
        $tab = new Tab();
        $current_url = url()->current();
        $query = request()->query();
        
        // 构建Redis和MySQL的URL
        $redis_query = $query;
        $redis_query['source'] = 'redis';
        $redis_url = $current_url . '?' . http_build_query($redis_query);
        
        $mysql_query = $query;
        $mysql_query['source'] = 'mysql';
        $mysql_url = $current_url . '?' . http_build_query($mysql_query);
        
        // 添加选项卡
        $tab->add('Redis数据', '<p>Redis数据源显示最近的蜘蛛爬行记录，包括访问次数统计。</p>', $this->source == 'redis');
        $tab->add('MySQL数据', '<p>MySQL数据源显示已导入的历史蜘蛛爬行记录，包含更详细的信息。</p>', $this->source == 'mysql');
        
        return $tab;
    }
    
    /**
     * 添加网站筛选器
     */
    protected function addSiteFilter($grid)
    {
        $sites = $this->getSiteList();
        if (!empty($sites)) {
            $filter_html = '<div class="mb-2">';
            $filter_html .= '<form class="form-inline" method="get">';
            $filter_html .= '<div class="form-group mr-2">';
            $filter_html .= '<label class="mr-1">网站：</label>';
            $filter_html .= '<select class="form-control" name="site" onchange="this.form.submit()">';
            $filter_html .= '<option value="">全部</option>';
            
            foreach ($sites as $site) {
                $selected = ($this->site == $site) ? 'selected' : '';
                $filter_html .= "<option value=\"$site\" $selected>$site</option>";
            }
            
            $filter_html .= '</select>';
            $filter_html .= '</div>';
            
            // 保持其他参数
            if ($this->date) {
                $filter_html .= '<input type="hidden" name="date" value="' . $this->date . '">';
            }
            
            $filter_html .= '<input type="hidden" name="source" value="' . $this->source . '">';
            
            // 如果有筛选条件，添加一个清除筛选的按钮
            if ($this->site) {
                $clear_url = "?";
                if($this->date) {
                    $clear_url .= "date=" . $this->date . "&";
                }
                $clear_url .= "source=" . $this->source;
                $filter_html .= '<a href="' . $clear_url . '" class="btn btn-sm btn-default">清除筛选</a>';
            }
            
            $filter_html .= '</form>';
            $filter_html .= '</div>';
            
            $grid->tools($filter_html);
        }
    }
    
    /**
     * 添加日期筛选器
     */
    protected function addDateFilter($grid)
    {
        // 获取最近7天的日期选项
        $dateOptions = [];
        for ($i = 0; $i < 7; $i++) {
            $date = date('Ymd', strtotime("-{$i} days"));
            $label = date('Y-m-d', strtotime("-{$i} days"));
            $dateOptions[$date] = $label;
        }
        
        $filter_html = '<div class="mb-2 ml-2">';
        $filter_html .= '<form class="form-inline" method="get">';
        $filter_html .= '<div class="form-group mr-2">';
        $filter_html .= '<label class="mr-1">日期：</label>';
        $filter_html .= '<select class="form-control" name="date" onchange="this.form.submit()">';
        
        foreach ($dateOptions as $value => $label) {
            $selected = ($this->date == $value) ? 'selected' : '';
            $filter_html .= "<option value=\"$value\" $selected>$label</option>";
        }
        
        $filter_html .= '</select>';
        $filter_html .= '</div>';
        
        // 保持其他参数
        if ($this->site) {
            $filter_html .= '<input type="hidden" name="site" value="' . $this->site . '">';
        }
        
        $filter_html .= '<input type="hidden" name="source" value="' . $this->source . '">';
        $filter_html .= '</form>';
        $filter_html .= '</div>';
        
        $grid->tools($filter_html);
    }
    
    /**
     * 添加数据源切换器
     */
    protected function addSourceSwitcher($grid)
    {
        $current_url = url()->current();
        $query = request()->query();
        
        // 创建Redis链接
        $redis_query = $query;
        $redis_query['source'] = 'redis';
        $redis_url = $current_url . '?' . http_build_query($redis_query);
        
        // 创建MySQL链接
        $mysql_query = $query;
        $mysql_query['source'] = 'mysql';
        $mysql_url = $current_url . '?' . http_build_query($mysql_query);
        
        $redis_active = $this->source == 'redis' ? 'active' : '';
        $mysql_active = $this->source == 'mysql' ? 'active' : '';
        
        $switcher = <<<HTML
        <div class="btn-group float-right mr-2" role="group">
            <a href="{$redis_url}" class="btn btn-sm btn-{$redis_active} {$redis_active}">Redis数据</a>
            <a href="{$mysql_url}" class="btn btn-sm btn-{$mysql_active} {$mysql_active}">MySQL数据</a>
        </div>
HTML;
        
        $grid->tools($switcher);
    }
    
    /**
     * 从Redis中获取蜘蛛日志数据
     */
    protected function getSpiderLogs()
    {
        $logs = [];
        $date = $this->date;
        $site_filter = $this->site;
        
        // 构建Redis键模式 - 使用正确的键格式
        $pattern = "total:{$date}:*:0"; // 0表示Google蜘蛛访问
        
        // 获取所有匹配的键
        $keys = Redis::keys($pattern);
        
        foreach ($keys as $key) {
            // 解析键名中的信息
            $parts = explode(':', $key);
            if (count($parts) >= 4) {
                $date = $parts[1] ?? '';
                $site = $parts[2] ?? '';
                $type = $parts[3] ?? '';
                
                // 如果设置了网站筛选，且不匹配当前网站，则跳过
                if ($site_filter && $site != $site_filter) {
                    continue;
                }
                
                // 获取访问次数
                $visit_count = Redis::get($key) ?: 0;
                
                // 获取最后访问时间
                $last_time_key = "lasttime:{$site}";
                $timestamp = Redis::get($last_time_key) ?: time();
                
                // 构建日志记录
                $logs[] = [
                    'time' => date('Y-m-d H:i:s', $timestamp),
                    'site' => $site,
                    'user_agent' => 'Googlebot', // 默认为Google蜘蛛
                    'url' => '/', // 无法从Redis键中获取具体URL
                    'ip' => '', // 无法从Redis键中获取IP
                    'visit_count' => $visit_count,
                    'date' => $date
                ];
            }
        }
        
        // 按时间倒序排序
        usort($logs, function($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });
        
        return $logs;
    }
    
    /**
     * 获取所有有日志记录的网站列表
     */
    protected function getSiteList()
    {
        $sites = [];
        
        if($this->source == 'mysql') {
            // 从MySQL获取网站列表
            $sites = SpiderLog::select('site')
                    ->distinct()
                    ->pluck('site')
                    ->toArray();
        } else {
            // 从Redis获取网站列表
            $date = $this->date;
            
            // 获取当前日期的所有蜘蛛日志键
            $keys = Redis::keys("spider_log:{$date}:*");
            
            foreach ($keys as $key) {
                $parts = explode(':', $key);
                if (count($parts) >= 3) {
                    $site = $parts[2];
                    if (!empty($site) && !in_array($site, $sites)) {
                        $sites[] = $site;
                    }
                }
            }
        }
        
        sort($sites);
        return $sites;
    }
    
    /**
     * 识别常见的搜索引擎蜘蛛
     */
    protected function identifySpider($user_agent)
    {
        $user_agent = strtolower($user_agent);
        
        if (strpos($user_agent, 'googlebot') !== false) {
            return 'Google';
        } elseif (strpos($user_agent, 'bingbot') !== false) {
            return 'Bing';
        } elseif (strpos($user_agent, 'baiduspider') !== false) {
            return '百度';
        } elseif (strpos($user_agent, 'yandexbot') !== false) {
            return 'Yandex';
        } elseif (strpos($user_agent, 'sogou') !== false) {
            return '搜狗';
        } elseif (strpos($user_agent, '360spider') !== false) {
            return '360搜索';
        } elseif (strpos($user_agent, 'bytespider') !== false) {
            return '字节搜索';
        } elseif (strpos($user_agent, 'ahrefsbot') !== false) {
            return 'Ahrefs';
        } elseif (strpos($user_agent, 'semrushbot') !== false) {
            return 'SEMrush';
        } elseif (strpos($user_agent, 'slurp') !== false) {
            return 'Yahoo';
        }
        
        return null;
    }
} 