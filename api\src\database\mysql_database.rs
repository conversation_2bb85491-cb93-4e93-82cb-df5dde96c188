use crate::GL<PERSON><PERSON>L_CONFIG;
use sqlx::mysql::{MySqlPool, MySqlPoolOptions};
use std::time::Duration;

pub async fn connect() -> Result<MySqlPool, sqlx::Error> {
    let database = &GLOBAL_CONFIG.database;
    println!("Initializing database connection");
    let database_url = connection_string();
    let pool = MySqlPoolOptions::new()
        .min_connections(database.min_connections)
        .max_connections(database.max_connections)
        .max_lifetime(Some(Duration::from_secs(database.max_lifetime)))
        .connect(&database_url)
        .await?;

    Ok(pool)
}

pub fn connection_string() -> String {
    let database = &GLOBAL_CONFIG.database;
    if database.password.is_empty() && database.username.is_empty() {
        format!(
            "mysql://{}:{}/{}",
            database.host, database.port, database.db
        )
    } else {
        format!(
            "mysql://{}:{}@{}:{}/{}",
            database.username, database.password, database.host, database.port, database.db
        )
    }
}
