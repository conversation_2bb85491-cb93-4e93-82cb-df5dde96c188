<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;

class UpdateDatabaseSchema extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:update-schema {--force : 强制执行，跳过确认}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新数据库表结构，确保与最新版本保持一致';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('=== 数据库结构更新工具 ===');
        $this->info('开始时间: ' . now()->format('Y-m-d H:i:s'));
        
        if (!$this->option('force')) {
            if (!$this->confirm('确定要更新数据库结构吗？此操作会修改数据库表结构。')) {
                $this->info('操作已取消');
                return 0;
            }
        }
        
        try {
            DB::beginTransaction();
            
            // 1. 更新seo_site表
            $this->updateSeoSiteTable();
            
            // 2. 更新seo_regions表
            $this->updateSeoRegionsTable();
            
            // 3. 更新admin_menu表
            $this->updateAdminMenuTable();
            
            // 4. 更新现有数据
            $this->updateExistingData();
            
            DB::commit();
            
            $this->info('');
            $this->info('✅ 所有更新完成！');
            $this->info('结束时间: ' . now()->format('Y-m-d H:i:s'));
            
            return 0;
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            $this->error('❌ 更新失败: ' . $e->getMessage());
            $this->error('文件: ' . $e->getFile());
            $this->error('行号: ' . $e->getLine());
            
            return 1;
        }
    }
    
    /**
     * 更新seo_site表结构
     */
    protected function updateSeoSiteTable()
    {
        $this->info('');
        $this->info('=== 检查seo_site表结构 ===');
        
        Schema::table('seo_site', function (Blueprint $table) {
            // 检查region_code字段
            if (!Schema::hasColumn('seo_site', 'region_code')) {
                $table->string('region_code', 10)->default('default')->comment('地区代码');
                $this->info('✅ 添加region_code字段成功');
            } else {
                $this->info('✅ region_code字段已存在');
            }
            
            // 检查last_sync字段
            if (!Schema::hasColumn('seo_site', 'last_sync')) {
                $table->bigInteger('last_sync')->default(0)->comment('最后同步时间');
                $this->info('✅ 添加last_sync字段成功');
            } else {
                $this->info('✅ last_sync字段已存在');
            }
        });
        
        // 添加索引
        try {
            DB::statement('CREATE INDEX idx_region_state ON seo_site(region_code, state)');
            $this->info('✅ 添加idx_region_state索引成功');
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $this->info('✅ idx_region_state索引已存在');
            } else {
                throw $e;
            }
        }
        
        try {
            DB::statement('CREATE INDEX idx_region_host ON seo_site(region_code, host)');
            $this->info('✅ 添加idx_region_host索引成功');
        } catch (\Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                $this->info('✅ idx_region_host索引已存在');
            } else {
                throw $e;
            }
        }
    }
    
    /**
     * 更新seo_regions表结构
     */
    protected function updateSeoRegionsTable()
    {
        $this->info('');
        $this->info('=== 检查seo_regions表结构 ===');
        
        Schema::table('seo_regions', function (Blueprint $table) {
            if (!Schema::hasColumn('seo_regions', 'jump_script')) {
                $table->text('jump_script')->nullable()->comment('跳转脚本');
                $this->info('✅ 添加jump_script字段成功');
            } else {
                $this->info('✅ jump_script字段已存在');
            }
        });
        
        // 确保默认地区记录存在
        $defaultExists = DB::table('seo_regions')->where('code', 'default')->exists();
        
        if (!$defaultExists) {
            DB::table('seo_regions')->insert([
                'code' => 'default',
                'name' => '默认地区',
                'language' => 'en',
                'timezone' => 'UTC',
                'config' => '{}',
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
            $this->info('✅ 添加默认地区记录成功');
        } else {
            $this->info('✅ 默认地区记录已存在');
        }
    }
    
    /**
     * 更新admin_menu表结构
     */
    protected function updateAdminMenuTable()
    {
        $this->info('');
        $this->info('=== 检查admin_menu表结构 ===');
        
        // 检查是否存在独立的地区管理菜单
        $regionMainMenuExists = DB::table('admin_menu')
            ->where('title', '地区管理')
            ->where('parent_id', 0)
            ->exists();
        
        if (!$regionMainMenuExists) {
            $this->info('重构菜单结构...');
            
            // 查找SEO管理菜单
            $seoMenu = DB::table('admin_menu')
                ->whereIn('title', ['SEO管理', 'Seo'])
                ->where('parent_id', 0)
                ->first();
            
            if ($seoMenu) {
                // 查找SEO下的地区管理菜单
                $oldRegionMenu = DB::table('admin_menu')
                    ->where('title', '地区管理')
                    ->where('parent_id', $seoMenu->id)
                    ->first();
                
                // 创建独立的地区管理主菜单
                $newRegionMenuId = DB::table('admin_menu')->insertGetId([
                    'parent_id' => 0,
                    'order' => 3,
                    'title' => '地区管理',
                    'icon' => 'fa-globe',
                    'uri' => '',
                    'show' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                // 如果存在旧的地区管理菜单，移动其子菜单
                if ($oldRegionMenu) {
                    DB::table('admin_menu')
                        ->where('parent_id', $oldRegionMenu->id)
                        ->update(['parent_id' => $newRegionMenuId]);
                    
                    DB::table('admin_menu')->where('id', $oldRegionMenu->id)->delete();
                }
                
                // 确保地区管理子菜单存在
                $this->ensureSubMenu($newRegionMenuId, '地区配置', 'fa-cog', 'seo/regions', 1);
                $this->ensureSubMenu($newRegionMenuId, '地区统计', 'fa-bar-chart', 'seo/regions-stats', 2);
                
                $this->info('✅ 地区管理菜单结构重构完成');
            }
        } else {
            $this->info('✅ 地区管理菜单结构已正确');
        }
        
        // 检查蜘蛛日志菜单
        $spiderLogExists = DB::table('admin_menu')->where('title', '蜘蛛日志')->exists();
        
        if (!$spiderLogExists) {
            $seoMenu = DB::table('admin_menu')
                ->whereIn('title', ['SEO管理', 'Seo'])
                ->where('parent_id', 0)
                ->first();
            
            if ($seoMenu) {
                DB::table('admin_menu')->insert([
                    'parent_id' => $seoMenu->id,
                    'order' => 16,
                    'title' => '蜘蛛日志',
                    'icon' => 'fa-bug',
                    'uri' => 'seo/spider-log',
                    'show' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                $this->info('✅ 蜘蛛日志菜单添加完成');
            }
        } else {
            $this->info('✅ 蜘蛛日志菜单已存在');
        }
    }
    
    /**
     * 确保子菜单存在
     */
    protected function ensureSubMenu($parentId, $title, $icon, $uri, $order)
    {
        $exists = DB::table('admin_menu')
            ->where('title', $title)
            ->where('parent_id', $parentId)
            ->exists();
        
        if (!$exists) {
            DB::table('admin_menu')->insert([
                'parent_id' => $parentId,
                'order' => $order,
                'title' => $title,
                'icon' => $icon,
                'uri' => $uri,
                'show' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
    
    /**
     * 更新现有数据
     */
    protected function updateExistingData()
    {
        $this->info('');
        $this->info('=== 更新现有数据 ===');
        
        // 更新空的region_code为default
        $updated = DB::table('seo_site')
            ->whereNull('region_code')
            ->orWhere('region_code', '')
            ->update(['region_code' => 'default']);
        
        if ($updated > 0) {
            $this->info("✅ 更新了 {$updated} 个空region_code记录为'default'");
        }
        
        // 根据域名后缀智能分配地区代码
        $domainMappings = [
            '.com.br' => 'br',    // 巴西
            '.co.in' => 'in',     // 印度
            '.com.pk' => 'pk',    // 巴基斯坦
            '.co.id' => 'id',     // 印尼
            '.com.mx' => 'mx',    // 墨西哥
            '.com.ng' => 'ng',    // 尼日利亚
            '.com.bd' => 'bd',    // 孟加拉国
            '.com.ph' => 'ph',    // 菲律宾
            '.com.vn' => 'vn',    // 越南
            '.co.jp' => 'jp',     // 日本
            '.co.th' => 'th',     // 泰国
        ];
        
        $totalAssigned = 0;
        foreach ($domainMappings as $suffix => $regionCode) {
            $affected = DB::table('seo_site')
                ->where('host', 'LIKE', "%{$suffix}")
                ->where('region_code', 'default')
                ->update(['region_code' => $regionCode]);
            
            if ($affected > 0) {
                $this->info("✅ 根据域名后缀 {$suffix} 分配了 {$affected} 个站点到地区 {$regionCode}");
                $totalAssigned += $affected;
            }
        }
        
        // 统计结果
        $totalSites = DB::table('seo_site')->count();
        $defaultSites = DB::table('seo_site')->where('region_code', 'default')->count();
        $assignedSites = $totalSites - $defaultSites;
        
        $this->info('');
        $this->info('📊 地区代码更新统计:');
        $this->info("- 总网站数: {$totalSites}");
        $this->info("- 已分配地区: {$assignedSites}");
        $this->info("- 默认地区: {$defaultSites}");
    }
}
