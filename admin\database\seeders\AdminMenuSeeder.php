<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Dcat\Admin\Models\Menu;
use Illuminate\Support\Facades\DB;

class AdminMenuSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // 查找SEO父菜单
        $seoParent = Menu::where('title', 'SEO管理')->first();
        
        if (!$seoParent) {
            // 如果SEO父菜单不存在，创建一个
            $seoParent = Menu::create([
                'parent_id' => 0,
                'order' => 1,
                'title' => 'SEO管理',
                'icon' => 'fa-search',
                'uri' => NULL,
            ]);
        }
        
        // 检查地区管理菜单是否已存在
        $regionMenu = Menu::where('title', '地区管理')->where('parent_id', $seoParent->id)->first();

        if (!$regionMenu) {
            // 如果地区管理菜单不存在，创建一个
            $regionMenu = Menu::create([
                'parent_id' => $seoParent->id,
                'order' => 2, // 排在配置之后
                'title' => '地区管理',
                'icon' => 'feather icon-map',
                'uri' => 'seo/regions',
            ]);

            $this->command->info('地区管理菜单已添加');
        } else {
            $this->command->info('地区管理菜单已存在，无需添加');
        }

        // 检查地区统计菜单是否已存在
        $regionStatsMenu = Menu::where('title', '地区统计')->where('parent_id', $regionMenu->id)->first();

        if (!$regionStatsMenu) {
            // 如果地区统计菜单不存在，创建一个
            Menu::create([
                'parent_id' => $regionMenu->id,
                'order' => 1,
                'title' => '地区统计',
                'icon' => 'feather icon-bar-chart-2',
                'uri' => 'seo/regions-stats',
            ]);

            $this->command->info('地区统计菜单已添加');
        } else {
            $this->command->info('地区统计菜单已存在，无需添加');
        }

        // 检查蜘蛛日志菜单是否已存在
        $spiderLogMenu = Menu::where('title', '蜘蛛日志')->where('parent_id', $seoParent->id)->first();

        if (!$spiderLogMenu) {
            // 如果蜘蛛日志菜单不存在，创建一个
            Menu::create([
                'parent_id' => $seoParent->id,
                'order' => 8, // 设置一个较大的顺序值，让它排在其他SEO子菜单后面
                'title' => '蜘蛛日志',
                'icon' => 'fa-bug',
                'uri' => 'seo/spider-log',
            ]);

            $this->command->info('蜘蛛日志菜单已添加');
        } else {
            $this->command->info('蜘蛛日志菜单已存在，无需添加');
        }
    }
} 