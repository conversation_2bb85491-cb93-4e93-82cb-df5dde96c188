# 使用 Ubuntu 22.04 LTS 作为基础镜像
FROM ubuntu:22.04

# 设置非交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 只安装运行时依赖
RUN apt-get update && apt-get install -y \
    libssl-dev \
    default-libmysqlclient-dev \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制预编译的二进制文件和必要的配置
COPY ./target/release/api ./
COPY ./docker.env ./.env

# 设置环境变量文件路径
ENV ENV_FILE=/app/.env

# 运行应用
CMD ["./api"]