<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class CheckRedisConfigCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'redis:check';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '检查Redis配置并列出键';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始检查Redis配置...');

        // 检查Redis连接
        try {
            $this->info('Redis连接配置:');
            $this->info('Host: ' . config('database.redis.default.host'));
            $this->info('Port: ' . config('database.redis.default.port'));
            $this->info('Database: ' . config('database.redis.default.database'));
            
            // 尝试ping
            $pong = Redis::connection()->ping();
            $this->info('Redis连接测试: ' . ($pong ? 'PONG' : 'FAILED'));
            
            // 列出所有键
            $this->info('Redis键:');
            
            // 列出total:*键
            $totalKeys = Redis::keys('total:*');
            $this->info('total:* 键数量: ' . count($totalKeys));
            foreach ($totalKeys as $key) {
                $value = Redis::get($key);
                $this->info("$key = $value");
            }
            
            // 列出lasttime:*键
            $lasttimeKeys = Redis::keys('lasttime:*');
            $this->info('lasttime:* 键数量: ' . count($lasttimeKeys));
            foreach ($lasttimeKeys as $key) {
                $value = Redis::get($key);
                $this->info("$key = $value (" . date('Y-m-d H:i:s', $value) . ")");
            }
            
            $this->info('Redis检查完成。');
            return 0;
        } catch (\Exception $e) {
            $this->error('Redis连接失败: ' . $e->getMessage());
            return 1;
        }
    }
} 