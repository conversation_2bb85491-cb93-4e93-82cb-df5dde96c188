// use flate2::{
//     read::{<PERSON>z<PERSON><PERSON>ode<PERSON>, ZlibDecoder},
//     write::{<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>},
//     Compression,
// };
// use std::io::{self, Read, Write};
use std::io;
use zstd::{decode_all, encode_all};
// 定义压缩特质
pub trait Compress {
    fn compress(content: &[u8]) -> io::Result<Vec<u8>>;
}

// 定义解压特质
pub trait Decompress {
    fn decompress(compressed_content: &[u8]) -> io::Result<Vec<u8>>;
}

// // 为Gzip格式实现压缩和解压特质
// pub struct Gzip;

// impl Compress for Gzip {
//     fn compress(content: &[u8]) -> io::Result<Vec<u8>> {
//         let mut encoder = GzEncoder::new(Vec::new(), Compression::default());
//         encoder.write_all(content)?;
//         encoder.finish()
//     }
// }

// impl Decompress for Gzip {
//     fn decompress(compressed_content: &[u8]) -> io::Result<Vec<u8>> {
//         let mut decoder = GzDecoder::new(compressed_content);
//         let mut decompressed_data = Vec::new();
//         decoder.read_to_end(&mut decompressed_data)?;
//         Ok(decompressed_data)
//     }
// }

// // 为Zlib格式实现压缩和解压特质
// pub struct Zlib;

// impl Compress for Zlib {
//     fn compress(content: &[u8]) -> io::Result<Vec<u8>> {
//         let mut encoder = ZlibEncoder::new(Vec::new(), Compression::default());
//         encoder.write_all(content)?;
//         encoder.finish()
//     }
// }

// impl Decompress for Zlib {
//     fn decompress(compressed_content: &[u8]) -> io::Result<Vec<u8>> {
//         let mut decoder = ZlibDecoder::new(compressed_content);
//         let mut decompressed_data = Vec::new();
//         decoder.read_to_end(&mut decompressed_data)?;
//         Ok(decompressed_data)
//     }
// }

pub struct Zstd;

impl Compress for Zstd {
    fn compress(content: &[u8]) -> io::Result<Vec<u8>> {
        encode_all(content, 1).map_err(|e| io::Error::new(io::ErrorKind::Other, e))
    }
}

impl Decompress for Zstd {
    fn decompress(compressed_content: &[u8]) -> io::Result<Vec<u8>> {
        decode_all(compressed_content).map_err(|e| io::Error::new(io::ErrorKind::Other, e))
    }
}
