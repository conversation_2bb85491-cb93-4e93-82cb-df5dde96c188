use crate::entities::{RequestInfo, SiteConfigs, AppState};
use crate::util::string::url::replace_with_resource;
use crate::util::time;
use crate::GLOBAL_CONFIG;
use actix_web::{get, web::Data, HttpRequest, HttpResponse, Responder};
use std::fmt::Write;

#[get("/sitemap.xml")]
pub async fn generate_sitemap(
    req: HttpRequest,
    app_state: Data<AppState>,
    domains: Data<SiteConfigs>
) -> impl Responder {
    let request_info = match RequestInfo::by_web(&req) {
        Some(r) => r,
        None => return HttpResponse::InternalServerError().finish(),
    };

    // 只允许配置的爬虫访问
    if !GLOBAL_CONFIG.is_allowed_spider(&request_info.user_agent) {
        return HttpResponse::NotFound().finish();
    }

    // 获取网站配置
    let site_config = match domains.get(&request_info.host).await {
        Some(config) => config,
        None => return HttpResponse::NotFound().finish(),
    };

    // 检查网站是否启用页面生成
    if !site_config.page {
        return HttpResponse::NotFound().finish();
    }

    // 从内存中获取地区资源
    let region_resource = crate::RESOURCE_MANAGER.get_resource(&site_config.region_code);

    // 获取地区时区
    let region_timezone = app_state.region_timezone_context
        .get_region_timezone(&site_config.region_code).await;

    // 使用地区时区生成lastmod时间
    let lastmod = time::format_region_time(&region_timezone, "%Y-%m-%d");

    // 确定协议
    let protocol = if site_config.https { "https" } else { "http" };

    let rng = &mut rand::thread_rng();
    let mut buffer = String::new();

    // XML头部
    writeln!(buffer, r#"<?xml version="1.0" encoding="UTF-8"?>"#).unwrap();
    writeln!(
        buffer,
        r#"<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">"#
    ).unwrap();

    // 首页URL
    writeln!(
        buffer,
        r#"<url><loc>{}://{}{}</loc><lastmod>{}</lastmod><changefreq>always</changefreq><priority>1.0</priority></url>"#,
        protocol,
        &request_info.host,
        &request_info.port,
        &lastmod
    ).unwrap();

    // 生成内页URLs
    for _ in 0..app_state.system_config.sitemap_num {
        let url_rule = site_config.get_random_uri_rule(rng);
        let generated_path = replace_with_resource(&url_rule, rng, &region_resource);

        writeln!(
            buffer,
            r#"<url><loc>{}://{}{}{}</loc><lastmod>{}</lastmod><changefreq>always</changefreq><priority>0.8</priority></url>"#,
            protocol,
            &request_info.host,
            &request_info.port,
            generated_path,
            &lastmod
        ).unwrap();
    }

    writeln!(buffer, "</urlset>").unwrap();

    HttpResponse::Ok()
        .append_header(("Content-Type", "application/xml"))
        .body(buffer)
}
