<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;

class SeoTotal extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'seo_total';
    public $timestamps = false; // 🔧 禁用timestamps，因为表中没有这些字段

    protected $fillable = [
        'site',
        'date',
        'jump',
        'spider'
    ];

    protected $casts = [
        'date' => 'integer',
        'jump' => 'integer',
        'spider' => 'integer'
    ];



    /**
     * 获取指定网站的统计数据
     */
    public static function getStatsBySite($site, $days = 30)
    {
        $startDate = intval(date('Ymd', strtotime("-{$days} days")));

        return self::where('site', $site)
                   ->where('date', '>=', $startDate)
                   ->orderBy('date', 'desc')
                   ->get();
    }

    /**
     * 获取指定地区所有网站的统计汇总
     */
    public static function getRegionStats($regionCode, $days = 30)
    {
        // 获取地区信息以确定时区
        $region = \App\Models\SeoRegion::where('code', $regionCode)->first();
        $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

        // 使用地区时区计算日期
        try {
            $regionDate = new \DateTime('now', new \DateTimeZone($timezone));
            $today = intval($regionDate->format('Ymd'));

            $startDateTime = clone $regionDate;
            $startDateTime->modify("-{$days} days");
            $startDate = intval($startDateTime->format('Ymd'));
        } catch (\Exception $e) {
            // 时区转换失败时回退到服务器时区
            $today = intval(date('Ymd'));
            $startDate = intval(date('Ymd', strtotime("-{$days} days")));
        }

        // 获取该地区的所有网站
        $sites = \App\Models\SeoSite::where('region_code', $regionCode)
                                   ->pluck('host')
                                   ->toArray();

        if (empty($sites)) {
            return [
                'total_jump' => 0,
                'total_spider' => 0,
                'today_jump' => 0,
                'today_spider' => 0,
                'daily_stats' => []
            ];
        }

        // 获取统计数据
        $stats = self::whereIn('site', $sites)
                     ->where('date', '>=', $startDate)
                     ->get();

        return [
            'total_jump' => $stats->sum('jump'),
            'total_spider' => $stats->sum('spider'),
            'today_jump' => $stats->where('date', $today)->sum('jump'),
            'today_spider' => $stats->where('date', $today)->sum('spider'),
            'daily_stats' => $stats->groupBy('date')
                                  ->map(function ($dayStats) {
                                      return [
                                          'jump' => $dayStats->sum('jump'),
                                          'spider' => $dayStats->sum('spider')
                                      ];
                                  })
                                  ->sortKeysDesc()
        ];
    }
}
