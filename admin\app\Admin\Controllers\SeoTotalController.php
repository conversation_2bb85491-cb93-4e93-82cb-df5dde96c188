<?php

namespace App\Admin\Controllers;

use App\Models\SeoTotal;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SeoTotalController extends AdminController
{
    public $site;
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(\App\Models\SeoTotal::class, function (Grid $grid) {
            $date = "";
            if(empty($_GET['site'])&&empty($_GET['date'])){
                $date = date('Ymd');
            }else if (isset($_GET['date'])){
                 $date = $_GET['date'];
            }
            
            $grid->model()->orderByRaw('`date` DESC,`jump` DESC,`spider` DESC');
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->column('date')->width(100);
            $grid->column('site');
            $grid->column('jump')->display(function ($val) {
                if( is_null($val)) {
                    return 0;
                } else {
                    return $val;
                }
            });
            $grid->column('spider')->display(function ($val) {
                if( is_null($val)) {
                    return 0;
                } else {
                    return $val;
                }
            });
            if($date){
                $grid->column('yesterday')->display(function () use ($date) {
                    $yesterday = date('Ymd', strtotime('-1 day', strtotime($date)));
                    $yesterdayData = SeoTotal::where('date', $yesterday)->where('site', $this->site)->first();
                    $jump = $spider = 0;
                    if(!empty($yesterdayData)){
                        $jump = $yesterdayData['jump'] ? $yesterdayData['jump'] : 0;
                        $spider = $yesterdayData['spider'] ? $yesterdayData['spider'] : 0;
                    }
                    return $jump.'/'.$spider;
                });
            }
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('date')->datetime(['format' => 'YYYYMMDD'])->default(date('Ymd'));
                $filter->like('site');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SeoTotal(), function (Show $show) {
            $show->field('id');
            $show->field('host');
            $show->field('uri');
            $show->field('hash');
            $show->field('content');
            $show->field('expired');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SeoTotal(), function (Form $form) {
            $form->display('id');
            $form->display('host');
            $form->display('uri');
            $form->display('hash');
            $form->textarea('content');
            $form->text('expired');
        });
    }
}
