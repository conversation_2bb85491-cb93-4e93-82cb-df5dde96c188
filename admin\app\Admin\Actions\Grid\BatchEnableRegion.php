<?php

namespace App\Admin\Actions\Grid;

use App\Models\SeoRegion;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchEnableRegion extends BatchAction
{
    protected $title = '批量启用';

    public function handle(Request $request)
    {
        $keys = $this->getKey();
        
        if (empty($keys)) {
            return $this->response()->error('请选择要启用的地区');
        }

        try {
            $count = SeoRegion::whereIn('id', $keys)->update(['status' => 1]);
            
            return $this->response()->success("成功启用 {$count} 个地区")->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('启用失败: ' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确定要启用选中的地区吗？', '启用后这些地区将可以被使用'];
    }
}
