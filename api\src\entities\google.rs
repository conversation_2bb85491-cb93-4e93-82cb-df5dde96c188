use ipnet::IpNet;
// use lazy_static::lazy_static;
use futures::future::join_all;
use reqwest;
use serde::Deserialize;
use std::collections::HashSet;
use std::net::IpAddr;
use std::str::FromStr;
use std::sync::RwLock;
use tokio::time::{interval, Duration};

#[derive(Deserialize, Debug)]
struct GooglebotIpRanges {
    prefixes: Vec<Prefix>,
}

#[derive(Deserialize, Debug)]
struct Prefix {
    #[serde(rename = "ipv4Prefix")]
    ipv4_prefix: Option<String>,
    #[serde(rename = "ipv6Prefix")]
    ipv6_prefix: Option<String>,
}

pub struct GoogleIPChecker {
    ip_ranges: RwLock<HashSet<IpNet>>,
}

// lazy_static! {
//     static ref IP_RANGES_CACHE: RwLock<HashSet<IpNet>> = RwLock::new(HashSet::new());
// }

impl GoogleIPChecker {
    pub fn new() -> Self {
        Self {
            ip_ranges: RwLock::new(HashSet::new()),
        }
    }
    pub async fn fetch_and_update_ip_ranges(&self) -> Result<(), Box<dyn std::error::Error + '_>> {
        let urls = vec![
            "https://developers.google.com/search/apis/ipranges/googlebot.json",
            "https://developers.google.com/search/apis/ipranges/special-crawlers.json",
            "https://developers.google.com/search/apis/ipranges/user-triggered-fetchers.json",
            "https://developers.google.com/search/apis/ipranges/user-triggered-fetchers-google.json",
        ];

        let fetch_tasks = urls.into_iter().map(|url| async move {
            let response = reqwest::get(url).await?.json::<GooglebotIpRanges>().await?;
            Result::<_, reqwest::Error>::Ok(response)
        });

        let results = join_all(fetch_tasks).await;

        let mut new_ip_ranges: HashSet<IpNet> = HashSet::new();

        for result in results {
            let response = result?;
            for prefix in response.prefixes {
                if let Some(ipv4_prefix) = prefix.ipv4_prefix {
                    new_ip_ranges.insert(IpNet::from_str(&ipv4_prefix)?);
                }
                if let Some(ipv6_prefix) = prefix.ipv6_prefix {
                    new_ip_ranges.insert(IpNet::from_str(&ipv6_prefix)?);
                }
            }
        }

        let mut ip_ranges_cache = self.ip_ranges.write()?;
        *ip_ranges_cache = new_ip_ranges;

        Ok(())
    }

    pub fn check_ip(&self, ip_str: &str) -> bool {
        match ip_str.parse::<IpAddr>() {
            Ok(ip) => {
                let ip_ranges_cache = self.ip_ranges.read().unwrap();
                for range in ip_ranges_cache.iter() {
                    if range.contains(&ip) {
                        return true;
                    }
                }
            }
            Err(_) => {} // 如果IP字符串无法解析为IP地址，则返回false
        }

        false
    }

    pub async fn start_refresh_loop(&self) {
        let mut interval_timer = interval(Duration::from_secs(60 * 60)); // 每小时刷新一次
        loop {
            interval_timer.tick().await;
            if let Err(e) = &self.fetch_and_update_ip_ranges().await {
                eprintln!("Error updating IP ranges: {}", e);
            }
        }
    }

    pub fn ip_ranges_len(&self) -> usize {
        self.ip_ranges.read().unwrap().len()
    }
}
