use rand::rngs::ThreadRng;
use rand::Rng;
use std::iter;

pub fn hour(rng: &mut ThreadRng) -> String {
    let random_hour: usize = rng.gen_range(0..24);
    format!("{:02}", random_hour)
}

pub fn minute(rng: &mut ThreadRng) -> String {
    let random_hour: usize = rng.gen_range(0..60);
    format!("{:02}", random_hour)
}

pub fn number(rng: &mut ThreadRng, n: usize) -> String {
    let rest_digits: String = iter::repeat(())
        .map(|()| rng.gen_range(0..10).to_string())
        .take(n)
        .collect();
    rest_digits
}

pub fn letters(rng: &mut ThreadRng, n: usize) -> String {
    let letters: String = iter::repeat(())
        .map(|()| rng.gen_range(b'a'..=b'z') as char) // 使用 'a' 到 'z' 作为范围
        .take(n)
        .collect();
    letters
}

// 为模板系统添加的别名函数
pub fn random_numbers(rng: &mut ThreadRng, n: usize) -> String {
    number(rng, n)
}

pub fn random_letters(rng: &mut ThreadRng, n: usize) -> String {
    letters(rng, n)
}

pub fn random_time(rng: &mut ThreadRng, _n: usize) -> String {
    // 生成随机时间格式 HH:MM
    format!("{}:{}", hour(rng), minute(rng))
}
