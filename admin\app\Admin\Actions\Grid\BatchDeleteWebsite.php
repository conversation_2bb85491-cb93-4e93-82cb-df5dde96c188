<?php

namespace App\Admin\Actions\Grid;

use App\Models\SeoSite;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;
use App\Models\Mongo;

class BatchDeleteWebsite extends BatchAction
{
    use HasPermissions;

    /**
     * @return string
     */
    public function title()
    {
        return '批量删除';
    }

    /**
     * 处理批量删除操作 - 简化版本
     */
    public function handle(Request $request)
    {
        $keys = $this->getKey();

        if (empty($keys)) {
            return $this->response()->error('请选择要删除的网站');
        }

        try {
            // 获取要删除的网站信息
            $sites = SeoSite::whereIn('id', $keys)->get();

            if ($sites->isEmpty()) {
                return $this->response()->error('未找到要删除的网站');
            }

            $deletedCount = 0;
            $totalCount = count($sites);

            // 批量删除
            foreach ($sites as $site) {
                try {
                    $host = $site->host;
                    $site->delete();
                    $deletedCount++;
                    \Log::info("批量删除网站成功: {$host}");
                } catch (\Exception $e) {
                    \Log::error("批量删除网站失败: {$site->host} - " . $e->getMessage());
                }
            }

            if ($deletedCount > 0) {
                $message = "成功删除 {$deletedCount} 个网站";
                if ($deletedCount < $totalCount) {
                    $message .= "，失败 " . ($totalCount - $deletedCount) . " 个";
                }
                return $this->response()->success($message)->refresh();
            } else {
                return $this->response()->error('删除失败');
            }

        } catch (\Exception $e) {
            \Log::error("批量删除网站操作失败: " . $e->getMessage());
            return $this->response()->error('批量删除失败: ' . $e->getMessage());
        }
    }



    /**
     * 确认对话框 - Dcat Admin官方方法
     */
    public function confirm()
    {
        return ['确定要删除选中的网站吗？', '删除后将无法恢复，同时会清理相关缓存数据！'];
    }

    /**
     * 按钮样式 - Dcat Admin官方方法
     */
    public function html()
    {
        return <<<HTML
<a class="{$this->getElementClass()}" href="javascript:void(0)">
    <i class="fa fa-trash"></i> {$this->title()}
</a>
HTML;
    }

    /**
     * 权限检查
     */
    protected function authorize($user): bool
    {
        return true; // 根据需要调整权限
    }
}
