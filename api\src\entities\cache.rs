use std::clone;

use mongodb::{
    bson::{oid::ObjectId, DateTime as BsonDateTime},
};
use serde::{Deserialize, Serialize};
// use chrono::{DateTime, Utc}; // 不再需要，使用 BSON DateTime 的内置方法

#[derive(Debug, Deserialize, Serialize, clone::Clone)]
pub struct CacheItem {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<ObjectId>,
    pub name: String,
    pub content: String,
    pub site: String,
    /// 过期时间（MongoDB Date类型，用于TTL索引自动清理）
    /// 基于 time.rs 模块计算，转换为MongoDB Date格式
    #[serde(rename = "expiredAt")]
    pub expired_at: BsonDateTime,
}

impl CacheItem {
    /// 创建新的缓存项，使用time.rs计算过期时间，转换为MongoDB Date类型
    ///
    /// 注意：这个方法使用全局时区，建议使用 new_smart() 方法
    pub fn new(name: String, content: String, site: String, expiration_days: u64) -> Self {
        // 使用time.rs获取当前时间戳，然后转换为MongoDB DateTime
        let future_timestamp = crate::util::time::timestamp() + (expiration_days * 24 * 60 * 60) as i64;
        let expired_at = BsonDateTime::from_millis(future_timestamp * 1000);

        Self {
            id: None,
            name,
            content,
            site,
            expired_at,
        }
    }

    /// 创建新的缓存项（支持地区时区）
    pub fn new_with_region_timezone(
        name: String,
        content: String,
        site: String,
        expiration_days: u64,
        region_timezone: &str
    ) -> Self {
        // 使用地区时区计算过期时间
        let future_timestamp = crate::util::time::calculate_region_cache_expiration(
            region_timezone,
            expiration_days * 24
        );
        let expired_at = BsonDateTime::from_millis(future_timestamp * 1000);

        Self {
            id: None,
            name,
            content,
            site,
            expired_at,
        }
    }

    /// 检查缓存是否已过期（基于time.rs的当前时间）
    pub fn is_expired(&self) -> bool {
        let current_timestamp = crate::util::time::timestamp();
        self.expired_at.timestamp_millis() / 1000 < current_timestamp
    }

    /// 检查缓存是否已过期（基于地区时区）
    pub fn is_expired_in_region(&self, region_timezone: &str) -> bool {
        let current_timestamp = crate::util::time::timestamp_for_region(region_timezone);
        self.expired_at.timestamp_millis() / 1000 < current_timestamp
    }

    /// 更新过期时间（基于time.rs）
    pub fn update_expiration(&mut self, expiration_days: u64) {
        let future_timestamp = crate::util::time::timestamp() + (expiration_days * 24 * 60 * 60) as i64;
        self.expired_at = BsonDateTime::from_millis(future_timestamp * 1000);
    }

    /// 更新过期时间（基于地区时区）
    pub fn update_expiration_with_region(&mut self, expiration_days: u64, region_timezone: &str) {
        let future_timestamp = crate::util::time::calculate_region_cache_expiration(
            region_timezone,
            expiration_days * 24
        );
        self.expired_at = BsonDateTime::from_millis(future_timestamp * 1000);
    }

    /// 智能创建缓存项：优先使用地区时区，回退到全局时区
    pub fn new_smart(
        name: String,
        content: String,
        site: String,
        expiration_days: u64,
        region_timezone: Option<&str>
    ) -> Self {
        // 使用智能时区选择
        let future_timestamp = crate::util::time::calculate_cache_expiration_smart(
            region_timezone,
            expiration_days * 24
        );
        let expired_at = BsonDateTime::from_millis(future_timestamp * 1000);

        Self {
            id: None,
            name,
            content,
            site,
            expired_at,
        }
    }

    /// 智能检查缓存是否过期：优先使用地区时区，回退到全局时区
    pub fn is_expired_smart(&self, region_timezone: Option<&str>) -> bool {
        let current_timestamp = crate::util::time::timestamp_smart(region_timezone);
        self.expired_at.timestamp_millis() / 1000 < current_timestamp
    }

    /// 智能更新过期时间：优先使用地区时区，回退到全局时区
    pub fn update_expiration_smart(&mut self, expiration_days: u64, region_timezone: Option<&str>) {
        let future_timestamp = crate::util::time::calculate_cache_expiration_smart(
            region_timezone,
            expiration_days * 24
        );
        self.expired_at = BsonDateTime::from_millis(future_timestamp * 1000);
    }
}
