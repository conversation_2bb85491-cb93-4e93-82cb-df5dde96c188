<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class SpiderLog extends Model
{
    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'spider_logs';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'site',
        'url',
        'user_agent',
        'ip',
        'created_at'
    ];
    
    /**
     * 模型不使用时间戳
     *
     * @var bool
     */
    public $timestamps = false;
    
    /**
     * 从Redis导入蜘蛛日志到MySQL
     * 
     * @param int $limit 最大导入记录数
     * @return int 导入的记录数
     */
    public static function importFromRedis($limit = 10000)
    {
        $count = 0;
        $imported = 0;
        
        // 获取最近7天的日期
        $dates = [];
        for ($i = 0; $i < 7; $i++) {
            $dates[] = date('Ymd', strtotime("-{$i} days"));
        }
        
        foreach ($dates as $date) {
            // 构建Redis键模式 - 使用正确的键格式
            $pattern = "total:{$date}:*:0"; // 0表示Google蜘蛛访问
            
            // 获取所有匹配的键
            $keys = Redis::keys($pattern);
            
            foreach ($keys as $key) {
                if ($count >= $limit) {
                    break 2; // 达到导入上限，跳出两层循环
                }
                
                // 解析键名中的信息
                $parts = explode(':', $key);
                if (count($parts) >= 4) {
                    $date = $parts[1] ?? '';
                    $site = $parts[2] ?? '';
                    $type = $parts[3] ?? '';
                    
                    // 获取访问次数
                    $visit_count = Redis::get($key) ?: 0;
                    
                    // 获取最后访问时间
                    $last_time_key = "lasttime:{$site}";
                    $timestamp = Redis::get($last_time_key) ?: time();
                    
                    // 准备数据
                    $data = [
                        'site' => $site,
                        'url' => '/', // 无法从Redis键中获取具体URL
                        'user_agent' => 'Googlebot', // 默认为Google蜘蛛
                        'ip' => '', // 无法从Redis键中获取IP
                        'created_at' => date('Y-m-d H:i:s', $timestamp)
                    ];
                    
                    // 检查记录是否已存在
                    $exists = self::where([
                        'site' => $data['site'],
                        'created_at' => $data['created_at']
                    ])->exists();
                    
                    if (!$exists) {
                        // 创建多条记录，基于访问次数
                        for ($i = 0; $i < $visit_count; $i++) {
                            self::create($data);
                            $imported++;
                        }
                    }
                    
                    $count++;
                }
            }
        }
        
        // 清理30天前的历史数据
        $thirtyDaysAgo = date('Y-m-d H:i:s', strtotime('-30 days'));
        self::where('created_at', '<', $thirtyDaysAgo)->delete();
        
        return $imported;
    }
} 