<?php

namespace App\Console\Commands;

use App\Models\SeoSite;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class FixNullFieldsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:null-fields';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修复数据库中字段为NULL的记录';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始修复数据库中字段为NULL的记录...');
        
        try {
            // 修复state字段为NULL的记录
            $nullStateCount = DB::table('seo_site')->whereNull('state')->update(['state' => 0]);
            $this->info("已修复 {$nullStateCount} 条state为NULL的记录");
            
            // 修复open_cache字段为NULL的记录
            $nullOpenCacheCount = DB::table('seo_site')->whereNull('open_cache')->update(['open_cache' => 0]);
            $this->info("已修复 {$nullOpenCacheCount} 条open_cache为NULL的记录");
            
            // 修复open_page字段为NULL的记录
            $nullOpenPageCount = DB::table('seo_site')->whereNull('open_page')->update(['open_page' => 0]);
            $this->info("已修复 {$nullOpenPageCount} 条open_page为NULL的记录");
            
            // 修复open_home字段为NULL的记录
            $nullOpenHomeCount = DB::table('seo_site')->whereNull('open_home')->update(['open_home' => 0]);
            $this->info("已修复 {$nullOpenHomeCount} 条open_home为NULL的记录");
            
            // 修复open_link字段为NULL的记录
            $nullOpenLinkCount = DB::table('seo_site')->whereNull('open_link')->update(['open_link' => 0]);
            $this->info("已修复 {$nullOpenLinkCount} 条open_link为NULL的记录");
            
            $this->info('修复完成！');
            return 0;
        } catch (\Exception $e) {
            $this->error('修复失败: ' . $e->getMessage());
            return 1;
        }
    }
} 