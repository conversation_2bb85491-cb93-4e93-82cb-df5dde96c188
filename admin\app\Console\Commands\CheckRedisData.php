<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Redis;

class CheckRedisData extends Command
{
    protected $signature = 'redis:check';
    protected $description = '检查Redis中的统计数据';

    public function handle()
    {
        $this->info('检查Redis中的统计数据...');
        
        // 检查total键
        $total_keys = Redis::keys("total:*");
        $this->info('找到 ' . count($total_keys) . ' 个total键:');
        
        foreach (array_slice($total_keys, 0, 10) as $key) {
            $value = Redis::get($key);
            $this->line("  {$key} = {$value}");
        }
        
        if (count($total_keys) > 10) {
            $this->line("  ... 还有 " . (count($total_keys) - 10) . " 个键");
        }
        
        // 检查lasttime键
        $lasttime_keys = Redis::keys("lasttime:*");
        $this->info('找到 ' . count($lasttime_keys) . ' 个lasttime键:');
        
        foreach (array_slice($lasttime_keys, 0, 5) as $key) {
            $value = Redis::get($key);
            $timestamp = is_numeric($value) ? date('Y-m-d H:i:s', $value) : $value;
            $this->line("  {$key} = {$timestamp}");
        }
        
        if (count($lasttime_keys) > 5) {
            $this->line("  ... 还有 " . (count($lasttime_keys) - 5) . " 个键");
        }
        
        // 检查今天的数据
        $today = date('Ymd');
        $today_keys = Redis::keys("total:{$today}:*");
        $this->info("今天({$today})的统计数据:");
        
        $spider_count = 0;
        $jump_count = 0;
        
        foreach ($today_keys as $key) {
            $parts = explode(':', $key);
            if (count($parts) >= 4) {
                $type = $parts[3];
                $value = Redis::get($key);
                
                if ($type == '0') {
                    $spider_count += $value;
                } elseif ($type == '1') {
                    $jump_count += $value;
                }
                
                $this->line("  {$key} = {$value}");
            }
        }
        
        $this->info("今天总计: 蜘蛛访问 {$spider_count} 次, 跳转 {$jump_count} 次");
        
        return 0;
    }
}
