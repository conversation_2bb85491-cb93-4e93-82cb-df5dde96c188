# ALLOWED_SPIDERS 配置说明

## 概述

`ALLOWED_SPIDERS` 配置参数用于控制哪些搜索引擎蜘蛛可以访问特定的API端点，如 `robots.txt`、`sitemap.xml` 以及影响跳转逻辑的判断。

## 配置格式

```env
ALLOWED_SPIDERS=google,baidu,bing,yandex
```

- 使用逗号分隔多个蜘蛛名称
- 不区分大小写
- 支持任意蜘蛛名称，只要在User-Agent中包含该字符串即可

## 支持的蜘蛛名称

常见的搜索引擎蜘蛛名称：

- `google` - Google搜索引擎 (Googlebot)
- `baidu` - 百度搜索引擎 (Baiduspider)
- `bing` - 微软Bing搜索引擎 (Bingbot)
- `yandex` - Yandex搜索引擎 (YandexBot)
- `sogou` - 搜狗搜索引擎
- `360spider` - 360搜索引擎
- `bytespider` - 字节跳动搜索引擎

## 影响的功能

### 1. robots.txt 访问控制

只有配置的蜘蛛才能访问 `/robots.txt` 端点，其他访问者会收到404响应。

### 2. sitemap.xml 访问控制

只有配置的蜘蛛才能访问 `/sitemap.xml` 端点，其他访问者会收到404响应。

### 3. 跳转逻辑判断

在主页跳转逻辑中，系统会检查：
- 访问者是否不是配置的蜘蛛（非蜘蛛访问者）
- 来路（Referer）是否包含配置的搜索引擎

## 默认配置

如果未设置 `ALLOWED_SPIDERS` 参数，系统默认只允许 `google`。

## 配置示例

```env
# 只允许Google
ALLOWED_SPIDERS=google

# 允许Google和百度
ALLOWED_SPIDERS=google,baidu

# 允许多个搜索引擎
ALLOWED_SPIDERS=google,baidu,bing,yandex,sogou
```

## 注意事项

1. 配置更改后需要重启API服务才能生效
2. 蜘蛛名称匹配是基于User-Agent字符串包含检查，不是精确匹配
3. 建议根据实际SEO需求配置相应的搜索引擎蜘蛛
4. 配置过多的蜘蛛可能会影响跳转逻辑的精确性

## 代码实现

配置解析在 `GlobalConfig::new()` 中进行：

```rust
allowed_spiders: parse_allowed_spiders(&env_var_default("ALLOWED_SPIDERS", "google")),
```

检查方法：

```rust
// 检查是否为允许的蜘蛛
pub fn is_allowed_spider(&self, user_agent: &str) -> bool

// 检查来路是否包含允许的搜索引擎
pub fn is_from_allowed_search_engine(&self, referer: &str) -> bool
```
