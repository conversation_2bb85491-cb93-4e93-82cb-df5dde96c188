<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SeoConfig;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SeoConfigController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SeoConfig(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('link_fixed');
            $grid->column('link_list');
            $grid->column('link_num');
            $grid->column('link_total');
            $grid->column('sitemap_num');
            $grid->column('link_site_num');
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SeoConfig(), function (Show $show) {
            $show->field('id');
            $show->field('link_fixed');
            $show->field('link_list');
            $show->field('link_num');
            $show->field('link_total');
            $show->field('sitemap_num');
            $show->field('link_site_num');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SeoConfig(), function (Form $form) {
            $form->display('id');
            $form->textarea('link_fixed');
            $form->textarea('link_list');
            $form->number('link_num');
            $form->number('link_total');
            $form->number('link_site_num');
            $form->number('sitemap_num');

        });
    }
}
