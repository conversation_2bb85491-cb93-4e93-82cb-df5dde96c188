<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

class UpdateExistingSitesRegionCode extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 1. 更新所有region_code为NULL或空字符串的记录为'default'
        DB::table('seo_site')
            ->whereNull('region_code')
            ->orWhere('region_code', '')
            ->update(['region_code' => 'default']);

        // 2. 根据域名后缀智能分配地区代码
        $domainRegionMapping = [
            '.com.br' => 'br',    // 巴西
            '.co.in' => 'in',     // 印度
            '.com.pk' => 'pk',    // 巴基斯坦
            '.co.id' => 'id',     // 印尼
            '.com.mx' => 'mx',    // 墨西哥
            '.com.ng' => 'ng',    // 尼日利亚
            '.com.bd' => 'bd',    // 孟加拉国
            '.com.ph' => 'ph',    // 菲律宾
            '.com.vn' => 'vn',    // 越南
            '.co.jp' => 'jp',     // 日本
            '.co.th' => 'th',     // 泰国
        ];

        foreach ($domainRegionMapping as $suffix => $regionCode) {
            DB::table('seo_site')
                ->where('host', 'LIKE', "%{$suffix}")
                ->where('region_code', 'default')
                ->update(['region_code' => $regionCode]);
        }

        // 3. 记录更新统计
        $totalSites = DB::table('seo_site')->count();
        $defaultSites = DB::table('seo_site')->where('region_code', 'default')->count();
        $assignedSites = $totalSites - $defaultSites;

        echo "地区代码更新完成:\n";
        echo "- 总网站数: {$totalSites}\n";
        echo "- 已分配地区: {$assignedSites}\n";
        echo "- 默认地区: {$defaultSites}\n";
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 回滚操作：将所有非NULL的region_code设置为NULL
        // 注意：这会丢失地区分配信息
        DB::table('seo_site')->update(['region_code' => null]);
    }
}
