use crate::entities::{RequestInfo, SiteConfigs, AppState};
use actix_web::{get, web::Data, HttpRequest, HttpResponse, Responder};
use std::fmt::Write;

#[get("/robots.txt")]
pub async fn generate_robots(
    req: HttpRequest,
    _app_state: Data<AppState>,
    domains: Data<SiteConfigs>
) -> impl Responder {
    let request_info = match RequestInfo::by_web(&req) {
        Some(r) => r,
        None => return HttpResponse::InternalServerError().finish(),
    };

    // 只允许Google爬虫访问
    if !request_info.user_agent.to_lowercase().contains("google") {
        return HttpResponse::NotFound().finish();
    }

    // 获取网站配置
    let site_config = match domains.get(&request_info.host).await {
        Some(config) => config,
        None => return HttpResponse::NotFound().finish(),
    };

    // 使用网站配置中的协议设置，而不是请求协议
    let protocol = if site_config.https { "https" } else { "http" };

    let mut buffer = String::new();
    writeln!(
        buffer,
        r#"User-agent: *
Disallow:

Sitemap: {}://{}{}/sitemap.xml"#,
        protocol,
        &request_info.host,
        &request_info.port
    ).unwrap();

    HttpResponse::Ok()
        .append_header(("Content-Type", "text/plain"))
        .body(buffer)
}
