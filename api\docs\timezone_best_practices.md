# 时区处理最佳实践指南

## 🎯 **核心原则**

### **业务时区 vs 用户时区**
- ✅ **使用业务时区**：网站缓存、爬行记录、业务逻辑都使用网站所属地区时区
- ❌ **不使用用户时区**：不根据访问用户的地理位置调整时间

### **示例场景**
```
美国用户访问巴西站点 example.com.br：
- 网站地区：巴西 (br)
- 网站时区：America/Sao_Paulo
- 用户地区：美国 (us)  
- 用户时区：America/New_York

✅ 正确：所有时间显示和计算使用巴西时区
❌ 错误：根据用户IP显示美国时区
```

## 📋 **时区使用场景详解**

### **1. 业务缓存 (Redis/MongoDB)**

#### **Redis缓存时间戳**
```rust
// ✅ 正确：使用网站地区时区
let site_config = get_site_config("example.com.br").await;
let region_timezone = "America/Sao_Paulo";

// 蜘蛛爬行时间记录
let crawl_timestamp = crate::util::time::timestamp_for_region(region_timezone);
redis.set("lasttime:example.com.br", crawl_timestamp).await;

// 场景：巴西时间 2024-07-06 08:00:00 爬行
// 存储：1720263600 (对应UTC: 2024-07-06 11:00:00)
```

#### **MongoDB缓存过期**
```rust
// ✅ 正确：使用网站地区时区计算过期时间
let cache_item = CacheItem::new_with_region_timezone(
    cache_name,
    content,
    site,
    24, // 24小时后过期
    "America/Sao_Paulo"
);

// 场景：巴西时间 08:00 创建，巴西时间次日 08:00 过期
// 创建：2024-07-06 08:00:00 (Brazil) = 2024-07-06 11:00:00 (UTC)
// 过期：2024-07-07 08:00:00 (Brazil) = 2024-07-07 11:00:00 (UTC)
```

### **2. 用户请求处理**

```rust
// ✅ 正确：返回网站地区时间
let site_region = "br";
let site_timezone = "America/Sao_Paulo";
let local_time = crate::util::time::format_region_time(
    site_timezone, 
    "%m/%d %H:%M:%S"
);

// 响应：巴西时间：07/06 08:00:04
// 无论用户在哪里，都显示巴西时间
```

### **3. 系统日志**

```rust
// ✅ 正确：使用全局时区便于运维
println!("系统启动时间: {}", crate::util::time::format_now("%Y-%m-%d %H:%M:%S"));
// 输出：系统启动时间: 2024-07-06 11:00:00 (UTC)
```

### **4. 数据库记录**

#### **MySQL业务记录**
```rust
// ✅ 正确：使用网站地区时区
let crawl_time = crate::util::time::now_for_region("America/Sao_Paulo");
sqlx::query!(
    "INSERT INTO crawl_log (site, crawl_time, region_timezone) VALUES (?, ?, ?)",
    site,
    crawl_time.naive_utc(), // 存储为UTC但语义是巴西时间
    "America/Sao_Paulo"
).execute(&pool).await;
```

#### **系统表记录**
```rust
// ✅ 正确：系统表使用UTC
let system_time = crate::util::time::now_utc();
sqlx::query!(
    "INSERT INTO system_log (event, created_at) VALUES (?, ?)",
    event,
    system_time.naive_utc()
).execute(&pool).await;
```

## 🔍 **时间戳存储和读取策略**

### **存储策略**
```rust
// 所有时间戳都存储为UTC时间戳（Unix timestamp）
// 但计算基于业务相关的地区时区

// 示例：巴西站点在巴西时间08:00被爬行
let brazil_time = "2024-07-06 08:00:00 (America/Sao_Paulo)";
let utc_timestamp = 1720263600; // 对应UTC: 2024-07-06 11:00:00

// Redis存储
redis.set("lasttime:example.com.br", utc_timestamp).await;

// MongoDB存储  
let expired_at = BsonDateTime::from_millis(utc_timestamp * 1000);
```

### **读取策略**
```rust
// 读取时转换回业务地区时区显示
let stored_timestamp = redis.get("lasttime:example.com.br").await;
let brazil_time = crate::util::time::utc_to_region_timezone(
    DateTime::from_timestamp(stored_timestamp, 0).unwrap(),
    "America/Sao_Paulo"
);

// 显示：最后爬行时间：07/06 08:00:04 (巴西时间)
```

## ⚠️ **常见错误和解决方案**

### **错误1：混用用户时区和业务时区**
```rust
// ❌ 错误
let user_ip = get_user_ip(&request);
let user_timezone = geoip_to_timezone(user_ip); // 用户时区
let display_time = format_time_in_timezone(user_timezone); // 错误！

// ✅ 正确
let site_config = get_site_config(&domain).await;
let site_timezone = get_site_timezone(&site_config.region_code).await;
let display_time = format_time_in_timezone(site_timezone); // 正确！
```

### **错误2：缓存时区不一致**
```rust
// ❌ 错误：创建和检查使用不同时区
let cache_item = CacheItem::new(...); // 使用UTC
let is_expired = cache_item.is_expired_in_region("America/Sao_Paulo"); // 使用巴西时区

// ✅ 正确：创建和检查使用相同时区
let cache_item = CacheItem::new_with_region_timezone(..., "America/Sao_Paulo");
let is_expired = cache_item.is_expired_in_region("America/Sao_Paulo");
```

### **错误3：统计时间混乱**
```rust
// ❌ 错误：爬行记录使用UTC，显示使用地区时区
redis.set("lasttime:site", utc_timestamp).await; // UTC时间
let display = format_brazil_time(utc_timestamp); // 显示会错乱

// ✅ 正确：统一使用地区时区语义
let brazil_timestamp = timestamp_for_region("America/Sao_Paulo");
redis.set("lasttime:site", brazil_timestamp).await; // 巴西时间语义
let display = format_brazil_time(brazil_timestamp); // 显示正确
```

## 🛠️ **实用工具函数**

### **时区转换工具**
```rust
// 获取网站地区时区
pub async fn get_site_timezone(app_state: &AppState, domain: &str) -> String {
    let site_config = get_site_config(domain).await;
    if let Some(region_code) = &site_config.region_code {
        app_state.region_timezone_context
            .get_region_timezone(region_code).await
    } else {
        crate::util::time::get_timezone() // 全局默认时区
    }
}

// 智能时间格式化
pub async fn format_site_time(app_state: &AppState, domain: &str) -> String {
    let timezone = get_site_timezone(app_state, domain).await;
    crate::util::time::format_region_time(&timezone, "%m/%d %H:%M:%S")
}
```

## 📊 **监控和调试**

### **时区一致性检查**
```rust
// 检查缓存时区一致性
pub async fn check_timezone_consistency(site: &str) -> bool {
    let redis_timestamp = redis.get(&format!("lasttime:{}", site)).await;
    let mongo_cache = mongo.find_cache(site).await;
    
    // 检查时间戳是否在合理范围内
    let time_diff = (redis_timestamp - mongo_cache.created_at).abs();
    time_diff < 3600 // 1小时内认为一致
}
```

### **时区调试信息**
```rust
// 添加调试日志
println!("站点: {}, 地区: {}, 时区: {}, 当前时间: {}", 
    site, region_code, timezone, current_time);
```

## 🎯 **总结**

1. **业务时区优先**：所有业务逻辑使用网站所属地区时区
2. **存储UTC语义**：时间戳存储为UTC但计算基于业务时区
3. **显示地区时间**：用户看到的是网站地区时间，不是用户时区
4. **系统操作UTC**：日志、监控等系统操作使用UTC或全局时区
5. **一致性原则**：同一业务流程中的时区选择保持一致
