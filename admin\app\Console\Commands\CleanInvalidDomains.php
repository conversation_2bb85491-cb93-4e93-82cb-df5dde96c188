<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\SeoSite;

class CleanInvalidDomains extends Command
{
    protected $signature = 'domains:clean-invalid {--dry-run : 只显示要删除的域名，不实际删除}';
    protected $description = '清理无效的域名记录';

    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('开始检查无效域名...');
        
        // 获取所有域名
        $sites = SeoSite::all();
        $this->info('总共找到 ' . count($sites) . ' 个域名记录');
        
        $invalidDomains = [];
        
        foreach ($sites as $site) {
            $host = $site->host;
            
            // 检查是否为无效域名
            if ($this->isInvalidDomain($host)) {
                $invalidDomains[] = [
                    'id' => $site->id,
                    'host' => $host,
                    'created_time' => date('Y-m-d H:i:s', $site->last_sync ?? 0)
                ];
            }
        }
        
        if (empty($invalidDomains)) {
            $this->info('✅ 没有发现无效域名');
            return 0;
        }
        
        $this->warn('发现 ' . count($invalidDomains) . ' 个无效域名:');
        
        // 显示无效域名列表
        $this->table(['ID', '域名', '创建时间'], $invalidDomains);
        
        if ($dryRun) {
            $this->info('--dry-run 模式，不会实际删除数据');
            return 0;
        }
        
        // 确认删除
        if (!$this->confirm('确定要删除这些无效域名吗？')) {
            $this->info('操作已取消');
            return 0;
        }
        
        // 执行删除
        $deletedCount = 0;
        foreach ($invalidDomains as $domain) {
            try {
                SeoSite::where('id', $domain['id'])->delete();
                $this->info('删除: ' . $domain['host']);
                $deletedCount++;
            } catch (\Exception $e) {
                $this->error('删除失败 ' . $domain['host'] . ': ' . $e->getMessage());
            }
        }
        
        $this->info("清理完成，删除了 $deletedCount 个无效域名");
        return 0;
    }
    
    /**
     * 检查域名是否无效
     */
    private function isInvalidDomain($host)
    {
        // 空域名
        if (empty($host)) {
            return true;
        }
        
        // 单个词（没有点号），除了localhost
        if (!str_contains($host, '.') && $host !== 'localhost') {
            return true;
        }
        
        // 包含无效字符（允许字母、数字、点号、连字符、下划线）
        if (preg_match('/[^a-zA-Z0-9._-]/', $host)) {
            return true;
        }
        
        // 以点开头或结尾
        if (str_starts_with($host, '.') || str_ends_with($host, '.')) {
            return true;
        }
        
        // 连续的点
        if (str_contains($host, '..')) {
            return true;
        }
        
        // 过长的域名
        if (strlen($host) > 253) {
            return true;
        }
        
        // 检查是否是明显的子域名片段
        $invalidPatterns = [
            'apps', 'intra', 'spool', 'corporate', 'web', 'gamma',
            'release', 'pre', 'prerelease', 'local', 'int', 'dev',
            'test', 'staging', 'admin', 'api', 'www', 'mail', 'ftp',
            'console', 'development'
        ];
        
        if (in_array($host, $invalidPatterns)) {
            return true;
        }
        
        // 检查是否包含错误的域名格式（如包含多个域名）
        if (preg_match('/\.(com|net|org|gov|edu|mil|int|co|pk|th|br|in|us).*\.(com|net|org|gov|edu|mil|int|co|pk|th|br|in|us)/', $host)) {
            return true;
        }
        
        // 🔧 检查重复域名模式（如 www.google.co.th.www.google.co.th）
        if ($this->hasDuplicateDomainPattern($host)) {
            return true;
        }

        // 🔧 检查是否包含多个顶级域名（如 example.com.example.org）
        if ($this->hasMultipleTlds($host)) {
            return true;
        }

        return false;
    }

    /**
     * 检查是否有重复的域名模式
     */
    private function hasDuplicateDomainPattern($host)
    {
        $parts = explode('.', $host);
        $len = count($parts);

        // 如果部分数量是偶数，检查前半部分是否等于后半部分
        if ($len >= 4 && $len % 2 == 0) {
            $mid = $len / 2;
            $firstHalf = array_slice($parts, 0, $mid);
            $secondHalf = array_slice($parts, $mid);

            if ($firstHalf === $secondHalf) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查是否包含多个顶级域名
     */
    private function hasMultipleTlds($host)
    {
        $domainStr = strtolower($host);

        // 🔧 只检查核心地区的复合TLD（性能优化）
        $validCompoundTlds = [
            // 核心地区 - 巴基斯坦
            '.gov.pk', '.edu.pk', '.org.pk', '.com.pk',

            // 核心地区 - 印度
            '.gov.in', '.edu.in', '.org.in', '.co.in',

            // 核心地区 - 泰国
            '.gov.th', '.edu.th', '.org.th', '.co.th',

            // 核心地区 - 巴西
            '.gov.br', '.edu.br', '.org.br', '.com.br',

            // 常见复合TLD
            '.gov.us', '.edu.us', '.co.uk', '.gov.uk',
            '.com.au', '.gov.au', '.co.za', '.gov.za',
        ];

        // 如果包含合法的复合TLD，不认为是多个TLD
        foreach ($validCompoundTlds as $compoundTld) {
            if (substr($domainStr, -strlen($compoundTld)) === $compoundTld) {
                return false;
            }
        }

        // 检查是否有真正的多个独立TLD（如 example.com.another.org）
        $tldPatterns = ['.com.', '.org.', '.net.', '.info.', '.biz.'];

        foreach ($tldPatterns as $pattern) {
            if (strpos($domainStr, $pattern) !== false) {
                // 检查这个TLD后面是否还有其他TLD
                $pos = strpos($domainStr, $pattern);
                if ($pos !== false) {
                    $afterTld = $pos + strlen($pattern);
                    if ($afterTld < strlen($domainStr)) {
                        $remaining = substr($domainStr, $afterTld);
                        // 如果后面还有明显的TLD模式，才认为是多个TLD
                        foreach ($tldPatterns as $checkPattern) {
                            $checkTld = substr($checkPattern, 1, -1); // 移除前后的点
                            if (strpos($remaining, $checkTld) !== false) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        return false;
    }
}
