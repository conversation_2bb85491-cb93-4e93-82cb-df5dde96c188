use crate::entities::{AppState, CacheItem, RequestInfo, SiteConfig, SiteConfigs, Region};
use crate::util::{template, time};
use crate::{/* GOOGLEIP, */ TEMPLATE_CONFIG, GEOIP_CHECKER};
use crate::util::string::url::replace_with_resource;
use actix_web::{get, http::header, web, HttpResponse, Responder};
use mongodb::bson::{self, doc};
use mongodb::options::UpdateOptions;
use rand::seq::SliceRandom;
// use rand::Rng;
use serde::Deserialize;
use std::collections::HashSet;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use once_cell::sync::Lazy;
use std::time::{Duration, Instant};
use tokio::time::sleep;
use dashmap::DashMap;
use sqlx::Row;
const HTML_HEADER: (header::HeaderName, &str) = (header::CONTENT_TYPE, "text/html; charset=utf-8");

/// 验证和规范化region_code
fn validate_and_normalize_region_code(region_param: Option<&String>) -> String {
    match region_param {
        Some(region_code) if !region_code.is_empty() => {
            // 验证region_code格式（只允许字母和数字，长度2-10）
            let normalized = region_code.to_lowercase();
            if normalized.chars().all(|c| c.is_alphanumeric()) && normalized.len() >= 2 && normalized.len() <= 10 {
                normalized
            } else {
                "other".to_string()
            }
        },
        _ => {
            // 没有guo参数或为空，设置为"other"分类
            "other".to_string()
        }
    }
}

// 改用tokio::sync::RwLock替代标准库的Mutex
static SYNCED_DOMAINS: Lazy<RwLock<HashSet<String>>> = Lazy::new(|| RwLock::new(HashSet::new()));

// 为重试添加的常量
const MAX_SYNC_RETRIES: u8 = 3;
const RETRY_DELAY_MS: u64 = 500;

// 全局批量处理队列
static DOMAIN_BATCH_QUEUE: Lazy<Arc<Mutex<DomainBatchQueue>>> = 
    Lazy::new(|| Arc::new(Mutex::new(DomainBatchQueue::new())));

// 全局域名同步时间记录，用于限制同一域名的同步频率
static DOMAIN_SYNC_TIMES: Lazy<Arc<RwLock<DashMap<String, Instant>>>> = 
    Lazy::new(|| Arc::new(RwLock::new(DashMap::new())));

// 域名同步的最小间隔时间（60分钟）
const MIN_SYNC_INTERVAL: Duration = Duration::from_secs(60 * 60);

// 在启动时从数据库加载同步记录到内存中
pub async fn load_sync_records(mysql_pool: &sqlx::MySqlPool) {
    // println!("从数据库加载域名同步记录...");
    
    // 检查数据库中是否存在last_sync字段，如果不存在则添加
    let check_column = sqlx::query("SELECT COUNT(*) as count FROM information_schema.COLUMNS WHERE TABLE_NAME = 'seo_site' AND COLUMN_NAME = 'last_sync'")
        .fetch_one(mysql_pool)
        .await;
    
    match check_column {
        Ok(row) => {
            let count: i64 = row.try_get("count").unwrap_or(0);
            if count == 0 {
                println!("添加last_sync字段到数据库...");
                // 添加last_sync字段，默认为0
                let result = sqlx::query("ALTER TABLE seo_site ADD COLUMN last_sync BIGINT NOT NULL DEFAULT 0")
                    .execute(mysql_pool)
                    .await;
                
                if let Err(e) = result {
                    eprintln!("添加last_sync字段失败: {}", e);
                    return;
                }
            }
        },
        Err(e) => {
            eprintln!("检查last_sync字段失败: {}", e);
            return;
        }
    }
    
    // 从数据库中加载所有具有非零last_sync值的域名
    let records = sqlx::query("SELECT host, last_sync FROM seo_site WHERE last_sync > 0")
        .fetch_all(mysql_pool)
        .await;
    
    match records {
        Ok(rows) => {
            let now = time::timestamp();
            
            let sync_times = DOMAIN_SYNC_TIMES.write().await;
            
            for row in rows {
                let host: String = row.try_get("host").unwrap_or_default();
                let last_sync: i64 = row.try_get("last_sync").unwrap_or(0);
                
                // 计算上次同步到现在的时间差（秒）
                let elapsed_secs = now - last_sync;
                
                // 如果上次同步时间在有效期内，添加到内存记录中
                if elapsed_secs < MIN_SYNC_INTERVAL.as_secs() as i64 {
                    // 创建一个虚拟的Instant，表示已经过去了elapsed_secs
                    let adjusted_time = Instant::now() - Duration::from_secs(elapsed_secs as u64);
                    sync_times.insert(host.clone(), adjusted_time);
                }
            }
            
            // println!("成功加载 {} 个域名的同步记录到内存", sync_times.len());
        },
        Err(e) => {
            eprintln!("从数据库加载同步记录失败: {}", e);
        }
    }
}

// 域名批量处理队列
struct DomainBatchQueue {
    // 待处理的域名配置
    domains: Vec<(String, String, SiteConfig, bool)>, // (host, protocol, template, is_template)
    // 上次处理时间
    last_process_time: Instant,
    // 添加失败队列，用于重试
    failed_domains: Vec<(String, String, SiteConfig, bool, u8)>, // (host, protocol, template, is_template, retry_count)
}

impl DomainBatchQueue {
    fn new() -> Self {
        Self {
            domains: Vec::new(),
            last_process_time: Instant::now(),
            failed_domains: Vec::new(),
        }
    }
    
    // 添加域名配置到批处理队列
    // 新增is_template参数表示是否使用模板配置(dll=1)
    fn add(&mut self, host: String, protocol: String, template: SiteConfig, is_template: bool) {
        self.domains.push((host, protocol, template, is_template));
    }
    
    // 添加失败的域名到重试队列
    fn add_failed(&mut self, host: String, protocol: String, template: SiteConfig, is_template: bool, retry_count: u8) {
        if retry_count < MAX_SYNC_RETRIES {
            self.failed_domains.push((host, protocol, template, is_template, retry_count + 1));
        } else {
            eprintln!("域名 {} 处理失败，已达到最大重试次数", host);
        }
    }
    
    fn len(&self) -> usize {
        self.domains.len()
    }
    
    fn should_process(&self) -> bool {
        // 降低批处理阈值，只要有域名就应该处理
        self.domains.len() > 0 || 
        (!self.domains.is_empty() && self.last_process_time.elapsed() >= Duration::from_secs(30)) || // 降低时间间隔
        !self.failed_domains.is_empty() // 有失败的域名也应该进行处理
    }
    
    // 获取并清空队列
    fn take_all(&mut self) -> (Vec<(String, String, SiteConfig, bool)>, Vec<(String, String, SiteConfig, bool, u8)>) {
        let domains = std::mem::take(&mut self.domains);
        let failed_domains = std::mem::take(&mut self.failed_domains);
        self.last_process_time = Instant::now();
        (domains, failed_domains)
    }
}

// 启动批量处理后台任务
pub async fn start_batch_processor(app_state: web::Data<AppState>, domains: web::Data<SiteConfigs>) {
    let app_state_clone = app_state.clone();
    let domains_clone = domains.clone();
    
    // 启动清理过期同步记录的定时任务
    tokio::spawn(async move {
        loop {
            // 每小时运行一次清理
            sleep(Duration::from_secs(3600)).await;
            
            let now = Instant::now();
            let sync_times = DOMAIN_SYNC_TIMES.write().await;
            let mut removed_count = 0;
            
            // 删除超过同步间隔时间的记录
            sync_times.retain(|_, &mut last_sync| {
                let keep = now.duration_since(last_sync) < MIN_SYNC_INTERVAL;
                if !keep {
                    removed_count += 1;
                }
                keep
            });
            
            // 清理完成，不输出日志避免频繁输出
        }
    });
    
    let app_state_clone2 = app_state_clone.clone();
    let domains_clone2 = domains_clone.clone();
    
    tokio::spawn(async move {
        loop {
            // 等待10秒检查一次
            sleep(Duration::from_secs(10)).await;
            
            // 检查是否需要处理队列
            let should_process;
            let (batch_domains, failed_domains);
            {
                let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                should_process = queue.should_process();
            if should_process {
                    (batch_domains, failed_domains) = queue.take_all();
                } else {
                    continue;
                }
                }
                
                // 处理批量域名
                if !batch_domains.is_empty() {
                    process_batch_domains(batch_domains, app_state_clone2.clone(), domains_clone2.clone()).await;
                }
            
            // 处理失败的域名（重试机制）
            if !failed_domains.is_empty() {
                // 添加延迟以避免立即重试
                sleep(Duration::from_millis(RETRY_DELAY_MS)).await;
                process_failed_domains(failed_domains, app_state_clone2.clone(), domains_clone2.clone()).await;
            }
        }
    });
}

// 验证域名格式是否有效
fn is_valid_domain(host: &str) -> bool {
    // 空域名
    if host.is_empty() {
        return false;
    }

    // 单个词（没有点号），除了localhost
    if !host.contains('.') && host != "localhost" {
        return false;
    }

    // 包含无效字符（允许字母、数字、点号、连字符、下划线）
    if host.chars().any(|c| !c.is_ascii_alphanumeric() && c != '.' && c != '-' && c != '_') {
        return false;
    }

    // 以点开头或结尾
    if host.starts_with('.') || host.ends_with('.') {
        return false;
    }

    // 连续的点
    if host.contains("..") {
        return false;
    }

    // 过长的域名
    if host.len() > 253 {
        return false;
    }

    // 检查是否是明显的内部服务名（只检查完全匹配的单词）
    let invalid_patterns = [
        "apps", "intra", "spool", "corporate", "web", "gamma",
        "release", "pre", "prerelease", "local", "int", "dev",
        "test", "staging", "admin", "api", "www", "mail", "ftp",
        "console", "development"
    ];

    if invalid_patterns.contains(&host) {
        return false;
    }

    // 基本的域名格式检查：至少包含一个点，且各部分不为空
    let parts: Vec<&str> = host.split('.').collect();
    if parts.len() < 2 {
        return false;
    }

    // 检查每个部分是否有效
    for part in parts {
        if part.is_empty() || part.len() > 63 {
            return false;
        }
        // 每个部分不能以连字符开头或结尾（但可以包含连字符和下划线）
        if part.starts_with('-') || part.ends_with('-') {
            return false;
        }
        // 检查是否只包含有效字符
        if part.chars().any(|c| !c.is_ascii_alphanumeric() && c != '-' && c != '_') {
            return false;
        }
    }

    // 🔧 检查重复域名模式（如 www.google.co.th.www.google.co.th）
    if has_duplicate_domain_pattern(host) {
        return false;
    }

    // 🔧 检查是否包含多个顶级域名（如 example.com.example.org）
    if has_multiple_tlds(host) {
        return false;
    }

    true
}

// 检查是否有重复的域名模式
fn has_duplicate_domain_pattern(host: &str) -> bool {
    let parts: Vec<&str> = host.split('.').collect();
    let len = parts.len();

    // 如果部分数量是偶数，检查前半部分是否等于后半部分
    if len >= 4 && len % 2 == 0 {
        let mid = len / 2;
        let first_half = &parts[0..mid];
        let second_half = &parts[mid..];

        if first_half == second_half {
            return true;
        }
    }

    false
}

// 检查是否包含多个顶级域名
fn has_multiple_tlds(host: &str) -> bool {
    let domain_str = host.to_lowercase();

    // 🔧 只检查核心地区的复合TLD（性能优化）
    let valid_compound_tlds = [
        // 核心地区 - 巴基斯坦
        ".gov.pk", ".edu.pk", ".org.pk", ".com.pk",

        // 核心地区 - 印度
        ".gov.in", ".edu.in", ".org.in", ".co.in",

        // 核心地区 - 泰国
        ".gov.th", ".edu.th", ".org.th", ".co.th",

        // 核心地区 - 巴西
        ".gov.br", ".edu.br", ".org.br", ".com.br",

        // 常见复合TLD
        ".gov.us", ".edu.us", ".co.uk", ".gov.uk",
        ".com.au", ".gov.au", ".co.za", ".gov.za",
    ];

    // 如果包含合法的复合TLD，不认为是多个TLD
    for compound_tld in valid_compound_tlds {
        if domain_str.ends_with(compound_tld) {
            return false;
        }
    }

    // 检查是否有真正的多个独立TLD（如 example.com.another.org）
    let tld_patterns = [".com.", ".org.", ".net.", ".info.", ".biz."];

    for pattern in tld_patterns {
        if domain_str.contains(pattern) {
            // 检查这个TLD后面是否还有其他TLD
            if let Some(pos) = domain_str.find(pattern) {
                let after_tld = pos + pattern.len();
                if after_tld < domain_str.len() {
                    let remaining = &domain_str[after_tld..];
                    // 如果后面还有明显的TLD模式，才认为是多个TLD
                    for check_pattern in tld_patterns {
                        if remaining.contains(&check_pattern[1..check_pattern.len()-1]) {
                            return true;
                        }
                    }
                }
            }
        }
    }

    false
}

// 处理批量域名
async fn process_batch_domains(
    batch_domains: Vec<(String, String, SiteConfig, bool)>,
    app_state: web::Data<AppState>,
    domains: web::Data<SiteConfigs>
) {
    if batch_domains.is_empty() {
        return;
    }
    
    // 批量处理域名配置
    
    // 获取MySQL连接
    let mysql_pool = &app_state.mysql;
    
    // 批量处理域名配置
    for (host, protocol, template, is_template) in batch_domains {
        // 🔧 验证域名格式
        if !is_valid_domain(&host) {
            eprintln!("跳过无效域名: {}", host);
            continue;
        }

        // 简化日志，只保留必要信息
        // println!("开始处理域名: {}, 协议: {}, 是否模板: {}", host, protocol, is_template);
        
        // 检查此域名是否在限制时间内已经同步过
        let should_sync = {
            // 获取此域名是否最近同步过的标志
            let is_recently_synced = {
                let sync_times = DOMAIN_SYNC_TIMES.read().await;
                // 使用map_or避免直接返回引用
                sync_times.get(&host).map_or(false, |time| time.elapsed() < MIN_SYNC_INTERVAL)
            };

            if is_recently_synced {
                false
            } else {
                true
            }
        };
        
        if !should_sync {
            continue;
        }
        
        // 检查内存中是否已有该域名的配置
        let _domain_exists_in_memory = domains.get(&host).await.is_some();
        // println!("域名 {} 在内存中 {}", host, if _domain_exists_in_memory { "已存在" } else { "不存在" });
        
        // 在执行SQL前先查询数据库中是否已存在该域名
        let domain_exists_in_db = match sqlx::query("SELECT COUNT(*) as count FROM seo_site WHERE host = ?")
            .bind(&host)
            .fetch_one(mysql_pool)
            .await {
                Ok(row) => {
                    let count: i64 = row.try_get("count").unwrap_or(0);
                    count > 0
                },
                Err(_) => false
            };
        
        let result = if domain_exists_in_db {
            // 域名在数据库中已存在，执行UPDATE操作
            // 只有当is_template为true时才设置state=1，并包含region_code更新
            let sql = if is_template {
                "UPDATE seo_site SET link_rules = ?, open_cache = ?, open_page = ?, open_home = ?, open_link = ?, https = ?, state = 1, last_sync = ?, region_code = ? WHERE host = ?"
            } else {
                "UPDATE seo_site SET link_rules = ?, open_cache = ?, open_page = ?, open_home = ?, open_link = ?, https = ?, last_sync = ?, region_code = ? WHERE host = ?"
            };

            // 确定HTTPS值
            let ishttps = if protocol == "https" { 1 } else { 0 };

            // 获取当前Unix时间戳
            let now = time::timestamp();

            // 获取region_code
            let region_code = if is_template {
                template.region_code.as_str()
            } else {
                "default"
            };
            
            // 简化日志输出
            // println!("准备执行SQL: {}", sql);
            // println!("参数: link_rules={}, cache={}, page={}, home={}, link={}, https={}, last_sync={}, host={}", 
            //     template.generate_rules.join("\\n"), 
            //     template.cache, 
            //     template.page, 
            //     template.home, 
            //     template.link, 
            //     ishttps,
            //     now,
            //     host);
            
            sqlx::query(sql)
                .bind(template.generate_rules.join("\n"))
                .bind(template.cache as i8)
                .bind(template.page as i8)
                .bind(template.home as i8)
                .bind(template.link as i8)
                .bind(ishttps)
                .bind(now)
                .bind(region_code)
                .bind(&host)
                .execute(mysql_pool)
                .await
        } else {
            // 域名在数据库中不存在，执行INSERT操作
            // 检查域名是否来自模板配置（根据dll参数判断，而不是state字段）
            let state_value = if is_template { 1 } else { 0 };
            
            // 确定HTTPS值
            let ishttps = if protocol == "https" { 1 } else { 0 };
            
            // 获取当前Unix时间戳
            let now = time::timestamp();
            
            // 根据是否是模板配置，选择不同的SQL语句
            if is_template {
                // ✅ 修复：使用模板配置的完整字段，包括jump_rules
                let sql = "INSERT INTO seo_site (host, https, link_rules, jump_rules, open_cache, open_page, open_home, open_link, state, last_sync, region_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                // 获取模板的region_code，如果没有则使用默认值
                let region_code = template.region_code.as_str();

                // 模板的跳转规则（目前为空，但保持字段完整性）
                let jump_rules = ""; // 模板中jump_rules目前为空

                sqlx::query(sql)
                    .bind(&host)
                    .bind(ishttps)  // ✅ 修复：HTTPS状态正确传递
                    .bind(template.generate_rules.join("\n"))
                    .bind(jump_rules) // ✅ 修复：添加jump_rules字段
                    .bind(template.cache as i8)
                    .bind(template.page as i8)
                    .bind(template.home as i8)
                    .bind(template.link as i8)
                    .bind(state_value)
                    .bind(now)
                    .bind(region_code)
                    .execute(mysql_pool)
                    .await
            } else {
                // ✅ 修复：包含所有必要字段，包括link_rules和jump_rules
                let sql = "INSERT INTO seo_site (host, https, state, open_cache, open_page, open_home, open_link, link_rules, jump_rules, last_sync, region_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                // 非模板配置使用默认地区代码
                let default_region_code = "default";

                // ✅ 修复：link_rules保持为空，不使用模板规则
                let default_link_rules = ""; // link_rules默认为空
                let default_jump_rules = ""; // 跳转规则默认为空

                let result = sqlx::query(sql)
                    .bind(&host)
                    .bind(ishttps)  // ✅ HTTPS状态正确传递
                    .bind(0) // 确保非模板网站的state值为0（未通过状态）
                    .bind(0) // 设置open_cache默认值为0（关闭缓存）
                    .bind(0) // 设置open_page默认值为0（关闭页面）
                    .bind(0) // 设置open_home默认值为0（关闭首页）
                    .bind(0) // 设置open_link默认值为0（关闭轮链）
                    .bind(default_link_rules) // ✅ 添加link_rules字段
                    .bind(default_jump_rules) // ✅ 添加jump_rules字段
                    .bind(now)
                    .bind(default_region_code)
                    .execute(mysql_pool)
                    .await;

                // 记录域名插入结果
                match &result {
                    Ok(_) => {

                        // ✅ 重要修复：新域名插入成功后，立即添加到内存中
                        let ishttps = if protocol == "https" { 1 } else { 0 };

                        // ✅ 修复：新域名不使用模板规则，保持为空
                        let uri_rules = Vec::new();  // 空的正则表达式向量
                        let jump_rules = Vec::new(); // 空的正则表达式向量

                        let new_config = SiteConfig {
                            domain: host.clone(),
                            https: ishttps == 1,  // ✅ HTTPS状态正确设置
                            state: false,         // 新域名默认未通过
                            home: false,          // 默认关闭首页
                            link: true,           // ✅ 修复：默认开启轮链（与数据库一致）
                            page: true,           // ✅ 修复：默认开启页面（与数据库一致）
                            cache: true,          // ✅ 修复：默认开启缓存（与数据库一致）
                            region_code: default_region_code.to_string(),
                            generate_rules: Vec::new(), // ✅ 修复：generate_rules保持为空
                            uri_rules,            // 空的正则表达式向量
                            jump_rules,           // 空的正则表达式向量
                        };

                        // 立即添加到内存中
                        domains.update(&host, new_config).await;
                    },
                    Err(_) => {}
                }

                result
            }
        };
                
        match result {
            Ok(_) => {
                // 成功后添加到已同步域名集合并记录同步时间
                {
                    let mut synced_domains = SYNCED_DOMAINS.write().await;
                    synced_domains.insert(host.clone());

                    // 记录域名同步时间
                    let sync_times = DOMAIN_SYNC_TIMES.write().await;
                    sync_times.insert(host.clone(), Instant::now());
                } // 释放锁

                // ✅ 重要修复：同步更新内存中的配置
                let ishttps = if protocol == "https" { 1 } else { 0 };
                let mut updated_config = template.clone();
                updated_config.https = ishttps == 1;
                updated_config.domain = host.clone();

                // 更新内存中的域名配置
                domains.update(&host, updated_config).await;

                // println!("域名 {} 配置同步成功，HTTPS状态: {}", host, ishttps == 1);
                
                // 同步成功后再次查询数据库确认记录已存在
                // 简化日志，移除这部分确认步骤
                // match sqlx::query("SELECT * FROM seo_site WHERE host = ?")
                //     .bind(&host)
                //     .fetch_one(mysql_pool)
                //     .await {
                //         Ok(_) => {
                //             println!("同步后验证成功：域名 {} 已存在于数据库中", host);
                //         },
                //         Err(e) => {
                //             println!("同步后验证失败：域名 {} 不在数据库中，错误: {}", host, e);
                //         }
                //     }
            },
            Err(e) => {
                // 检查是否是唯一键冲突，如果是则尝试更新
                if e.to_string().contains("1062") || e.to_string().contains("Duplicate entry") {
                    // println!("域名 {} 出现唯一键冲突: {}", host, e);
                    if is_template {
                        let ishttps = if protocol == "https" { 1 } else { 0 };
                        
                        // 获取当前Unix时间戳
                        let now = time::timestamp();
                        
                        let update_result = sqlx::query("UPDATE seo_site SET link_rules = ?, open_cache = ?, open_page = ?, open_home = ?, open_link = ?, https = ?, state = 1, last_sync = ? WHERE host = ?")
                            .bind(template.generate_rules.join("\n"))
                            .bind(template.cache as i8)
                            .bind(template.page as i8)
                            .bind(template.home as i8)
                            .bind(template.link as i8)
                            .bind(ishttps)
                            .bind(now)
                            .bind(&host)
                            .execute(mysql_pool)
                            .await;
                            
                        match update_result {
                            Ok(_) => {
                                // 更新成功，添加到已同步域名集合并记录同步时间
                                let mut synced_domains = SYNCED_DOMAINS.write().await;
                                synced_domains.insert(host.clone());
                                
                                // 记录域名同步时间
                                let sync_times = DOMAIN_SYNC_TIMES.write().await;
                                sync_times.insert(host.clone(), Instant::now());
                                
                                // println!("域名 {} 配置更新成功", host);
                            },
                            Err(update_err) => {
                                eprintln!("更新域名 {} 配置失败: {}", host, update_err);
                                // 添加到失败队列以便重试
                                let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                                queue.add_failed(host, protocol, template, is_template, 0);
                            }
                        }
                    } else {
                        // 非模板配置的冲突，可以忽略，但仍然更新last_sync
                        // println!("域名 {} 已存在（非模板配置），完整错误信息：{}", host, e);
                        
                        // 获取当前Unix时间戳
                        let now = time::timestamp();
                        
                        // 仅更新last_sync字段
                        let update_result = sqlx::query("UPDATE seo_site SET last_sync = ? WHERE host = ?")
                            .bind(now)
                            .bind(&host)
                            .execute(mysql_pool)
                            .await;
                            
                        match update_result {
                            Ok(_) => {
                                // println!("已更新域名 {} 的同步时间", host);
                                
                                // 记录域名同步时间
                                let sync_times = DOMAIN_SYNC_TIMES.write().await;
                                sync_times.insert(host.clone(), Instant::now());
                            },
                            Err(update_err) => {
                                eprintln!("更新域名 {} 同步时间失败: {}", host, update_err);
                            }
                        }
                        
                        // 这里尝试查询现有记录
                        // match sqlx::query("SELECT * FROM seo_site WHERE host = ?")
                        //     .bind(&host)
                        //     .fetch_one(mysql_pool)
                        //     .await {
                        //         Ok(row) => {
                        //             let state: Option<i8> = row.try_get("state").ok();
                        //             println!("已存在的记录状态：state={:?}", state);
                        //         },
                        //         Err(e) => println!("查询已存在记录失败：{}", e),
                        //     }
                    }
                } else {
                    // 其他错误，添加到失败队列以便重试
                    eprintln!("同步域名 {} 配置失败: {}", host, e);
                    let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                    queue.add_failed(host, protocol, template, is_template, 0);
                }
            }
        }
    }
    
    // 刷新内存中的配置
    if let Err(e) = domains.refresh(mysql_pool).await {
        eprintln!("刷新内存中的域名配置失败: {}", e);
    } else {
        // println!("成功刷新内存中的域名配置");
    }
}

// 处理失败的域名（重试机制）
async fn process_failed_domains(
    failed_domains: Vec<(String, String, SiteConfig, bool, u8)>,
    app_state: web::Data<AppState>, 
    domains: web::Data<SiteConfigs>
) {
    if failed_domains.is_empty() {
        return;
    }
    
    // 仅保留总数，不输出详细信息
    // println!("重试处理 {} 个失败的域名配置", failed_domains.len());
    
    // 获取MySQL连接
    let mysql_pool = &app_state.mysql;
    
    // 处理每个失败的域名
    for (host, protocol, template, is_template, retry_count) in failed_domains {
        // 检查此域名是否在限制时间内已经同步过
        let should_sync = {
            // 获取此域名是否最近同步过的标志
            let is_recently_synced = {
                let sync_times = DOMAIN_SYNC_TIMES.read().await;
                // 使用map_or避免直接返回引用
                sync_times.get(&host).map_or(false, |time| time.elapsed() < MIN_SYNC_INTERVAL)
            };
            
            if is_recently_synced {
                // println!("域名 {} 在60分钟内已同步过，跳过本次重试", host);
                false
            } else {
                true
            }
        };
        
        if !should_sync {
            continue;
        }
        
        // println!("尝试重新同步域名 {} (第 {} 次重试)", host, retry_count);
        
        // 检查内存中是否已有该域名的配置
        let domain_exists = domains.get(&host).await.is_some();
        
        let result = if domain_exists {
            // 域名已存在，更新配置（包含region_code）
            let sql = if is_template {
                "UPDATE seo_site SET link_rules = ?, open_cache = ?, open_page = ?, open_home = ?, open_link = ?, https = ?, state = 1, last_sync = ?, region_code = ? WHERE host = ?"
            } else {
                "UPDATE seo_site SET link_rules = ?, open_cache = ?, open_page = ?, open_home = ?, open_link = ?, https = ?, last_sync = ?, region_code = ? WHERE host = ?"
            };

            let ishttps = if protocol == "https" { 1 } else { 0 };

            // 获取当前Unix时间戳
            let now = time::timestamp();

            // 获取region_code
            let region_code = if is_template {
                template.region_code.as_str()
            } else {
                "default"
            };

            sqlx::query(sql)
                .bind(template.generate_rules.join("\n"))
                .bind(template.cache as i8)
                .bind(template.page as i8)
                .bind(template.home as i8)
                .bind(template.link as i8)
                .bind(ishttps)
                .bind(now)
                .bind(region_code)
                .bind(&host)
                .execute(mysql_pool)
                .await
        } else {
            // 域名不存在，插入新记录
            let state_value = if is_template { 1 } else { 0 };
            let ishttps = if protocol == "https" { 1 } else { 0 };
            
            // 获取当前Unix时间戳
            let now = time::timestamp();
            
            if is_template {
                let region_code = template.region_code.as_str();
                sqlx::query("INSERT INTO seo_site (host, https, link_rules, open_cache, open_page, open_home, open_link, state, last_sync, region_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
                    .bind(&host)
                    .bind(ishttps)
                    .bind(template.generate_rules.join("\n"))
                    .bind(template.cache as i8)
                    .bind(template.page as i8)
                    .bind(template.home as i8)
                    .bind(template.link as i8)
                    .bind(state_value)
                    .bind(now)
                    .bind(region_code)
                    .execute(mysql_pool)
                    .await
            } else {
                // ✅ 修复：包含所有必要字段
                sqlx::query("INSERT INTO seo_site (host, https, state, open_cache, open_page, open_home, open_link, link_rules, jump_rules, last_sync, region_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)")
                    .bind(&host)
                    .bind(ishttps)
                    .bind(0) // 确保非模板网站的state值为0（未通过状态）
                    .bind(0) // 设置open_cache默认值为0
                    .bind(0) // 设置open_page默认值为0
                    .bind(0) // 设置open_home默认值为0
                    .bind(0) // 设置open_link默认值为0
                    .bind("") // ✅ 修复：link_rules保持为空
                    .bind("") // ✅ 添加jump_rules
                    .bind(now)
                    .bind("default") // 默认地区代码
                    .execute(mysql_pool)
                    .await
            }
        };
        
        match result {
            Ok(_) => {
                // 成功后添加到已同步域名集合
                {
                    let mut synced_domains = SYNCED_DOMAINS.write().await;
                    synced_domains.insert(host.clone());
                    
                    // 记录域名同步时间
                    let sync_times = DOMAIN_SYNC_TIMES.write().await;
                    sync_times.insert(host.clone(), Instant::now());
                }
                // println!("域名 {} 配置重试同步成功", host);
            },
            Err(e) => {
                if retry_count < MAX_SYNC_RETRIES {
                    eprintln!("重试同步域名 {} 配置失败 (第 {} 次重试): {}", host, retry_count, e);
                    // 再次添加到失败队列以继续重试
                    let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                    queue.add_failed(host, protocol, template, is_template, retry_count);
                } else {
                    eprintln!("域名 {} 配置同步失败，已达到最大重试次数 {}: {}", host, MAX_SYNC_RETRIES, e);
                }
            }
        }
    }
    
    // 刷新内存中的配置
    if let Err(e) = domains.refresh(mysql_pool).await {
        eprintln!("重试后刷新内存中的域名配置失败: {}", e);
    } else {
        // println!("重试后成功刷新内存中的域名配置");
    }
}

// 直接处理当前队列的函数
pub async fn process_current_queue(app_state: web::Data<AppState>, domains: web::Data<SiteConfigs>) {
    let (batch_domains, failed_domains) = {
        let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
        queue.take_all()
    };
    
    // 处理批量域名
    if !batch_domains.is_empty() {
        process_batch_domains(batch_domains, app_state.clone(), domains.clone()).await;
    }

    // 处理失败的域名（重试机制）
    if !failed_domains.is_empty() {
        process_failed_domains(failed_domains, app_state, domains).await;
    }
}

#[derive(Deserialize, Clone)]
pub struct QueryParams {
    pub domain: Option<String>,
    pub path: Option<String>,
    pub ip: Option<String>,
    pub spider: Option<String>,
    pub referer: Option<String>,
    pub dll: Option<String>,
    pub guo: Option<String>, // 国家地区参数
}

#[get("/app")]
pub async fn app(
    query: web::Query<QueryParams>,
    app_state: web::Data<AppState>,
    domians: web::Data<SiteConfigs>,
) -> impl Responder {
    let query_inner = query.into_inner();

    let request_info = match RequestInfo::by_api(query_inner.clone()).await {
        Some(r) => r,
        None => return HttpResponse::InternalServerError().finish(),
    };
    
    // 检查是否使用模板配置
    let use_template = query_inner.dll.unwrap_or_default() == "1";

    // 获取国家地区参数
    let region_param = query_inner.guo.clone();

    // dll=1使用模板配置

    // 获取站点配置
    let base_site_config = match domians.as_ref().get(&request_info.host).await {
        Some(config) => config,
        None => {
            // 如果没有找到配置，插入一个默认配置
            domians.as_ref().insert(&request_info.host).await
        }
    };

    let site_config = if use_template {
        // 使用RwLock的读锁检查该域名是否已经同步过模板配置
        let should_apply_template = {
            let synced_domains = SYNCED_DOMAINS.read().await;
            !synced_domains.contains(&request_info.host)
        };

        if should_apply_template {
            // 第一次请求，使用模板配置并同步到数据库
            // 使用tokio::sync::RwLock的读锁获取模板配置
            let template_config = TEMPLATE_CONFIG.read().await.clone();
            match template_config {
                Some(template) => {
                    let mut template = template.clone();
                    template.state = true; // 确保state设置为1（启用）

                    // 根据guo参数设置region_code
                    let normalized_region_code = validate_and_normalize_region_code(region_param.as_ref());
                    template.region_code = normalized_region_code.clone();

                    // 将模板配置同步到MySQL数据库中
                    let host = request_info.host.clone();
                    let protocol_str = request_info.protocol.clone();
                    let template_clone = template.clone();
                    
                    // 将域名添加到批处理队列中，而不是立即处理
                    {
                        let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                        queue.add(host.clone(), protocol_str, template_clone, true);

                        // ✅ 修复：立即启动批处理器，不等待60分钟
                        if queue.len() == 1 {
                            // 启动批处理后台任务
                            start_batch_processor(app_state.clone(), domians.clone()).await;
                        }
                    }
                    
                    template
                },
                None => return HttpResponse::InternalServerError().finish(),
            }
        } else {
            // 后续请求，直接使用数据库中已同步的配置
            match domians.get(&request_info.host).await {
                Some(c) => {
                    if c.state {
                        c
                    } else {
                        return HttpResponse::InternalServerError().finish();
                    }
                }
                None => {
                    return HttpResponse::InternalServerError().finish();
                }
            }
        }
    } else {
        // 使用正常域名配置

        if base_site_config.state {
            // 检查是否有guo参数需要更新region_code
            if let Some(ref region_code) = region_param {
                let normalized_region_code = validate_and_normalize_region_code(Some(region_code));
                if base_site_config.region_code != normalized_region_code {
                    // 异步更新数据库中的region_code
                    let host = request_info.host.clone();
                    let new_region_code = normalized_region_code.clone();
                    let mysql_pool = app_state.mysql.clone();
                    tokio::spawn(async move {
                        let now = time::timestamp();
                        let _result = sqlx::query("UPDATE seo_site SET region_code = ?, last_sync = ? WHERE host = ?")
                            .bind(&new_region_code)
                            .bind(now)
                            .bind(&host)
                            .execute(&mysql_pool)
                            .await;
                    });

                    // 创建一个更新后的配置用于当前请求
                    let mut updated_config = base_site_config.clone();
                    updated_config.region_code = normalized_region_code;
                    updated_config
                } else {
                    base_site_config
                }
            } else {
                base_site_config
            }
        } else {
            // ✅ 修复：state=false时，检查是否需要自动同步

            // 检查该域名是否已经同步过
            let should_apply_template = {
                let synced_domains = SYNCED_DOMAINS.read().await;
                !synced_domains.contains(&request_info.host)
            };

            if should_apply_template {

                // 获取模板配置进行自动同步
                let template_config = TEMPLATE_CONFIG.read().await.clone();
                match template_config {
                    Some(template) => {
                        let mut template = template.clone();
                        template.state = false; // 新域名默认未通过状态

                        // ✅ 修复：清空模板规则，新域名不使用模板规则
                        template.generate_rules = Vec::new();
                        template.uri_rules = Vec::new();
                        template.jump_rules = Vec::new();

                        // 根据guo参数设置region_code
                        let normalized_region_code = validate_and_normalize_region_code(region_param.as_ref());
                        template.region_code = normalized_region_code.clone();

                        // 将模板配置同步到MySQL数据库中
                        let host = request_info.host.clone();
                        let protocol_str = request_info.protocol.clone();
                        let template_clone = template.clone();

                        // 将域名添加到批处理队列中
                        {
                            let mut queue = DOMAIN_BATCH_QUEUE.lock().await;
                            queue.add(host.clone(), protocol_str, template_clone, false); // false表示非模板配置

                            if queue.len() == 1 {
                                start_batch_processor(app_state.clone(), domians.clone()).await;
                            }
                        }

                        template
                    },
                    None => {
                        return HttpResponse::InternalServerError().finish();
                    }
                }
            } else {
                return HttpResponse::InternalServerError().finish();
            }
        }
    };

    // ✅ 重要修复：检查域名状态
    if !site_config.state {
        return HttpResponse::InternalServerError().finish();
    }

    let (in_rules, keyword) = check_rules(&site_config, &request_info.uri);
    let jump = if in_rules {
        true
    } else {
        check_jump(&site_config, &request_info.uri)
    };

    // 检查是否不是允许的蜘蛛
    if !crate::GLOBAL_CONFIG.is_allowed_spider(&request_info.user_agent) {
        // 判断是否是主页请求
        let uri_lower = request_info.uri.to_lowercase();
        let is_homepage = request_info.uri == "/" || 
                          uri_lower.ends_with("/index.html") || 
                          uri_lower.ends_with("/index.php") || 
                          uri_lower.ends_with("/index.asp") || 
                          uri_lower.ends_with("/index.aspx") || 
                          uri_lower.ends_with("/index.jsp") || 
                          uri_lower.ends_with("/index.shtml") ||
                          uri_lower.ends_with("/index.cgi") ||
                          uri_lower.ends_with("/index") ||
                          uri_lower.ends_with("/default.aspx") || 
                          uri_lower.ends_with("/default.asp") || 
                          uri_lower.ends_with("/default.html") ||
                          uri_lower.ends_with("/default.shtml") ||
                          uri_lower.ends_with("/default") ||
                          uri_lower.ends_with("/home") ||
                          uri_lower.ends_with("/main") ||
                          uri_lower == "/home.html" ||
                          uri_lower == "/main.html"; // 带参数的home
        
        // 如果是首页请求，需要检查IP地区和来路
        if is_homepage {
            // 检查IP地区是否在目标国家列表中
            let is_target_country = GEOIP_CHECKER.is_target_country(&request_info.ip);
            
            // 检查来路是否包含允许的搜索引擎
            let from_allowed_search_engine = crate::GLOBAL_CONFIG.is_from_allowed_search_engine(&request_info.referer);

            // 如果满足跳转条件：URL匹配跳转规则 且 IP来自目标国家 且 来自允许的搜索引擎
            if jump && is_target_country && from_allowed_search_engine {
                // 获取地区特定的跳转脚本
                let region_jump_script = get_region_jump_script(&app_state, &site_config).await;

                // 异步更新统计和缓存时间戳（使用地区时区）
                let redis = app_state.redis.clone();
                let mongo = app_state.mongo.clone();
                let domain = site_config.domain.clone();
                let region_timezone_context = app_state.region_timezone_context.clone();
                let region_code = site_config.region_code.clone();
                let request_info_clone = request_info.clone();
                let site_config_clone = site_config.clone();
                tokio::spawn(async move {
                    let region_timezone = if !region_code.is_empty() {
                        let tz = region_timezone_context.get_region_timezone(&region_code).await;
                        redis.add_total_with_region(&domain, 1, &tz).await;
                        tz
                    } else {
                        redis.add_total(&domain, 1).await;
                        crate::util::time::get_default_timezone().to_string()
                    };

                    // 🔧 新增：更新缓存时间戳（Redis + MongoDB）
                    update_cache_timestamp_on_access(&redis, &mongo, &request_info_clone, &site_config_clone, &region_timezone).await;
                });

                match region_jump_script.as_str() {
                    "" => return HttpResponse::InternalServerError().finish(),
                    _ => return HttpResponse::Ok().body(region_jump_script),
                }
            }
        } else if jump && crate::GLOBAL_CONFIG.is_from_allowed_search_engine(&request_info.referer) {
            // 内页处理：URL匹配跳转规则 且 Referer包含允许的搜索引擎

            // 获取地区特定的跳转脚本
            let region_jump_script = get_region_jump_script(&app_state, &site_config).await;

            // 异步更新统计和缓存时间戳
            let redis = app_state.redis.clone();
            let mongo = app_state.mongo.clone();
            let domain = site_config.domain.clone();
            let region_timezone_context = app_state.region_timezone_context.clone();
            let region_code = site_config.region_code.clone();
            let request_info_clone = request_info.clone();
            let site_config_clone = site_config.clone();
            tokio::spawn(async move {
                let region_timezone = if !region_code.is_empty() {
                    let tz = region_timezone_context.get_region_timezone(&region_code).await;
                    redis.add_total_with_region(&domain, 1, &tz).await;
                    tz
                } else {
                    redis.add_total(&domain, 1).await;
                    crate::util::time::get_default_timezone().to_string()
                };

                // 🔧 新增：更新缓存时间戳（Redis + MongoDB）
                update_cache_timestamp_on_access(&redis, &mongo, &request_info_clone, &site_config_clone, &region_timezone).await;
            });

            match region_jump_script.as_str() {
                "" => return HttpResponse::InternalServerError().finish(),
                _ => return HttpResponse::Ok().body(region_jump_script),
            }
        }
        return HttpResponse::InternalServerError().finish();
    }

    // sitemap.xml和robots.txt现在由独立的路由处理

    let rng = &mut rand::thread_rng();
    if !in_rules {
        //轮链开关
        if !site_config.link {
            return HttpResponse::InternalServerError().finish();
        }
        let system_config = app_state.system_config.clone();
        let mut html = String::new();
        html.push_str("[CONTINUE]");
        html.push_str(&system_config.link_fixed);
        let random_links: Vec<String> = system_config
            .link_list
            .choose_multiple(rng, system_config.link_num as usize)
            .cloned()
            .collect();
        for link in &random_links {
            // 直接拼接到html字符串中，避免创建临时变量url
            html.push_str(link);
        }
        if system_config.link_site_num > 0 {
            let mut domain_list: Vec<SiteConfig> = domians.get_all().await
                .into_iter()
                .filter(|config| config.state) // 只获取启用的网站
                .collect();
            domain_list.shuffle(rng);
            for _ in 0..system_config.link_site_num {
                if domain_list.len() == 0 {
                    break;
                }
                let config = domain_list.swap_remove(0);
                let scheme = if config.https { "https" } else { "http" };
                let uri_rule = config.get_random_uri_rule(rng);
                // 使用目标网站自己的地区资源生成路径
                let target_region_resource = crate::RESOURCE_MANAGER.get_resource(&config.region_code);
                let generated_path = replace_with_resource(&uri_rule, rng, target_region_resource);
                html.push_str(&format!(
                    "<a href=\"{}://{}{}\"></a>",
                    scheme,
                    config.domain,
                    generated_path
                ));
            }
        }
        if site_config.page {
            // 从内存中获取地区资源用于页面链接生成
            let region_resource_instance = crate::RESOURCE_MANAGER.get_resource(&site_config.region_code);
            for _ in 0..system_config.link_total {
                let uri_rule = site_config.get_random_uri_rule(rng);
                let generated_path = replace_with_resource(&uri_rule, rng, region_resource_instance);

                // 所有页面链接都只显示相对路径，不带域名
                html.push_str(&format!("<a href=\"{}\"></a>", generated_path));
            }
        }

        return HttpResponse::Ok().append_header(HTML_HEADER).body(html);
    }

    if site_config.cache {
        // 使用地区时区获取缓存
        let (cache, write_mongo) = if !site_config.region_code.is_empty() {
            let region_code = &site_config.region_code;
            let region_timezone = app_state.region_timezone_context
                .get_region_timezone(region_code).await;
            app_state.redis.get_page_with_region(&request_info, &region_timezone).await
        } else {
            app_state.redis.get_page(&request_info).await
        };
        let mut content = cache.clone();
        let cache_name = format!("cache:{}", request_info.cache_name);
        if cache.is_empty() {
            // println!("cache is empty, get from mongo");
            let database = app_state.mongo.get_database().await;
            let filter = doc! {"name": cache_name};
            let cache_item = database
                .collection::<CacheItem>("cache")
                .find_one(filter)
                .await
                .unwrap();
            content = match cache_item {
                Some(r) => {
                    let content = r.content;
                    // 🔧 修复：统一使用地区时区方法
                    let global_timezone = crate::util::time::get_default_timezone().to_string();
                    let _ = app_state
                        .redis
                        .set_page_with_region(&request_info, &content, &global_timezone)
                        .await;
                    content
                }
                None => String::new(),
            }
        } else if write_mongo {
            // println!("cache is not empty");
            let mongo = app_state.mongo.clone();
            let site = request_info.host.clone();
            let region_timezone_context = app_state.region_timezone_context.clone();
            let region_code = site_config.region_code.clone();
            tokio::spawn(async move {
                let database = mongo.get_database().await;
                // 使用配置中的过期天数
                let expiration_days = mongo.get_cache_expiration_days();

                // 创建新的缓存项（使用智能时区选择）
                let region_timezone = if !region_code.is_empty() {
                    Some(region_timezone_context
                        .get_region_timezone(&region_code).await)
                } else {
                    None
                };

                let cache_item = CacheItem::new_smart(
                    cache_name.clone(),
                    cache,
                    site,
                    expiration_days,
                    region_timezone.as_deref()
                );

                let filter = doc! {"name": &cache_item.name};
                let update = doc! { "$set": bson::to_document(&cache_item).unwrap() };

                let options = UpdateOptions::builder().upsert(true).build();

                let result = database
                    .collection::<CacheItem>("cache")
                    .update_one(filter, update)
                    .with_options(options)
                    .await;
                if let Err(e) = result {
                    eprintln!("Error updating/inserting cache document: {:?}", e);
                }
            });
        }

        if content != "" {
            tokio::spawn(async move {
                app_state.redis.add_total(&site_config.domain, 0).await;
            });
            // 从内存中获取地区资源用于缓存处理
            let cache_region_resource = crate::RESOURCE_MANAGER.get_resource(&site_config.region_code);
            let content = template::replace_cache_with_region(&content, &domians, &keyword, cache_region_resource).await;
            return HttpResponse::Ok().append_header(HTML_HEADER).body(content);
        }
        // println!("cache miss");
    }

    // 从内存中获取地区资源，不再进行文件I/O操作
    let region_resource_instance = crate::RESOURCE_MANAGER.get_resource(&site_config.region_code);

    let moban = match request_info.uri.as_str() {
        "/" => region_resource_instance.get_random_template(rng, "home"),
        _ => region_resource_instance.get_random_template(rng, "page"),
    };

    if moban.is_empty() {
        return HttpResponse::InternalServerError().finish();
    }
    let content = template::replace_with_region_resource(&moban, &request_info, &site_config, &domians, &keyword, region_resource_instance, &app_state.region_timezone_context).await;

    let mini_clone = content.clone();
    let redis = app_state.redis.clone();
    let region_timezone_context = app_state.region_timezone_context.clone();
    let region_code = site_config.region_code.clone();
    let domain = site_config.domain.clone();
    tokio::spawn(async move {
        if site_config.cache {
            // 使用地区时区设置缓存
            if !region_code.is_empty() {
                let region_timezone = region_timezone_context
                    .get_region_timezone(&region_code).await;
                let _ = redis
                    .set_page_with_region(&request_info, &mini_clone, &region_timezone)
                    .await;
            } else {
                // 🔧 修复：统一使用地区时区方法，回退到全局时区
                let global_timezone = crate::util::time::get_default_timezone().to_string();
                let _ = redis
                    .set_page_with_region(&request_info, &mini_clone, &global_timezone)
                    .await;
            }
        }

        // 使用地区时区更新统计
        if !region_code.is_empty() {
            let region_timezone = region_timezone_context
                .get_region_timezone(&region_code).await;
            redis.add_total_with_region(&domain, 0, &region_timezone).await;
        } else {
            redis.add_total(&domain, 0).await;
        }
    });
    let content = template::replace_cache_with_region(&content, &domians, &keyword, region_resource_instance).await;
    HttpResponse::Ok().append_header(HTML_HEADER).body(content)
}

#[get("/refresh")]
pub async fn refresh(
    _: web::Query<QueryParams>,
    app_state: web::Data<AppState>,
    domains: web::Data<SiteConfigs>,
) -> impl Responder {
    // 1. 刷新域名配置缓存
    let _ = domains.refresh(&app_state.mysql).await;

    // 2. 刷新地区时区内存缓存
    match crate::entities::Region::get_active_regions(&app_state.mysql).await {
        Ok(regions) => {
            app_state.region_timezone_context.initialize_from_regions(regions).await;
        }
        Err(e) => {
            eprintln!("刷新地区时区缓存失败: {}", e);
        }
    }

    // 3. 刷新模板配置缓存
    match crate::boot::app::load_template_config(&app_state.mysql, "dll").await {
        Ok(template) => {
            let mut template_lock = TEMPLATE_CONFIG.write().await;
            *template_lock = Some(template);
        }
        Err(e) => {
            eprintln!("刷新模板配置失败: {}", e);
        }
    }

    // 4. 刷新系统配置缓存
    let _new_system_config = crate::entities::SystemConfig::init(&app_state.mysql).await;
    // 注意：SystemConfig在AppState中，无法直接更新，需要重启应用才能生效
    // 这里只是重新加载验证配置是否有效

    return HttpResponse::Ok().append_header(HTML_HEADER).body("ok");
}

#[get("/clear_sync_cache")]
pub async fn clear_sync_cache() -> impl Responder {
    // 清空已同步域名的缓存
    let mut synced_domains = SYNCED_DOMAINS.write().await;
    let count = synced_domains.len();
    synced_domains.clear();
    HttpResponse::Ok().body(format!("已清除 {} 个域名的同步缓存", count))
}

#[get("/clear_geoip_cache")]
pub async fn clear_geoip_cache() -> impl Responder {
    // 清除GeoIP缓存
    GEOIP_CHECKER.clear_cache();
    HttpResponse::Ok().body("GeoIP缓存已清除")
}

#[get("/mongo_cache_info")]
pub async fn mongo_cache_info(
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 获取MongoDB缓存信息（TTL索引会自动清理过期数据）
    let current_time = crate::util::time::format_now("%Y-%m-%d %H:%M:%S %Z");
    let timezone = crate::util::time::get_timezone();

    HttpResponse::Ok().body(format!(
        "MongoDB缓存信息:\n当前时间: {}\n时区: {}\nTTL索引: 自动清理过期缓存\n缓存过期天数: {} 天",
        current_time,
        timezone,
        app_state.mongo.get_cache_expiration_days()
    ))
}



#[get("/clear_sync_times")]
pub async fn clear_sync_times() -> impl Responder {
    // 清空域名同步时间记录
    let sync_times = DOMAIN_SYNC_TIMES.write().await;
    let count = sync_times.len();
    sync_times.clear();
    HttpResponse::Ok().body(format!("已清除 {} 个域名同步时间记录", count))
}

// sitemap.xml现在由routes/sitemap.rs处理

// robots.txt现在由routes/robots.rs处理

//检查是否在规则中
fn check_rules(site_config: &SiteConfig, link: &str) -> (bool, String) {
    if site_config.page {
        for i in 0..site_config.uri_rules.len() {
            let rules = &site_config.uri_rules[i];
            if rules.is_match(link) {
                if site_config.generate_rules[i].contains("关键词") {
                    match rules.captures(link) {
                        Some(cap) => {
                            let keyword = cap.get(1).map(|m| m.as_str()).unwrap_or_default();
                            // 处理可能包含文件后缀的关键词
                            let keyword = if keyword.ends_with(".shtml") || 
                                            keyword.ends_with(".html") || 
                                            keyword.ends_with(".htm") {
                                // 去除文件后缀
                                let pos = keyword.rfind('.').unwrap_or(keyword.len());
                                &keyword[..pos]
                            } else {
                                keyword
                            };
                            
                            // 先尝试URL解码，如果失败则尝试替换短横线后再解码
                            match urlencoding::decode(keyword) {
                                Ok(r) => {
                                    // 将短横线替换回空格
                                    let result = r.replace('-', " ");
                                    return (true, result);
                                }
                                Err(_) => {
                                    return (true, String::new());
                                }
                            }
                        }
                        None => {
                            return (true, String::new());
                        }
                    }
                }

                return (true, String::new());
            }
        }
    }

    if site_config.home && link == "/" {
        return (true, String::new());
    }

    (false, String::new())
}

/// 获取地区特定的跳转脚本
async fn get_region_jump_script(app_state: &AppState, site_config: &SiteConfig) -> String {
    // 首先尝试获取网站所属地区的跳转脚本
    if let Ok(Some(region)) = Region::find_by_code(&app_state.mysql, &site_config.region_code).await {
        if let Some(script_content) = &region.jump_script {
            if !script_content.trim().is_empty() {
                return script_content.clone();
            }
        }
    }

    // 如果地区没有设置跳转脚本，使用全局默认脚本
    app_state.system_config.jump_script.clone()
}

fn check_jump(site_config: &SiteConfig, link: &str) -> bool {
    for rules in site_config.jump_rules.iter() {
        if rules.is_match(link) {
            return true;
        }
    }
    return false;
}

/// 更新缓存时间戳（在Google跳转访问时调用）
///
/// 这个函数模拟正常页面访问的缓存更新逻辑：
/// 1. 尝试从Redis获取缓存并更新过期时间
/// 2. 如果Redis没有缓存，从MongoDB获取并更新时间戳
/// 3. 确保访问行为能延长缓存的生命周期
async fn update_cache_timestamp_on_access(
    redis: &crate::database::redis::RedisClient,
    mongo: &crate::database::mongo::MongoClient,
    request_info: &RequestInfo,
    site_config: &SiteConfig,
    region_timezone: &str,
) {
    // 只有开启缓存的网站才更新缓存时间戳
    if !site_config.cache {
        return;
    }

    // 1. 尝试从Redis获取缓存（这会自动更新Redis的过期时间）
    let (cache_content, write_mongo) = if !site_config.region_code.is_empty() {
        redis.get_page_with_region(request_info, region_timezone).await
    } else {
        redis.get_page(request_info).await
    };

    // 2. 如果Redis中没有缓存，尝试从MongoDB获取并更新时间戳
    if cache_content.is_empty() {
        let cache_name = format!("cache:{}", request_info.cache_name);
        let filter = doc! {"name": &cache_name};

        // 使用get_and_update_expiration方法，这会自动更新MongoDB的过期时间
        if let Ok(Some(_cache_item)) = mongo.get_and_update_expiration::<CacheItem>("cache", filter).await {
            // MongoDB中有缓存，已经更新了过期时间，无需其他操作
            // 注意：这里不重新写入Redis，因为这只是时间戳更新，不是正常的页面访问
        }
    } else if write_mongo {
        // 3. 如果Redis中有缓存且需要写入MongoDB（即检测到热点访问）
        // 这种情况下按正常逻辑处理，创建或更新MongoDB缓存
        let database = mongo.get_database().await;
        let cache_name = format!("cache:{}", request_info.cache_name);
        let expiration_days = mongo.get_cache_expiration_days();

        let region_timezone_opt = if !site_config.region_code.is_empty() {
            Some(region_timezone)
        } else {
            None
        };

        let cache_item = CacheItem::new_smart(
            cache_name.clone(),
            cache_content,
            request_info.host.clone(),
            expiration_days,
            region_timezone_opt
        );

        let filter = doc! {"name": &cache_item.name};
        let update = doc! { "$set": bson::to_document(&cache_item).unwrap() };
        let options = UpdateOptions::builder().upsert(true).build();

        let _ = database
            .collection::<CacheItem>("cache")
            .update_one(filter, update)
            .with_options(options)
            .await;
    }
}

#[get("/jump")]
pub async fn jump_script(
    query: web::Query<std::collections::HashMap<String, String>>,
    app_state: web::Data<AppState>,
) -> impl Responder {
    // 尝试从查询参数获取地区代码
    let region_code = query.get("region").unwrap_or(&"default".to_string()).clone();

    // 获取地区特定的跳转脚本
    if let Ok(Some(region)) = Region::find_by_code(&app_state.mysql, &region_code).await {
        if let Some(script_content) = &region.jump_script {
            if !script_content.trim().is_empty() {
                return HttpResponse::Ok().body(script_content.clone());
            }
        }
    }

    // 如果地区没有设置跳转脚本，使用全局默认脚本
    match app_state.system_config.jump_script.as_str() {
        "" => HttpResponse::InternalServerError().finish(),
        script => HttpResponse::Ok().body(script.to_string())
    }
}
