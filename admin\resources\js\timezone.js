/**
 * 时区处理工具函数
 */

// 获取服务器端配置的应用时区
const APP_TIMEZONE = document.querySelector('meta[name="app-timezone"]')?.content || 'UTC';

/**
 * 格式化UTC时间为应用时区时间
 * @param {string} utcTime UTC时间字符串
 * @param {string} format 格式化模式 (默认: YYYY-MM-DD HH:mm:ss)
 * @returns {string} 格式化后的时间
 */
function formatInAppTimezone(utcTime, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!utcTime) return '';
    
    if (typeof moment === 'undefined') {
        console.warn('Moment.js is required for timezone conversion');
        return utcTime;
    }
    
    return moment.utc(utcTime).tz(APP_TIMEZONE).format(format);
}

/**
 * 将本地时间转换为UTC时间
 * @param {string} localTime 本地时间字符串
 * @returns {string} UTC时间字符串
 */
function toUTC(localTime) {
    if (!localTime) return '';
    
    if (typeof moment === 'undefined') {
        console.warn('Moment.js is required for timezone conversion');
        return localTime;
    }
    
    return moment.tz(localTime, APP_TIMEZONE).utc().format('YYYY-MM-DD HH:mm:ss');
}

/**
 * 初始化页面上的时间元素
 */
function initTimeElements() {
    const timeElements = document.querySelectorAll('[data-time-utc]');
    timeElements.forEach(el => {
        const utcTime = el.getAttribute('data-time-utc');
        const format = el.getAttribute('data-time-format') || 'YYYY-MM-DD HH:mm:ss';
        el.textContent = formatInAppTimezone(utcTime, format);
    });
}

// 页面加载完成后初始化时间元素
document.addEventListener('DOMContentLoaded', initTimeElements);

// 导出函数，方便其他JS模块使用
window.TimezoneUtils = {
    formatInAppTimezone,
    toUTC,
    APP_TIMEZONE
}; 