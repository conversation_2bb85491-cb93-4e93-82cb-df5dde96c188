-- 修复地区数据和外键约束

-- 1. 首先检查seo_regions表是否存在default记录
SELECT * FROM seo_regions WHERE code = 'default';

-- 2. 如果不存在，插入基础地区数据
INSERT IGNORE INTO `seo_regions` (`code`, `name`, `language`, `timezone`, `config`, `status`, `priority`, `created_at`, `updated_at`) VALUES
('default', '默认地区', 'en', 'UTC', '{"currency": "USD", "domain_suffix": ".com", "country_code": "US"}', 1, 1, NOW(), NOW()),
('br', '巴西', 'pt', 'America/Sao_Paulo', '{"currency": "BRL", "domain_suffix": ".com.br", "country_code": "BR"}', 1, 10, NOW(), NOW()),
('pk', '巴基斯坦', 'en', 'Asia/Karachi', '{"currency": "PKR", "domain_suffix": ".pk", "country_code": "PK"}', 1, 30, NOW(), NOW()),
('in', '印度', 'hi', 'Asia/Kolkata', '{"currency": "INR", "domain_suffix": ".in", "country_code": "IN"}', 1, 20, NOW(), NOW()),
('us', '美国', 'en', 'America/New_York', '{"currency": "USD", "domain_suffix": ".com", "country_code": "US"}', 1, 40, NOW(), NOW()),
('other', '其他', 'en', 'UTC', '{"currency": "USD", "domain_suffix": ".com", "country_code": "XX"}', 1, 999, NOW(), NOW());

-- 3. 检查seo_site表中是否有无效的region_code
SELECT DISTINCT region_code FROM seo_site 
WHERE region_code NOT IN (SELECT code FROM seo_regions);

-- 4. 将无效的region_code更新为default
UPDATE seo_site 
SET region_code = 'default' 
WHERE region_code NOT IN (SELECT code FROM seo_regions) 
   OR region_code IS NULL 
   OR region_code = '';

-- 5. 检查外键约束是否存在
SHOW CREATE TABLE seo_site;

-- 6. 如果外键约束不存在，添加它
-- ALTER TABLE seo_site 
-- ADD CONSTRAINT seo_site_ibfk_1 
-- FOREIGN KEY (region_code) REFERENCES seo_regions(code) 
-- ON DELETE SET NULL ON UPDATE CASCADE;

-- 7. 验证修复结果
SELECT 
    r.code,
    r.name,
    COUNT(s.id) as site_count
FROM seo_regions r
LEFT JOIN seo_site s ON r.code = s.region_code
GROUP BY r.code, r.name
ORDER BY r.priority;
