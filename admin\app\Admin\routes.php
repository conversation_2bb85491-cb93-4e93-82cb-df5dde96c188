<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;
use App\Models\SpiderLog;

Admin::routes();

Route::group([
    'prefix'     => config('admin.route.prefix'),
    'namespace'  => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    $router->resource('seo/config', 'SeoConfigController');
    $router->resource('seo/regions', 'SeoRegionController');
    $router->get('seo/regions-stats', 'SeoRegionController@stats')->name('seo-regions.stats');
    $router->post('seo/regions/batch-create-directories', 'SeoRegionController@batchCreateDirectories');
    $router->post('seo/regions/{code}/reload', 'SeoRegionController@reloadRegion');
    $router->get('seo/regions/export-config', 'SeoRegionController@exportConfig');
    $router->post('seo/regions/import-config', 'SeoRegionController@importConfig');
    // 批量编辑路由必须在resource路由之前定义
    $router->get('seo/site/batch-edit', 'SeoSiteController@batchEdit')->name('seo-sites.batch-edit');
    $router->post('seo/site/batch-edit', 'SeoSiteController@batchEdit')->name('seo-sites.batch-edit.post');
    $router->resource('seo/site', 'SeoSiteController');
    $router->resource('seo/cache', 'SeoCacheController');
    $router->resource('seo/total', 'SeoTotalController');
    $router->resource('seo/moban', 'SeoMobanController');
    $router->resource('seo/spider-log', 'SeoSpiderLogController');
    $router->resource('website/list', 'WebsiteController');

    // 网站管理路由
    $router->get('seo/website-management', 'WebsiteManagementController@index')->name('website-management.index');
    $router->get('seo/website-management/batch-edit', 'WebsiteManagementController@batchEdit')->name('website-management.batch-edit');
    $router->post('seo/website-management/batch-edit', 'WebsiteManagementController@batchEdit')->name('website-management.batch-edit.post');
    $router->post('seo/sync-region-menus', 'WebsiteManagementController@syncMenus')->name('website-management.sync-menus');
    $router->get('seo/website-stats', 'WebsiteManagementController@getStats')->name('website-management.stats');
    $router->get('seo/website-detail-stats', 'WebsiteManagementController@getDetailStats')->name('website-management.detail-stats');
    
    // 缓存统计API
    $router->get('api/cache-stats/{host}', 'SeoSiteController@getCacheStats')->name('api.cache-stats');

    // 蜘蛛日志导入API
    $router->post('api/spider-import', function () {
        try {
            $count = SpiderLog::importFromRedis(5000);
            return response()->json([
                'status' => true,
                'message' => "成功导入 {$count} 条蜘蛛爬行日志"
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => "导入失败：" . $e->getMessage()
            ]);
        }
    });
});
