<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Dcat\Admin\Models\Menu;

class MoveRegionManagementToSeparateMenu extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 1. 创建独立的地区管理主菜单
        $regionMainMenu = Menu::create([
            'parent_id' => 0,
            'order' => 3, // 排在SEO管理之后
            'title' => '地区管理',
            'icon' => 'fa-globe',
            'uri' => '',
            'show' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 2. 查找SEO管理菜单
        $seoMenu = Menu::where('title', 'SEO管理')->orWhere('title', 'Seo')->first();
        
        if ($seoMenu) {
            // 3. 查找原来在SEO下的地区管理菜单
            $oldRegionMenu = Menu::where('parent_id', $seoMenu->id)
                                ->where('title', '地区管理')
                                ->first();
            
            if ($oldRegionMenu) {
                // 4. 将原地区管理菜单的子菜单移动到新的主菜单下
                $subMenus = Menu::where('parent_id', $oldRegionMenu->id)->get();
                foreach ($subMenus as $subMenu) {
                    $subMenu->update(['parent_id' => $regionMainMenu->id]);
                }
                
                // 5. 删除原来的地区管理菜单
                $oldRegionMenu->delete();
            }
        }

        // 6. 创建地区管理子菜单
        Menu::create([
            'parent_id' => $regionMainMenu->id,
            'order' => 1,
            'title' => '地区配置',
            'icon' => 'fa-cog',
            'uri' => 'seo/regions',
            'show' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        Menu::create([
            'parent_id' => $regionMainMenu->id,
            'order' => 2,
            'title' => '地区统计',
            'icon' => 'fa-bar-chart',
            'uri' => 'seo/regions-stats',
            'show' => 1,
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // 7. 更新其他菜单的排序
        $this->updateMenuOrder();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 1. 查找独立的地区管理菜单
        $regionMainMenu = Menu::where('title', '地区管理')
                             ->where('parent_id', 0)
                             ->first();
        
        if ($regionMainMenu) {
            // 2. 查找SEO管理菜单
            $seoMenu = Menu::where('title', 'SEO管理')->orWhere('title', 'Seo')->first();
            
            if ($seoMenu) {
                // 3. 在SEO下重新创建地区管理菜单
                $newRegionMenu = Menu::create([
                    'parent_id' => $seoMenu->id,
                    'order' => 2,
                    'title' => '地区管理',
                    'icon' => 'feather icon-map',
                    'uri' => 'seo/regions',
                    'show' => 1,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
                
                // 4. 将子菜单移回SEO下的地区管理
                $subMenus = Menu::where('parent_id', $regionMainMenu->id)->get();
                foreach ($subMenus as $subMenu) {
                    $subMenu->update(['parent_id' => $newRegionMenu->id]);
                }
            }
            
            // 5. 删除独立的地区管理菜单
            $regionMainMenu->delete();
        }
    }

    /**
     * 更新菜单排序
     */
    private function updateMenuOrder()
    {
        // 获取所有顶级菜单并重新排序
        $topMenus = Menu::where('parent_id', 0)->orderBy('order')->get();
        
        $order = 1;
        foreach ($topMenus as $menu) {
            $menu->update(['order' => $order]);
            $order++;
        }
    }
}
