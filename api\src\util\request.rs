// pub mod web {
//     use actix_web::HttpRequest;
//     pub fn get_host(req: &HttpRequest) -> String {
//         let host_with_port = req.connection_info().host().to_owned();
//         match host_with_port.split(':').next() {
//             Some(host) => host.to_string(),
//             None => host_with_port,
//         }
//     }

//     pub fn get_user_agent(req: &HttpRequest) -> Option<String> {
//         get_header(req, "user-agent")
//     }
//     pub fn get_uri(req: &HttpRequest) -> String {
//         let uri = req.uri().path_and_query().map_or("", |v| v.as_str());
//         uri.to_string()
//     }

//     pub fn get_referer(req: &HttpRequest) -> &str {
//         get_header(req, "referer").unwrap_or_else(String::new)
//     }

//     pub fn get_protocol(req: &HttpRequest) -> String {
//         let proto = get_header(req, "x-forwarded-proto")
//             .as_ref()
//             .map(|s| s.to_lowercase());

//         match proto {
//             Some(ref p) if p == "http" => "http".to_string(),
//             Some(ref p) if p == "https" => "https".to_string(),
//             None => req.connection_info().scheme().to_string(),
//             _ => "http".to_string(),
//         }
//     }
//     pub fn get_ip(req: &HttpRequest) -> Option<String> {
//         get_header(req, "x-forwarded-for")
//             .and_then(parse_x_forwarded_for)
//             .or_else(|| req.peer_addr().map(|peer_addr| peer_addr.ip().to_string()))
//     }
//     pub fn get_current_url(req: &HttpRequest) -> String {
//         format!("{}://{}{}", get_protocol(req), get_host(req), get_uri(req))
//     }

//     fn get_header(req: &HttpRequest, header: &str) -> Option<String> {
//         req.headers()
//             .get(header)
//             .and_then(|v| v.to_str().ok())
//             .map(String::from)
//     }
//     fn parse_x_forwarded_for(header: String) -> Option<String> {
//         header.split(',').next().map(String::from)
//     }
// }
