#!/bin/bash

# API项目编译脚本
# 🎉 现在完全不需要数据库连接进行编译！

echo "🚀 开始编译API项目..."
echo "✨ 使用运行时查询，无需编译时数据库连接"

# 清理之前的编译缓存
echo "🧹 清理编译缓存..."
cargo clean

# 直接编译，完全独立于数据库
echo "📦 编译中..."
if cargo build --release; then
    echo ""
    echo "✅ API项目编译成功！"
    echo "📁 可执行文件: target/release/api"
    echo "🎉 编译完全独立，无需任何数据库连接！"
    echo ""
    echo "📋 下一步："
    echo "  1. 确保MySQL服务运行"
    echo "  2. 检查.env配置"
    echo "  3. 启动API服务: ./target/release/api"
else
    echo ""
    echo "❌ API项目编译失败！"
    echo "💡 请检查代码语法错误"
    exit 1
fi
