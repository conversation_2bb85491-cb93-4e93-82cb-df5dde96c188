use crate::GLOBAL_CONFIG;
use mongodb::{options::ClientOptions, Client};
use std::time::Duration;
use mongodb::bson::{doc, Document};
use mongodb::error::Error as MongoError;
use mongodb::bson::de::Error as BsonDeError;
use mongodb::bson::ser::Error as BsonSerError;
use std::sync::Arc;
use tokio::sync::OnceCell;
use futures::stream::TryStreamExt;

static MONGO_CLIENT: OnceCell<Arc<MongoClient>> = OnceCell::const_new();

#[derive(Clone)]
pub struct MongoClient {
    database: String,
    client: Client,
}

#[derive(Debug)]
pub enum MongoClientError {
    MongoDbError(MongoError),
    BsonDeError(BsonDeError),
    BsonSerError(BsonSerError),
}

impl From<MongoError> for MongoClientError {
    fn from(error: MongoError) -> Self {
        MongoClientError::MongoDbError(error)
    }
}

impl From<BsonDeError> for MongoClientError {
    fn from(error: BsonDeError) -> Self {
        MongoClientError::BsonDeError(error)
    }
}

impl From<BsonSerError> for MongoClientError {
    fn from(error: BsonSerError) -> Self {
        MongoClientError::BsonSerError(error)
    }
}

impl MongoClient {
    // 私有方法,构建MongoDB连接URI
    fn build_connection_uri() -> String {
        let mongo_config = &GLOBAL_CONFIG.mongo;
        if mongo_config.password.is_empty() || mongo_config.username.is_empty() {
            format!(
                "mongodb://{}:{}/{}",
                mongo_config.host, mongo_config.port, mongo_config.db
            )
        } else {
            format!(
                "mongodb://{}:{}@{}:{}/{}",
                mongo_config.username,
                mongo_config.password,
                mongo_config.host,
                mongo_config.port,
                mongo_config.db
            )
        }
    }
    
    // 创建新的MongoDB客户端实例
    async fn create_client() -> Result<Self, MongoClientError> {
        let mongo_config = &GLOBAL_CONFIG.mongo;
        
        // 构建 MongoDB 连接 URI
        let mongo_uri = Self::build_connection_uri();

        // 创建 MongoClientOptions
        let mut client_options = ClientOptions::parse(mongo_uri).await?;
        
        // 设置连接池
        client_options.min_pool_size = Some(mongo_config.min_pool);
        client_options.max_pool_size = Some(mongo_config.max_pool);
        
        // 设置超时
        client_options.connect_timeout = Some(Duration::from_secs(mongo_config.timeout));
        client_options.max_idle_time = Some(Duration::from_secs(mongo_config.timeout));

        // 创建 MongoClient
        let client = Client::with_options(client_options)?;

        // 测试连接
        client
            .database(&mongo_config.db)
            .run_command(doc! {"ping": 1})
            .await?;

        let mongo_client = Self {
            client,
            database: mongo_config.db.clone(),
        };

        Ok(mongo_client)
    }
    
    // 获取全局单例实例
    pub async fn get_instance() -> Result<Arc<Self>, MongoClientError> {
        MONGO_CLIENT
            .get_or_try_init(|| async {
                let client = Self::create_client().await?;
                Ok(Arc::new(client))
            })
            .await
            .map(|client| client.clone())
    }

    pub async fn get_database(&self) -> mongodb::Database {
        self.client.database(&self.database)
    }

    /// 创建TTL索引（支持MongoDB自动清理过期文档）
    pub async fn create_ttl_index(&self, collection: &str, field: &str) -> Result<(), MongoClientError> {
        let db = self.get_database().await;
        let collection = db.collection::<Document>(collection);

        // 创建TTL索引，expire_after设置为0表示根据文档中的日期字段自动过期
        let index_options = mongodb::options::IndexOptions::builder()
            .expire_after(Duration::from_secs(0))
            .build();

        let index_model = mongodb::IndexModel::builder()
            .keys(doc! { field: 1 })
            .options(index_options)
            .build();

        collection.create_index(index_model).await?;
        Ok(())
    }
    
    // 获取配置中的缓存过期天数
    pub fn get_cache_expiration_days(&self) -> u64 {
        let mongo_config = &GLOBAL_CONFIG.mongo;
        mongo_config.cache_expiration_days
    }

    /// 获取文档并更新过期时间（基于time.rs，转换为MongoDB Date类型）
    pub async fn get_and_update_expiration<T: serde::de::DeserializeOwned>(
        &self,
        collection: &str,
        filter: Document,
    ) -> Result<Option<T>, MongoClientError> {
        let db = self.get_database().await;
        let collection = db.collection::<Document>(collection);

        // 查找文档
        let doc = collection.find_one(filter.clone()).await?;

        if let Some(doc) = doc {
            // 使用time.rs计算新的过期时间，转换为MongoDB DateTime
            let expiration_days = self.get_cache_expiration_days();
            // 🔧 修复：使用智能时区选择，优先使用地区时区
            let future_timestamp = crate::util::time::calculate_cache_expiration_smart(
                None, // 这里无法获取地区时区，使用全局时区作为回退
                expiration_days * 24
            );
            let expired_at = mongodb::bson::DateTime::from_millis(future_timestamp * 1000);

            let update = doc! {
                "$set": {
                    "expiredAt": expired_at,
                }
            };

            collection.update_one(filter, update).await?;

            // 反序列化并返回
            let result: T = mongodb::bson::from_document(doc)?;
            Ok(Some(result))
        } else {
            Ok(None)
        }
    }

    /// 启动时迁移旧格式缓存（i64时间戳 -> MongoDB Date）
    pub async fn migrate_legacy_cache_on_startup(&self) -> Result<u64, MongoClientError> {
        let db = self.get_database().await;
        let collection = db.collection::<Document>("cache");

        println!("检查是否需要迁移旧格式缓存...");

        // 查找所有expiredAt字段为数字类型的文档（旧格式）
        let filter = doc! {
            "expiredAt": {
                "$type": "number"  // BSON类型：数字
            }
        };

        let mut cursor = collection.find(filter).await?;
        let mut migrated_count = 0;

        while let Some(doc) = cursor.try_next().await? {
            if let Some(expired_at_value) = doc.get("expiredAt") {
                // 尝试获取i64时间戳
                if let Some(timestamp) = expired_at_value.as_i64() {
                    // 转换为MongoDB DateTime
                    let new_expired_at = mongodb::bson::DateTime::from_millis(timestamp * 1000);

                    // 更新文档
                    let update_filter = doc! {"_id": doc.get("_id")};
                    let update = doc! {
                        "$set": {
                            "expiredAt": new_expired_at
                        }
                    };

                    if collection.update_one(update_filter, update).await.is_ok() {
                        migrated_count += 1;
                    }
                }
            }
        }

        if migrated_count > 0 {
            println!("成功迁移 {} 个旧格式缓存项到新格式", migrated_count);
        } else {
            println!("未发现需要迁移的旧格式缓存");
        }

        Ok(migrated_count)
    }

    // 插入文档并设置过期时间
    pub async fn insert_with_expiration<T: serde::Serialize>(
        &self,
        collection: &str,
        document: T,
    ) -> Result<mongodb::bson::oid::ObjectId, MongoClientError> {
        let db = self.get_database().await;
        let collection = db.collection::<Document>(collection);
        
        // 转换为BSON文档
        let doc = mongodb::bson::to_document(&document)?;
        
        // 插入文档
        let result = collection.insert_one(doc).await?;
        
        Ok(result.inserted_id.as_object_id().unwrap())
    }
}
