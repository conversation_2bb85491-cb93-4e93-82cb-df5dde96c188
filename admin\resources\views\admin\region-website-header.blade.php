{{-- 地区网站管理头部 --}}
<div class="alert alert-info mb-3" style="border-left: 4px solid #007bff;">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-start flex-wrap">
                <div class="flex-grow-1 mr-3">
                    <h5 class="mb-1">
                        @if(isset($region))
                            {{ $region->flag }} {{ $region->name }} - 网站管理与流量统计
                        @else
                            🌐 全部网站管理
                        @endif
                    </h5>
                    @if(isset($region))
                        <div>
                            <small class="d-block" style="color: #495057; font-weight: 500;">
                                地区代码: <span class="text-primary font-weight-bold">{{ $region->code }}</span> |
                                语言: <span class="text-success font-weight-bold">{{ $region->language }}</span>
                            </small>
                            <small class="d-block" style="color: #495057; font-weight: 500;">
                                <span id="region-current-time" class="text-primary font-weight-bold">加载中...</span>
                            </small>
                            <small class="d-block mt-1" style="color: #28a745; font-weight: 500;">
                                💡 新域名访问API后会自动出现在未通过列表中，无需手动刷新
                            </small>
                        </div>
                    @endif
                </div>
                <div class="flex-shrink-0">
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="{{ admin_url('seo/site/create') }}{{ isset($region) ? '?region_code=' . $region->code : '' }}"
                           class="btn btn-primary btn-sm btn-add-website">
                            <i class="fa fa-plus"></i> 新增网站
                        </a>
                        <button type="button" class="btn btn-success btn-sm" onclick="refreshStats()">
                            <i class="fa fa-refresh"></i> 刷新统计
                        </button>
                        <a href="{{ admin_url('seo/website-detail-stats') }}{{ isset($region) ? '?region=' . $region->code : '' }}"
                           class="btn btn-info btn-sm" target="_blank">
                            <i class="fa fa-chart-line"></i> 详细统计
                        </a>
                        @if(isset($region))
                            <a href="{{ admin_url('seo/website-management') }}" class="btn btn-secondary btn-sm">
                                <i class="fa fa-arrow-left"></i> 返回全部
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-3">
    <!-- 网站统计 -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-info">
            <div class="inner">
                <h3 id="total-sites">{{ $siteStats['total_sites'] ?? 0 }}</h3>
                <p>总网站</p>
            </div>
            <div class="icon"><i class="fa fa-globe"></i></div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-success">
            <div class="inner">
                <h3 id="active-sites">{{ $siteStats['active_sites'] ?? 0 }}</h3>
                <p>启用</p>
            </div>
            <div class="icon"><i class="fa fa-check"></i></div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-warning">
            <div class="inner">
                <h3 id="https-sites">{{ $siteStats['https_sites'] ?? 0 }}</h3>
                <p>HTTPS</p>
            </div>
            <div class="icon"><i class="fa fa-lock"></i></div>
        </div>
    </div>

    <!-- 流量统计 -->
    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-red">
            <div class="inner">
                <h3 id="today-jump">{{ number_format($regionStats['today_jump'] ?? 0) }}</h3>
                <p>今日跳转</p>
            </div>
            <div class="icon"><i class="fa fa-external-link"></i></div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-yellow">
            <div class="inner">
                <h3 id="today-spider">{{ number_format($regionStats['today_spider'] ?? 0) }}</h3>
                <p>今日蜘蛛</p>
            </div>
            <div class="icon"><i class="fa fa-bug"></i></div>
        </div>
    </div>

    <div class="col-lg-2 col-md-4 col-sm-6">
        <div class="small-box bg-purple">
            <div class="inner">
                <h3 id="total-jump">{{ number_format($regionStats['total_jump'] ?? 0) }}</h3>
                <p>总跳转</p>
            </div>
            <div class="icon"><i class="fa fa-line-chart"></i></div>
        </div>
    </div>
</div>



<script>
function refreshStats() {
    const regionCode = '{{ isset($region) ? $region->code : "all" }}';

    $.ajax({
        url: '{{ admin_url("seo/website-stats") }}',
        method: 'GET',
        data: { region: regionCode },
        success: function(response) {
            if (response.status && response.data) {
                $('#total-sites').text(response.data.total || 0);
                $('#active-sites').text(response.data.active || 0);
                $('#https-sites').text(response.data.https || 0);
                $('#today-jump').text(formatNumber(response.data.today_jump || 0));
                $('#today-spider').text(formatNumber(response.data.today_spider || 0));
                $('#total-jump').text(formatNumber(response.data.total_jump || 0));

                Dcat.success('统计数据已刷新');
            }
        },
        error: function() {
            Dcat.error('刷新统计数据失败');
        }
    });
}



function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

function updateRegionTime(timezone) {
    try {
        const now = new Date();
        const options = {
            timeZone: timezone,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };
        const timeString = now.toLocaleTimeString('zh-CN', options);
        const dateOptions = {
            timeZone: timezone,
            month: '2-digit',
            day: '2-digit'
        };
        const dateString = now.toLocaleDateString('zh-CN', dateOptions);
        $('#region-current-time').text('(' + dateString + ' ' + timeString + ')');
    } catch (error) {
        console.error('时区转换错误:', error);
        $('#region-current-time').text('(时区错误)');
    }
}

$(document).ready(function() {
    setTimeout(refreshStats, 1000);

    // 显示地区当前时间
    @if(isset($region))
        updateRegionTime('{{ $region->timezone }}');
        setInterval(function() {
            updateRegionTime('{{ $region->timezone }}');
        }, 1000);
    @endif

    // 新增网站按钮点击事件
    $('.btn-add-website').on('click', function(e) {
        var url = $(this).attr('href');
        console.log('跳转到新增网站页面:', url);
        // 可以在这里添加额外的逻辑，比如统计点击等
    });

    // 删除功能
    $(document).on('click', '.grid-row-delete', function(e) {
        e.preventDefault();
        var url = $(this).data('url');
        var id = $(this).data('id');
        var $this = $(this);
        var $row = $this.closest('tr');

        console.log('删除URL:', url);
        console.log('删除ID:', id);

        // 使用原生confirm
        if (confirm('确定要删除这个网站吗？删除后将无法恢复！')) {
            // 显示加载状态
            $this.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i> 删除中...');

            $.ajax({
                url: url,
                method: 'DELETE',
                data: {
                    '_token': '{{ csrf_token() }}'
                },
                headers: {
                    'Accept': 'application/json'
                },
                success: function(response) {
                    console.log('删除成功响应:', response);

                    if (response.status) {
                        // 显示成功消息
                        if (typeof Dcat !== 'undefined' && Dcat.success) {
                            Dcat.success(response.message || '删除成功');
                        } else {
                            alert(response.message || '删除成功');
                        }

                        // 移除表格行
                        $row.fadeOut(500, function() {
                            $(this).remove();
                            // 刷新统计数据
                            refreshStats();
                        });
                    } else {
                        throw new Error(response.message || '删除失败');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('删除失败:', xhr.responseText);

                    var errorMsg = '删除失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg += ': ' + xhr.responseJSON.message;
                    } else if (error) {
                        errorMsg += ': ' + error;
                    }

                    if (typeof Dcat !== 'undefined' && Dcat.error) {
                        Dcat.error(errorMsg);
                    } else {
                        alert(errorMsg);
                    }

                    // 恢复按钮状态
                    $this.prop('disabled', false).html('<i class="fa fa-trash"></i> 删除');
                }
            });
        }
    });
});

// 实时时间更新功能
@if(isset($region))
function updateRegionTime() {
    const timezone = '{{ $region->timezone }}';
    const regionName = '{{ $region->name }}';

    try {
        const now = new Date();
        const options = {
            timeZone: timezone,
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        };

        const formatter = new Intl.DateTimeFormat('en-US', options);
        const parts = formatter.formatToParts(now);

        // 提取各个部分
        const month = parts.find(part => part.type === 'month').value;
        const day = parts.find(part => part.type === 'day').value;
        const hour = parts.find(part => part.type === 'hour').value;
        const minute = parts.find(part => part.type === 'minute').value;
        const second = parts.find(part => part.type === 'second').value;

        // 格式化为 "地区名称时间：MM/dd HH:mm:ss"
        const timeString = `${regionName}时间：${month}/${day} ${hour}:${minute}:${second}`;

        $('#region-current-time').text(timeString);
    } catch (error) {
        console.error('时区转换错误:', error);
        $('#region-current-time').text(`${regionName}时间：时区错误`);
    }
}

// 页面加载时立即更新时间
$(document).ready(function() {
    updateRegionTime();
    // 每秒更新一次时间
    setInterval(updateRegionTime, 1000);
});
@endif
</script>
