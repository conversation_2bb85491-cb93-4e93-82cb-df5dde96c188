use actix_web::{web, HttpResponse, Result};
use serde::{Deserialize, Serialize};
use crate::entities::{AppState, Region};

#[derive(Serialize)]
pub struct RegionResponse {
    pub code: String,
    pub name: String,
    pub language: String,
    pub timezone: String,
    pub status: bool,
    pub priority: i32,
    pub site_count: i64,
    pub active_site_count: i64,
}

#[derive(Serialize)]
pub struct RegionStatsResponse {
    pub region_code: String,
    pub file_data_count: usize,
    pub app_template_count: usize,
    pub home_template_count: usize,
    pub last_updated: String,
    pub is_loaded: bool,
}

#[derive(Deserialize)]
pub struct ReloadRequest {
    pub region_code: String,
}

/// 获取所有地区列表
#[actix_web::get("/regions")]
pub async fn get_regions(app_state: web::Data<AppState>) -> Result<HttpResponse> {
    match Region::get_active_regions(&app_state.mysql).await {
        Ok(regions) => {
            let mut response_regions = Vec::new();
            
            for region in regions {
                // 获取网站统计
                let site_count: i64 = sqlx::query_scalar(
                    "SELECT COUNT(*) FROM seo_site WHERE region_code = ?"
                )
                .bind(&region.code)
                .fetch_one(&app_state.mysql)
                .await
                .unwrap_or(0);

                let active_site_count: i64 = sqlx::query_scalar(
                    "SELECT COUNT(*) FROM seo_site WHERE region_code = ? AND state = 1"
                )
                .bind(&region.code)
                .fetch_one(&app_state.mysql)
                .await
                .unwrap_or(0);
                
                response_regions.push(RegionResponse {
                    code: region.code,
                    name: region.name,
                    language: region.language,
                    timezone: region.timezone,
                    status: region.status,
                    priority: region.priority,
                    site_count,
                    active_site_count,
                });
            }
            
            Ok(HttpResponse::Ok().json(response_regions))
        }
        Err(e) => {
            eprintln!("获取地区列表失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "获取地区列表失败"
            })))
        }
    }
}

/// 获取指定地区详情
#[actix_web::get("/regions/{code}")]
pub async fn get_region(
    path: web::Path<String>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();
    
    match Region::get_by_code(&app_state.mysql, &region_code).await {
        Ok(Some(region)) => {
            // 获取网站统计
            let site_count: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM seo_site WHERE region_code = ?"
            )
            .bind(&region.code)
            .fetch_one(&app_state.mysql)
            .await
            .unwrap_or(0);

            let active_site_count: i64 = sqlx::query_scalar(
                "SELECT COUNT(*) FROM seo_site WHERE region_code = ? AND state = 1"
            )
            .bind(&region.code)
            .fetch_one(&app_state.mysql)
            .await
            .unwrap_or(0);
            
            let response = RegionResponse {
                code: region.code,
                name: region.name,
                language: region.language,
                timezone: region.timezone,
                status: region.status,
                priority: region.priority,
                site_count,
                active_site_count,
            };
            
            Ok(HttpResponse::Ok().json(response))
        }
        Ok(None) => {
            Ok(HttpResponse::NotFound().json(serde_json::json!({
                "error": "地区不存在"
            })))
        }
        Err(e) => {
            eprintln!("获取地区详情失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "error": "获取地区详情失败"
            })))
        }
    }
}

/// 重新加载地区资源
#[actix_web::post("/regions/{code}/reload")]
pub async fn reload_region(
    path: web::Path<String>,
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();
    
    // 检查地区是否存在
    match app_state.region_manager.region_exists(&region_code).await {
        true => {
            // 简化的重新加载逻辑：资源现在是按需加载的
            println!("地区 {} 资源重新加载成功（使用按需加载）", region_code);
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": format!("地区 {} 资源重新加载成功", region_code)
            })))
        }
        false => {
            Ok(HttpResponse::NotFound().json(serde_json::json!({
                "success": false,
                "message": format!("地区 {} 不存在", region_code)
            })))
        }
    }
}

/// 获取地区资源统计
#[actix_web::get("/regions/{code}/stats")]
pub async fn get_region_stats(
    path: web::Path<String>,
    _app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let region_code = path.into_inner();

    // 从内存中的资源管理器获取统计信息
    if crate::RESOURCE_MANAGER.has_region(&region_code) {
        let all_stats = crate::RESOURCE_MANAGER.get_stats();
        if let Some(region_stats) = all_stats.get(&region_code) {
            let response = RegionStatsResponse {
                region_code: region_code.clone(),
                file_data_count: region_stats.values().sum::<usize>() -
                    region_stats.get("app_templates").unwrap_or(&0) -
                    region_stats.get("home_templates").unwrap_or(&0),
                app_template_count: *region_stats.get("app_templates").unwrap_or(&0),
                home_template_count: *region_stats.get("home_templates").unwrap_or(&0),
                last_updated: chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
                is_loaded: true,
            };

            Ok(HttpResponse::Ok().json(response))
        } else {
            Ok(HttpResponse::NotFound().json(serde_json::json!({
                "error": "地区统计信息不存在"
            })))
        }
    } else {
        Ok(HttpResponse::NotFound().json(serde_json::json!({
            "error": "地区不存在"
        })))
    }
}

/// 获取所有地区资源统计
#[actix_web::get("/regions/stats")]
pub async fn get_all_region_stats(
    _app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    // 从内存中的资源管理器获取所有统计信息
    let all_stats = crate::RESOURCE_MANAGER.get_stats();
    let loaded_regions = crate::RESOURCE_MANAGER.get_loaded_regions();
    let mut response_stats = Vec::new();

    for region_code in loaded_regions {
        if let Some(region_stats) = all_stats.get(&region_code) {
            response_stats.push(RegionStatsResponse {
                region_code: region_code.clone(),
                file_data_count: region_stats.values().sum::<usize>() -
                    region_stats.get("app_templates").unwrap_or(&0) -
                    region_stats.get("home_templates").unwrap_or(&0),
                app_template_count: *region_stats.get("app_templates").unwrap_or(&0),
                home_template_count: *region_stats.get("home_templates").unwrap_or(&0),
                last_updated: chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string(),
                is_loaded: true,
            });
        }
    }

    Ok(HttpResponse::Ok().json(response_stats))
}

/// 重新加载地区管理器
#[actix_web::post("/regions/reload-manager")]
pub async fn reload_region_manager(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    match app_state.region_manager.reload().await {
        Ok(_) => {
            println!("地区管理器重新加载成功");
            Ok(HttpResponse::Ok().json(serde_json::json!({
                "success": true,
                "message": "地区管理器重新加载成功"
            })))
        }
        Err(e) => {
            eprintln!("地区管理器重新加载失败: {}", e);
            Ok(HttpResponse::InternalServerError().json(serde_json::json!({
                "success": false,
                "error": format!("重新加载失败: {}", e)
            })))
        }
    }
}

/// 获取系统信息
#[actix_web::get("/system/info")]
pub async fn get_system_info(
    app_state: web::Data<AppState>,
) -> Result<HttpResponse> {
    let region_count = app_state.region_manager.get_region_count().await;
    let default_region = app_state.region_manager.get_default_region_code();
    let all_regions = app_state.region_manager.get_all_regions().await;

    Ok(HttpResponse::Ok().json(serde_json::json!({
        "total_regions": region_count,
        "default_region": default_region,
        "loaded_regions": all_regions.len(), // 使用按需加载
        "max_regions_in_memory": "unlimited", // 按需加载，无内存限制
        "loaded_region_codes": all_regions.iter().map(|r| &r.code).collect::<Vec<_>>()
    })))
}

/// 健康检查
#[actix_web::get("/health")]
pub async fn health_check() -> Result<HttpResponse> {
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "status": "ok",
        "timestamp": chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string()
    })))
}

/// 配置地区管理路由
pub fn config_region_routes(cfg: &mut web::ServiceConfig) {
    cfg.service(
        web::scope("/api")
            .service(get_regions)
            .service(get_region)
            .service(reload_region)
            .service(get_region_stats)
            .service(get_all_region_stats)
            .service(reload_region_manager)
            .service(get_system_info)
            .service(health_check)
    );
}
