<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class WebsiteStatsAction extends AbstractTool
{
    use HasPermissions;

    /**
     * @return string
     */
    protected $title = '统计信息';

    /**
     * 处理统计信息显示
     */
    public function handle(Request $request)
    {
        try {
            // 获取地区代码
            $regionCode = $request->get('region', 'all');

            // 构建查询
            $query = DB::table('seo_site');

            if ($regionCode !== 'all') {
                $query->where('region_code', $regionCode);
            }

            // 获取统计数据
            $totalSites = $query->count();
            $activeSites = $query->where('state', 1)->count();
            $inactiveSites = $query->where('state', 0)->count();
            $httpsSites = $query->where('https', 1)->count();

            // 计算百分比
            $activeRate = $totalSites > 0 ? round(($activeSites / $totalSites) * 100, 1) : 0;
            $httpsRate = $totalSites > 0 ? round(($httpsSites / $totalSites) * 100, 1) : 0;

            // 获取地区名称
            $regionName = '全部地区';
            if ($regionCode !== 'all') {
                $region = DB::table('seo_regions')->where('code', $regionCode)->first();
                $regionName = $region ? $region->name : $regionCode;
            }

            $message = "📊 {$regionName} 统计: 总计{$totalSites}个网站，启用{$activeSites}个({$activeRate}%)，HTTPS{$httpsSites}个({$httpsRate}%)";

            return $this->response()->success($message);

        } catch (\Exception $e) {
            return $this->response()->error('获取统计信息失败: ' . $e->getMessage());
        }
    }

    /**
     * 权限检查
     */
    protected function authorize($user): bool
    {
        return true; // 根据需要调整权限
    }
}
