<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Dcat\Admin\Models\Menu;

class AddSpiderLogMenu extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'admin:add-spider-log-menu';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '添加蜘蛛日志菜单项到SEO管理菜单';

    /**
     * 创建命令
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始添加蜘蛛日志菜单项...');
        
        // 查找SEO父菜单
        $seoParent = Menu::where('title', 'SEO管理')->first();
        
        if (!$seoParent) {
            $this->error('未找到SEO管理菜单，请确保SEO管理菜单已存在');
            return 1;
        }
        
        // 检查蜘蛛日志菜单是否已存在
        $spiderLogMenu = Menu::where('title', '蜘蛛日志')->where('parent_id', $seoParent->id)->first();
        
        if (!$spiderLogMenu) {
            // 如果蜘蛛日志菜单不存在，创建一个
            Menu::create([
                'parent_id' => $seoParent->id,
                'order' => 8, // 设置一个较大的顺序值，让它排在其他SEO子菜单后面
                'title' => '蜘蛛日志',
                'icon' => 'fa-bug',
                'uri' => 'seo/spider-log',
            ]);
            
            $this->info('蜘蛛日志菜单已成功添加！');
        } else {
            $this->info('蜘蛛日志菜单已存在，无需添加');
        }
        
        return 0;
    }
} 