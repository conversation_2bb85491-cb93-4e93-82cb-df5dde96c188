<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // $schedule->command('inspire')->hourly();
        // 每1分钟运行一次refresh:total命令，更新Redis中的统计数据到数据库
        $schedule->command('refresh:total')->everyMinute();

        // 🔧 临时禁用蜘蛛日志功能，避免表不存在的错误
        // $schedule->command('spider:import')->everyThirtyMinutes();
        // $schedule->command('spider:clean --days=30')->dailyAt('03:00');
    }

    /**
     * Register the commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
