<?php

namespace App\Admin\Actions\Grid;

use App\Models\SeoRegion;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchDisableRegion extends BatchAction
{
    protected $title = '批量禁用';

    public function handle(Request $request)
    {
        $keys = $this->getKey();
        
        if (empty($keys)) {
            return $this->response()->error('请选择要禁用的地区');
        }

        try {
            // 检查是否包含默认地区
            $defaultRegions = SeoRegion::whereIn('id', $keys)
                ->whereIn('code', ['default', 'DEFAULT'])
                ->count();
                
            if ($defaultRegions > 0) {
                return $this->response()->error('不能禁用默认地区');
            }

            $count = SeoRegion::whereIn('id', $keys)->update(['status' => 0]);
            
            return $this->response()->success("成功禁用 {$count} 个地区")->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('禁用失败: ' . $e->getMessage());
        }
    }

    public function confirm()
    {
        return ['确定要禁用选中的地区吗？', '禁用后这些地区将不能被使用'];
    }
}
