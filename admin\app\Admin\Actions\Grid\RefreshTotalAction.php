<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class RefreshTotalAction extends AbstractTool
{
    /**
     * @return string
     */
	protected $title = '刷新统计数据';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        try {
            // 记录日志，以便调试
            \Log::info('开始手动执行refresh:total命令');
            
            // 执行refresh:total命令
            $exitCode = Artisan::call('refresh:total');
            
            // 获取命令输出
            $output = Artisan::output();
            
            // 记录日志
            \Log::info('refresh:total命令执行结果: ' . $exitCode);
            \Log::info('refresh:total命令输出: ' . $output);
            
            if ($exitCode === 0 || $exitCode === 1) {
                return $this->response()
                    ->success('统计数据刷新成功')
                    ->refresh();
            } else {
                return $this->response()->error('刷新失败: ' . $output);
            }
        } catch (\Exception $e) {
            \Log::error('refresh:total命令执行异常: ' . $e->getMessage());
            return $this->response()->error('刷新异常: ' . $e->getMessage());
        }
    }

    /**
     * @return string|void
     */
    public function href()
    {
        // return admin_url('auth/users');
    }

    /**
	 * @return string|array|void
	 */
	public function confirm()
	{
		return ['确认刷新', '刷新将从Redis获取最新数据更新到数据库。确定要执行吗？'];
	}

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
} 