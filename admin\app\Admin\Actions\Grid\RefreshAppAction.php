<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use App\Services\ApiConfigService;

class RefreshAppAction extends AbstractTool
{
    /**
     * @return string
     */
	protected $title = '更新缓存';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        try {
            \Log::info('开始执行完整缓存同步');

            // 执行完整的缓存同步
            $results = ApiConfigService::fullCacheSync();

            // 记录详细结果
            \Log::info('缓存同步结果:', $results);

            // 检查所有操作是否成功
            $allSuccess = true;
            $messages = [];

            if ($results['refresh'] === 'ok') {
                $messages[] = '✅ API内存缓存刷新成功';
            } else {
                $allSuccess = false;
                $messages[] = '❌ API内存缓存刷新失败: ' . $results['refresh'];
            }

            if (strpos($results['clear_sync'], '已清除') !== false) {
                $messages[] = '✅ API同步缓存清除成功';
            } else {
                $allSuccess = false;
                $messages[] = '❌ API同步缓存清除失败: ' . $results['clear_sync'];
            }

            if ($results['status_check'] === 'ok') {
                $messages[] = '✅ API服务状态正常';
            } else {
                $allSuccess = false;
                $messages[] = '❌ API服务状态异常';
            }

            // 额外验证：检查数据库状态
            $dbStats = $this->getDatabaseStats();
            $messages[] = "📊 数据库状态: {$dbStats['total_sites']}个网站, {$dbStats['active_sites']}个启用";

            $finalMessage = implode('<br>', $messages);

            if ($allSuccess) {
                \Log::info('完整缓存同步成功');
                return $this->response()
                    ->success('缓存同步成功！<br>' . $finalMessage)
                    ->refresh();
            } else {
                \Log::warning('缓存同步部分失败');
                return $this->response()
                    ->warning('缓存同步部分成功<br>' . $finalMessage)
                    ->refresh();
            }

        } catch (\Exception $e) {
            \Log::error('缓存同步失败: ' . $e->getMessage());

            // 提供更详细的错误信息
            $errorMessage = '缓存同步失败: ' . $e->getMessage();

            // 如果是API连接问题，提供额外的调试信息
            if (strpos($e->getMessage(), '无法获取API基础URL') !== false ||
                strpos($e->getMessage(), 'API调用失败') !== false) {
                $errorMessage .= '<br><br>请检查：<br>';
                $errorMessage .= '1. API项目是否正在运行<br>';
                $errorMessage .= '2. API_PATH配置是否正确<br>';
                $errorMessage .= '3. API项目.env文件中的BIND_ADDR配置';
            }

            return $this->response()->error($errorMessage);
        }
    }

    /**
     * 获取数据库统计信息
     */
    private function getDatabaseStats(): array
    {
        try {
            $totalSites = \DB::table('seo_site')->count();
            $activeSites = \DB::table('seo_site')->where('state', 1)->count();

            return [
                'total_sites' => $totalSites,
                'active_sites' => $activeSites
            ];
        } catch (\Exception $e) {
            return [
                'total_sites' => 'N/A',
                'active_sites' => 'N/A'
            ];
        }
    }

    /**
     * @return string|void
     */
    public function href()
    {
        // return admin_url('auth/users');
    }

    /**
	 * @return string|array|void
	 */
	public function confirm()
	{
		return ['确认刷新缓存', '这将同步admin后台、MySQL数据库和API内存中的所有网站状态和配置。确定要执行吗？'];
	}

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
}
