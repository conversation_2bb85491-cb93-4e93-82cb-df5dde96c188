use sqlx::{MySqlPool, Row};
use std::error::Error;
use log::info;

// 测试数据库连接
pub async fn test_database_connection(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    info!("🔗 测试数据库连接...");

    // 执行简单的查询测试连接
    let result = sqlx::query("SELECT 1 as test")
        .fetch_one(pool)
        .await?;

    let test_value: i32 = result.get("test");
    if test_value == 1 {
        info!("✅ 数据库连接测试成功");
        Ok(())
    } else {
        Err("数据库连接测试失败".into())
    }
}

pub async fn check_and_setup_database(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    info!("🔍 开始检查数据库结构...");

    // 检查并创建核心业务表
    info!("📊 检查核心业务表...");

    // 1. 优先检查seo_regions表（其他表可能依赖它）
    if let Err(e) = check_seo_regions_table(pool).await {
        eprintln!("❌ seo_regions表检查失败: {}", e);
        return Err(e);
    }

    // 2. 检查其他SEO表
    if let Err(e) = check_seo_site_table(pool).await {
        eprintln!("❌ seo_site表检查失败: {}", e);
    }
    if let Err(e) = check_seo_moban_table(pool).await {
        eprintln!("❌ seo_moban表检查失败: {}", e);
    }
    if let Err(e) = check_seo_config_table(pool).await {
        eprintln!("❌ seo_config表检查失败: {}", e);
    }
    if let Err(e) = check_seo_cache_table(pool).await {
        eprintln!("❌ seo_cache表检查失败: {}", e);
    }
    if let Err(e) = check_seo_total_table(pool).await {
        eprintln!("❌ seo_total表检查失败: {}", e);
    }
    if let Err(e) = check_spider_logs_table(pool).await {
        eprintln!("❌ spider_logs表检查失败: {}", e);
    }
    if let Err(e) = check_website_table(pool).await {
        eprintln!("❌ website表检查失败: {}", e);
    }

    // 3. 检查admin表（如果失败不中断启动）
    info!("👤 检查Admin相关表...");
    if let Err(e) = check_admin_users_table(pool).await {
        eprintln!("⚠️ admin_users表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_roles_table(pool).await {
        eprintln!("⚠️ admin_roles表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_permissions_table(pool).await {
        eprintln!("⚠️ admin_permissions表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_menu_table(pool).await {
        eprintln!("⚠️ admin_menu表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_role_users_table(pool).await {
        eprintln!("⚠️ admin_role_users表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_role_permissions_table(pool).await {
        eprintln!("⚠️ admin_role_permissions表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_permission_menu_table(pool).await {
        eprintln!("⚠️ admin_permission_menu表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_role_menu_table(pool).await {
        eprintln!("⚠️ admin_role_menu表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_settings_table(pool).await {
        eprintln!("⚠️ admin_settings表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_extensions_table(pool).await {
        eprintln!("⚠️ admin_extensions表检查失败: {} (继续启动)", e);
    }
    if let Err(e) = check_admin_extension_histories_table(pool).await {
        eprintln!("⚠️ admin_extension_histories表检查失败: {} (继续启动)", e);
    }

    info!("✅ 数据库结构检查完成");
    Ok(())
}

// 检查seo_regions表
async fn check_seo_regions_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_regions").await?;

    if !table_exists {
        info!("创建seo_regions表...");
        sqlx::query(
            "CREATE TABLE `seo_regions` (
                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地区代码',
                `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL COMMENT '地区名称',
                `language` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'en' COMMENT '语言代码',
                `timezone` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'UTC' COMMENT '时区',
                `config` json DEFAULT NULL COMMENT '地区特定配置',
                `data_version` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT '1.0' COMMENT '数据版本',
                `fallback_region` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'default' COMMENT '回退地区',
                `priority` int DEFAULT 100 COMMENT '优先级',
                `status` tinyint(1) DEFAULT 1 COMMENT '状态：1启用，0禁用',
                `jump_script` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '跳转脚本',
                `created_at` timestamp DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE KEY `uk_code` (`code`) USING BTREE,
                KEY `idx_status` (`status`) USING BTREE,
                KEY `idx_priority` (`priority`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic COMMENT='SEO地区配置表';"
        )
        .execute(pool)
        .await?;

        // 插入默认地区
        sqlx::query(
            "INSERT INTO `seo_regions` (`code`, `name`, `language`, `timezone`, `config`, `status`, `priority`) VALUES
            ('default', '默认地区', 'en', 'UTC', '{}', 1, 100),
            ('br', '巴西', 'pt', 'America/Sao_Paulo', '{\"currency\": \"BRL\", \"domain_suffix\": \".com.br\"}', 1, 100),
            ('in', '印度', 'hi', 'Asia/Kolkata', '{\"currency\": \"INR\", \"domain_suffix\": \".in\"}', 1, 100),
            ('pk', '巴基斯坦', 'en', 'Asia/Karachi', '{\"currency\": \"PKR\", \"domain_suffix\": \".pk\", \"country_code\": \"pk\"}', 1, 100),
            ('us', '美国', 'en', 'America/New_York', '{\"currency\": \"USD\", \"domain_suffix\": \".com\"}', 1, 100),
            ('other', '其他', 'en', 'UTC', '{\"currency\": \"USD\", \"domain_suffix\": \".com\", \"country_code\": \"XX\"}', 1, 999)"
        )
        .execute(pool)
        .await?;

        info!("seo_regions表创建成功");
    } else {
        // 检查是否存在jump_script字段，如果不存在则添加
        let column_exists = check_column_exists(pool, "seo_regions", "jump_script").await?;
        if !column_exists {
            info!("添加jump_script字段到seo_regions表...");
            sqlx::query(
                "ALTER TABLE `seo_regions` ADD COLUMN `jump_script` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '跳转脚本' AFTER `status`"
            )
            .execute(pool)
            .await?;
            info!("jump_script字段添加成功");
        }

        // 确保默认地区记录存在
        ensure_default_regions(pool).await?;
    }

    Ok(())
}

// 确保默认地区记录存在
async fn ensure_default_regions(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查默认地区是否存在
    let default_exists = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM seo_regions WHERE code = 'default'"
    )
    .fetch_one(pool)
    .await? > 0;

    if !default_exists {
        info!("添加默认地区记录...");
        sqlx::query(
            "INSERT INTO `seo_regions` (`code`, `name`, `language`, `timezone`, `config`, `status`) VALUES
            ('default', '默认地区', 'en', 'UTC', '{}', 1)"
        )
        .execute(pool)
        .await?;
        info!("默认地区记录添加成功");
    }

    // 检查其他常用地区是否存在
    let regions_to_check = vec![
        ("br", "巴西", "pt", "America/Sao_Paulo", r#"{"currency": "BRL", "domain_suffix": ".com.br"}"#),
        ("in", "印度", "hi", "Asia/Kolkata", r#"{"currency": "INR", "domain_suffix": ".in"}"#),
        ("pk", "巴基斯坦", "ur", "Asia/Karachi", r#"{"currency": "PKR", "domain_suffix": ".com.pk"}"#),
        ("us", "美国", "en", "America/New_York", r#"{"currency": "USD", "domain_suffix": ".com"}"#),
    ];

    for (code, name, language, timezone, config) in regions_to_check {
        let exists = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM seo_regions WHERE code = ?"
        )
        .bind(code)
        .fetch_one(pool)
        .await? > 0;

        if !exists {
            info!("添加地区记录: {} ({})", name, code);
            sqlx::query(
                "INSERT INTO `seo_regions` (`code`, `name`, `language`, `timezone`, `config`, `status`) VALUES (?, ?, ?, ?, ?, 1)"
            )
            .bind(code)
            .bind(name)
            .bind(language)
            .bind(timezone)
            .bind(config)
            .execute(pool)
            .await?;
        }
    }

    Ok(())
}

// 检查seo_site表
async fn check_seo_site_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_site").await?;
    
    if !table_exists {
        info!("创建seo_site表...");
        sqlx::query(
            "CREATE TABLE `seo_site` (
                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `https` tinyint(1) NULL DEFAULT 0,
                `open_cache` tinyint(1) NULL DEFAULT 0,
                `open_page` tinyint(1) NULL DEFAULT 0,
                `open_home` tinyint(1) NULL DEFAULT 0,
                `open_link` tinyint(1) NULL DEFAULT 0,
                `state` tinyint(1) NULL DEFAULT 0,
                `link_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
                `jump_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
                `region_code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT 'default' COMMENT '地区代码',
                `last_time` bigint(20) NULL DEFAULT 0,
                `last_sync` bigint(20) NULL DEFAULT 0,
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `host`(`host`) USING BTREE,
                INDEX `idx_region_state`(`region_code`, `state`) USING BTREE,
                INDEX `idx_region_host`(`region_code`, `host`) USING BTREE,
                FOREIGN KEY (`region_code`) REFERENCES `seo_regions`(`code`) ON DELETE SET NULL ON UPDATE CASCADE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("seo_site表创建成功");
    } else {
        // 检查region_code字段是否存在
        let region_code_exists = check_column_exists(pool, "seo_site", "region_code").await?;
        if !region_code_exists {
            info!("添加region_code字段到seo_site表...");
            sqlx::query("ALTER TABLE seo_site ADD COLUMN region_code VARCHAR(10) DEFAULT 'default' COMMENT '地区代码'")
                .execute(pool)
                .await?;

            // 添加索引
            sqlx::query("CREATE INDEX idx_region_state ON seo_site(region_code, state)")
                .execute(pool)
                .await
                .ok(); // 忽略索引已存在的错误

            sqlx::query("CREATE INDEX idx_region_host ON seo_site(region_code, host)")
                .execute(pool)
                .await
                .ok(); // 忽略索引已存在的错误

            info!("region_code字段添加成功");
        }

        // 更新现有记录的region_code字段
        update_existing_site_region_codes(pool).await?;
        // 检查last_sync字段是否存在
        let last_sync_exists = check_column_exists(pool, "seo_site", "last_sync").await?;
        if !last_sync_exists {
            info!("添加last_sync字段到seo_site表...");
            sqlx::query("ALTER TABLE seo_site ADD COLUMN last_sync BIGINT NOT NULL DEFAULT 0")
                .execute(pool)
                .await?;
            info!("last_sync字段添加成功");
        }
        
        // 检查last_time字段是否存在
        let last_time_exists = check_column_exists(pool, "seo_site", "last_time").await?;
        if !last_time_exists {
            info!("添加last_time字段到seo_site表...");
            sqlx::query("ALTER TABLE seo_site ADD COLUMN last_time BIGINT NOT NULL DEFAULT 0")
                .execute(pool)
                .await?;
            info!("last_time字段添加成功");
        }
    }
    
    Ok(())
}

// 更新现有站点的region_code字段
async fn update_existing_site_region_codes(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    info!("更新现有站点的region_code字段...");

    // 1. 更新所有region_code为NULL或空字符串的记录为'default'
    let updated_null = sqlx::query(
        "UPDATE seo_site SET region_code = 'default' WHERE region_code IS NULL OR region_code = ''"
    )
    .execute(pool)
    .await?;

    if updated_null.rows_affected() > 0 {
        info!("更新了 {} 个空region_code记录为'default'", updated_null.rows_affected());
    }

    // 🔧 移除域名自动分配功能，避免外键约束问题
    // 所有网站都使用default地区，由admin后台手动管理地区分配

    // 3. 统计更新结果
    let total_sites = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM seo_site")
        .fetch_one(pool)
        .await?;
    let default_sites = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM seo_site WHERE region_code = 'default'")
        .fetch_one(pool)
        .await?;
    let assigned_sites = total_sites - default_sites;

    info!("地区代码更新完成:");
    info!("- 总网站数: {}", total_sites);
    info!("- 已分配地区: {}", assigned_sites);
    info!("- 默认地区: {}", default_sites);

    Ok(())
}

// 检查seo_moban表
async fn check_seo_moban_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_moban").await?;
    
    if !table_exists {
        info!("创建seo_moban表...");
        sqlx::query(
            "CREATE TABLE `seo_moban` (
                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `open_cache` tinyint(1) NULL DEFAULT 0,
                `open_page` tinyint(1) NULL DEFAULT 0,
                `open_home` tinyint(1) NULL DEFAULT 0,
                `open_link` tinyint(1) NULL DEFAULT 0,
                `link_rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("seo_moban表创建成功");
        
        // 添加默认模板记录，更新为指定的配置
        sqlx::query(
            "INSERT INTO `seo_moban` (`name`, `open_cache`, `open_page`, `open_home`, `open_link`, `link_rules`) 
            VALUES ('dll', 1, 1, 0, 1, '/gameing-{关键词}/\n/betting-{关键词}/')"
        )
        .execute(pool)
        .await?;
        info!("添加第一个默认模板记录成功");
        
        // 添加第二个默认模板记录
        sqlx::query(
            "INSERT INTO `seo_moban` (`name`, `open_cache`, `open_page`, `open_home`, `open_link`, `link_rules`) 
            VALUES ('关键词', 1, 1, 0, 1, '?key={关键词}\n?search={关键词}\n?q={关键词}\n?s={关键词}\n?keywords={关键词}\n?pamr={关键词}\n?video={关键词}\n?id={关键词}\n?post={关键词}\n?play={关键词}\n?iso={关键词}\n?ios={关键词}\n?patt={关键词}\n?games={关键词}\n?blank={关键词}\n?casino={关键词}\n?download={关键词}')"
        )
        .execute(pool)
        .await?;
        info!("添加第二个默认模板记录成功");
    }
    
    Ok(())
}

// 检查seo_config表
async fn check_seo_config_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_config").await?;

    if !table_exists {
        info!("创建seo_config表...");
        sqlx::query(
            "CREATE TABLE `seo_config` (
                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                `jump_script` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '跳转脚本',
                `link_fixed` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '固定链接',
                `link_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL COMMENT '链接列表',
                `link_num` int(11) NULL DEFAULT 0 COMMENT '链接数量',
                `link_total` int(11) NULL DEFAULT 30 COMMENT '总链接数',
                `sitemap_num` int(11) NULL DEFAULT 1000 COMMENT 'sitemap数量',
                `link_site_num` int(11) NULL DEFAULT 30 COMMENT '链接站点数',
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("seo_config表创建成功");
        
        // 添加默认配置数据
        info!("添加默认配置数据...");
        sqlx::query(
            "INSERT INTO `seo_config` (jump_script, link_fixed, link_list, link_num, link_total, sitemap_num, link_site_num)
            VALUES (?, ?, ?, ?, ?, ?, ?)"
        )
        .bind("<html>
<head>
<meta name=\"viewport\" content=\"width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0\">
<meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\">
<script charset=\"UTF-8\" id=\"LA_COLLECT\" src=\"//sdk.51.la/js-sdk-pro.min.js\"></script>
<script>LA.init({id:\"KivEzLEnoI51ioGx\",ck:\"KivEzLEnoI51ioGx\"})</script>
</head>
<body>
<center>
<br><br>
<h2>
🎮 Hızlı giriş yapıyorsunuz, lütfen bekleyiniz. . . . . .
</h2>
</center>
<center>
<script type=\"text/javascript\">
function jumurl(){
window.location.href = \"https://mklGi.nazobet-agent.com\";
}
setTimeout(jumurl,500);
</script>
</center>
</body>
</html>")
        .bind("")  // link_fixed
        .bind("")  // link_list
        .bind(0)   // link_num
        .bind(30)  // link_total
        .bind(1000) // sitemap_num
        .bind(30)  // link_site_num
        .execute(pool)
        .await?;
        
        info!("添加默认配置成功");
    }
    
    Ok(())
}

// 检查seo_cache表
async fn check_seo_cache_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_cache").await?;
    
    if !table_exists {
        info!("创建seo_cache表...");
        sqlx::query(
            "CREATE TABLE `seo_cache` (
                `id` bigint(20) NOT NULL AUTO_INCREMENT,
                `host` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `uri` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `hash` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL,
                `expired` int(11) NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `hash`(`hash`) USING BTREE,
                INDEX `host`(`host`) USING BTREE,
                INDEX `expired`(`expired`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("seo_cache表创建成功");
    }
    
    Ok(())
}

// 检查seo_total表
async fn check_seo_total_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "seo_total").await?;

    if !table_exists {
        info!("创建seo_total表...");
        sqlx::query(
            "CREATE TABLE `seo_total` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `site` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL,
                `date` int(11) NULL DEFAULT NULL,
                `jump` int(11) NULL DEFAULT NULL,
                `spider` int(11) NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `idx_site_date`(`site`, `date`) USING BTREE,
                INDEX `idx_date`(`date`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("seo_total表创建成功");
    } else {
        // 检查并添加缺失的字段
        let date_exists = check_column_exists(pool, "seo_total", "date").await?;
        if !date_exists {
            info!("添加date字段到seo_total表...");
            sqlx::query("ALTER TABLE seo_total ADD COLUMN date INT(11) NULL DEFAULT NULL")
                .execute(pool)
                .await?;
        }

        let jump_exists = check_column_exists(pool, "seo_total", "jump").await?;
        if !jump_exists {
            info!("添加jump字段到seo_total表...");
            sqlx::query("ALTER TABLE seo_total ADD COLUMN jump INT(11) NULL DEFAULT NULL")
                .execute(pool)
                .await?;
        }

        let spider_exists = check_column_exists(pool, "seo_total", "spider").await?;
        if !spider_exists {
            info!("添加spider字段到seo_total表...");
            sqlx::query("ALTER TABLE seo_total ADD COLUMN spider INT(11) NULL DEFAULT NULL")
                .execute(pool)
                .await?;
        }

        // 检查site字段是否存在（可能原来是host字段）
        let site_exists = check_column_exists(pool, "seo_total", "site").await?;
        let host_exists = check_column_exists(pool, "seo_total", "host").await?;

        if !site_exists && host_exists {
            info!("将host字段重命名为site...");
            sqlx::query("ALTER TABLE seo_total CHANGE COLUMN host site VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL")
                .execute(pool)
                .await?;
        } else if !site_exists && !host_exists {
            info!("添加site字段到seo_total表...");
            sqlx::query("ALTER TABLE seo_total ADD COLUMN site VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NULL DEFAULT NULL")
                .execute(pool)
                .await?;
        }

        // 删除不需要的total字段（如果存在）
        let total_exists = check_column_exists(pool, "seo_total", "total").await?;
        if total_exists {
            info!("删除不需要的total字段...");
            sqlx::query("ALTER TABLE seo_total DROP COLUMN total")
                .execute(pool)
                .await
                .ok(); // 忽略可能的错误
        }

        // 添加索引
        sqlx::query("CREATE INDEX idx_site_date ON seo_total(site, date)")
            .execute(pool)
            .await
            .ok(); // 忽略索引已存在的错误

        sqlx::query("CREATE INDEX idx_date ON seo_total(date)")
            .execute(pool)
            .await
            .ok(); // 忽略索引已存在的错误

        info!("seo_total表结构更新完成");
    }

    Ok(())
}

// 检查website表
async fn check_website_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "website").await?;
    
    if !table_exists {
        info!("创建website表...");
        sqlx::query(
            "CREATE TABLE `website` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
                `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        info!("website表创建成功");
    }
    
    Ok(())
}

// 检查admin_users表
async fn check_admin_users_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_users").await?;
    
    if !table_exists {
        info!("创建admin_users表...");
        sqlx::query(
            "CREATE TABLE `admin_users` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `username` varchar(120) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `password` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `remember_token` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `admin_users_username_unique`(`username`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        // 添加默认管理员账户（基于guo.sql）
        sqlx::query(
            "INSERT IGNORE INTO `admin_users` (`id`, `username`, `password`, `name`, `avatar`, `remember_token`, `created_at`, `updated_at`)
            VALUES (1, 'admin', '$2y$10$ovEaCmshNFW8I8s89rL7ku7Xj.GcTqW1o3uSE61waLbqIVj3aYAKm', 'Administrator', NULL, 'ksMcPwVUJqnZp6LWuBeExASbsJaEZMLId0anIvq2rMMivEREOrINtIHrdEp2', '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;

        // 添加默认菜单数据
        info!("添加默认菜单数据...");

        // 插入主菜单项（更新后的菜单结构）
        sqlx::query(
            "INSERT IGNORE INTO `admin_menu` (`id`, `parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
            (1, 0, 1, 'Index', 'feather icon-bar-chart-2', '/', '', 1, '2024-05-05 09:15:48', NULL),
            (2, 0, 7, 'Admin', 'feather icon-settings', '', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (3, 2, 8, 'Users', NULL, 'auth/users', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (4, 2, 9, 'Roles', '', 'auth/roles', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (5, 2, 10, 'Permission', '', 'auth/permissions', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (6, 2, 11, 'Menu', '', 'auth/menu', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (7, 2, 12, 'Extensions', '', 'auth/extensions', '', 1, '2024-05-05 09:15:48', '2024-05-08 13:19:15'),
            (8, 0, 2, 'SEO管理', 'fa-globe', '', '', 1, '2024-05-05 09:33:11', '2024-05-05 09:33:59'),
            (9, 8, 3, '配置', 'fa-circle-thin', 'seo/config', '', 1, '2024-05-05 10:27:08', '2024-05-08 13:19:15'),
            (10, 8, 4, '网站', 'fa-circle-thin', 'seo/site', '', 1, '2024-05-05 12:45:59', '2024-05-08 13:19:15'),
            (11, 8, 5, '缓存', 'fa-circle-thin', 'seo/cache', '', 1, '2024-05-05 13:27:44', '2024-05-08 13:19:15'),
            (12, 0, 6, '站群管理', 'fa-bitcoin', '', '', 1, '2024-05-08 13:19:00', '2024-07-19 08:52:35'),
            (13, 12, 13, '网站管理', 'fa-circle-thin', 'website/list', '', 1, '2024-05-08 13:20:19', '2024-05-16 10:13:16'),
            (14, 8, 14, '统计', 'fa-circle-thin', 'seo/total', '', 1, '2024-07-18 13:56:54', '2024-07-18 13:56:54'),
            (15, 8, 15, '规则模板', 'fa-circle-thin', 'seo/moban', '', 1, '2024-07-19 08:52:29', '2024-07-19 08:52:29'),
            (16, 0, 3, '地区管理', 'fa-globe', '', '', 1, NOW(), NOW()),
            (17, 16, 1, '地区配置', 'fa-cog', 'seo/regions', '', 1, NOW(), NOW()),
            (18, 16, 2, '地区统计', 'fa-bar-chart', 'seo/regions-stats', '', 1, NOW(), NOW()),
            (19, 8, 16, '蜘蛛日志', 'fa-bug', 'seo/spider-log', '', 1, NOW(), NOW()),
            (20, 12, 14, '网站管理', 'fa-sitemap', '', '', 1, NOW(), NOW()),
            (21, 20, 1, '全部网站', 'fa-globe', 'seo/website-management', '', 1, NOW(), NOW())"
        )
        .execute(pool)
        .await?;
        
        info!("admin_menu表创建成功");
    } else {
        // 如果表已存在，检查并更新菜单结构
        info!("检查并更新admin_menu表结构...");
        update_admin_menu_structure(pool).await?;
    }

    Ok(())
}

// 更新admin_menu表结构
async fn update_admin_menu_structure(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 1. 检查是否存在独立的地区管理菜单
    let region_main_menu_exists = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM admin_menu WHERE title = '地区管理' AND parent_id = 0"
    )
    .fetch_one(pool)
    .await? > 0;

    if !region_main_menu_exists {
        info!("创建独立的地区管理菜单结构...");

        // 2. 查找SEO管理菜单
        let seo_menu_id: Option<i64> = sqlx::query_scalar(
            "SELECT id FROM admin_menu WHERE title IN ('SEO管理', 'Seo') AND parent_id = 0 LIMIT 1"
        )
        .fetch_optional(pool)
        .await?;

        if let Some(seo_id) = seo_menu_id {
            // 3. 查找SEO下的地区管理菜单
            let old_region_menu_id: Option<i64> = sqlx::query_scalar(
                "SELECT id FROM admin_menu WHERE title = '地区管理' AND parent_id = ?"
            )
            .bind(seo_id)
            .fetch_optional(pool)
            .await?;

            // 4. 创建独立的地区管理主菜单
            sqlx::query(
                "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                VALUES (0, 3, '地区管理', 'fa-globe', '', '', 1, NOW(), NOW())"
            )
            .execute(pool)
            .await?;

            let new_region_menu_id = sqlx::query_scalar::<_, i64>(
                "SELECT LAST_INSERT_ID()"
            )
            .fetch_one(pool)
            .await?;

            // 5. 如果存在旧的地区管理菜单，移动其子菜单
            if let Some(old_id) = old_region_menu_id {
                sqlx::query(
                    "UPDATE admin_menu SET parent_id = ? WHERE parent_id = ?"
                )
                .bind(new_region_menu_id)
                .bind(old_id)
                .execute(pool)
                .await?;

                // 删除旧的地区管理菜单
                sqlx::query("DELETE FROM admin_menu WHERE id = ?")
                    .bind(old_id)
                    .execute(pool)
                    .await?;
            }

            // 6. 确保地区管理子菜单存在
            let region_config_exists = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM admin_menu WHERE title = '地区配置' AND parent_id = ?"
            )
            .bind(new_region_menu_id)
            .fetch_one(pool)
            .await? > 0;

            if !region_config_exists {
                sqlx::query(
                    "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                    VALUES (?, 1, '地区配置', 'fa-cog', 'seo/regions', '', 1, NOW(), NOW())"
                )
                .bind(new_region_menu_id)
                .execute(pool)
                .await?;
            }

            let region_stats_exists = sqlx::query_scalar::<_, i64>(
                "SELECT COUNT(*) FROM admin_menu WHERE title = '地区统计' AND parent_id = ?"
            )
            .bind(new_region_menu_id)
            .fetch_one(pool)
            .await? > 0;

            if !region_stats_exists {
                sqlx::query(
                    "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                    VALUES (?, 2, '地区统计', 'fa-bar-chart', 'seo/regions-stats', '', 1, NOW(), NOW())"
                )
                .bind(new_region_menu_id)
                .execute(pool)
                .await?;
            }

            info!("地区管理菜单结构更新完成");
        }
    }

    // 7. 检查并添加蜘蛛日志菜单
    let spider_log_exists = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM admin_menu WHERE title = '蜘蛛日志'"
    )
    .fetch_one(pool)
    .await? > 0;

    if !spider_log_exists {
        if let Some(seo_id) = sqlx::query_scalar::<_, i64>(
            "SELECT id FROM admin_menu WHERE title IN ('SEO管理', 'Seo') AND parent_id = 0 LIMIT 1"
        )
        .fetch_optional(pool)
        .await? {
            sqlx::query(
                "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                VALUES (?, 16, '蜘蛛日志', 'fa-bug', 'seo/spider-log', '', 1, NOW(), NOW())"
            )
            .bind(seo_id)
            .execute(pool)
            .await?;
            info!("蜘蛛日志菜单添加完成");
        }
    }

    // 8. 检查并添加网站管理菜单结构
    let website_management_exists = sqlx::query_scalar::<_, i64>(
        "SELECT COUNT(*) FROM admin_menu WHERE uri = 'seo/website-management'"
    )
    .fetch_one(pool)
    .await? > 0;

    if !website_management_exists {
        if let Some(website_group_id) = sqlx::query_scalar::<_, i64>(
            "SELECT id FROM admin_menu WHERE title = '站群管理' AND parent_id = 0 LIMIT 1"
        )
        .fetch_optional(pool)
        .await? {
            // 创建网站管理子菜单组
            sqlx::query(
                "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                VALUES (?, 14, '网站管理', 'fa-sitemap', '', '', 1, NOW(), NOW())"
            )
            .bind(website_group_id)
            .execute(pool)
            .await?;

            let website_menu_id = sqlx::query_scalar::<_, i64>(
                "SELECT LAST_INSERT_ID()"
            )
            .fetch_one(pool)
            .await?;

            // 添加全部网站菜单
            sqlx::query(
                "INSERT INTO admin_menu (parent_id, `order`, title, icon, uri, extension, `show`, created_at, updated_at)
                VALUES (?, 1, '全部网站', 'fa-globe', 'seo/website-management', '', 1, NOW(), NOW())"
            )
            .bind(website_menu_id)
            .execute(pool)
            .await?;

            info!("网站管理菜单结构添加完成");
        }
    }

    Ok(())
}

// 检查admin_roles表
async fn check_admin_roles_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_roles").await?;
    
    if !table_exists {
        info!("创建admin_roles表...");
        sqlx::query(
            "CREATE TABLE `admin_roles` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `admin_roles_slug_unique`(`slug`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;

        // 添加默认权限数据
        info!("添加默认权限数据...");
        sqlx::query(
            "INSERT IGNORE INTO `admin_permissions` (`id`, `name`, `slug`, `http_method`, `http_path`, `order`, `parent_id`, `created_at`, `updated_at`) VALUES
            (1, 'Auth management', 'auth-management', '', '', 1, 0, '2024-05-05 09:15:48', NULL),
            (2, 'Users', 'users', '', '/auth/users*', 2, 1, '2024-05-05 09:15:48', NULL),
            (3, 'Roles', 'roles', '', '/auth/roles*', 3, 1, '2024-05-05 09:15:48', NULL),
            (4, 'Permissions', 'permissions', '', '/auth/permissions*', 4, 1, '2024-05-05 09:15:48', NULL),
            (5, 'Menu', 'menu', '', '/auth/menu*', 5, 1, '2024-05-05 09:15:48', NULL),
            (6, 'Extension', 'extension', '', '/auth/extensions*', 6, 1, '2024-05-05 09:15:48', NULL)"
        )
        .execute(pool)
        .await?;

        // 添加默认角色
        sqlx::query(
            "INSERT IGNORE INTO `admin_roles` (`id`, `name`, `slug`, `created_at`, `updated_at`)
            VALUES (1, 'Administrator', 'administrator', '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;

        // 添加用户角色关联
        info!("添加用户角色关联...");
        sqlx::query(
            "INSERT IGNORE INTO `admin_role_users` (`role_id`, `user_id`, `created_at`, `updated_at`)
            VALUES (1, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;

        // 添加角色权限关联（给Administrator角色所有权限）
        info!("添加角色权限关联...");
        sqlx::query(
            "INSERT IGNORE INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`) VALUES
            (1, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (1, 2, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (1, 3, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (1, 4, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (1, 5, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (1, 6, '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;

        info!("admin_roles表和关联数据创建成功");
    }

    Ok(())
}

// 检查admin_permissions表
async fn check_admin_permissions_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_permissions").await?;
    
    if !table_exists {
        info!("创建admin_permissions表...");
        sqlx::query(
            "CREATE TABLE `admin_permissions` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `slug` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `http_method` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `http_path` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `admin_permissions_slug_unique`(`slug`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        // 添加默认权限（基于guo.sql）
        sqlx::query(
            "INSERT INTO `admin_permissions` (`id`, `name`, `slug`, `http_method`, `http_path`, `created_at`, `updated_at`)
            VALUES (1, 'All permission', '*', '', '*', '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (2, 'Dashboard', 'dashboard', 'GET', '/', '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;
        
        info!("admin_permissions表创建成功");
    }
    
    Ok(())
}

// 检查admin_menu表
async fn check_admin_menu_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_menu").await?;
    
    if !table_exists {
        info!("创建admin_menu表...");
        sqlx::query(
            "CREATE TABLE `admin_menu` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `parent_id` bigint(20) NOT NULL DEFAULT 0,
                `order` int(11) NOT NULL DEFAULT 0,
                `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `uri` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `extension` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
                `show` tinyint(4) NOT NULL DEFAULT 1,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        // 添加完整的菜单数据（基于guo.sql）
        sqlx::query(
            "INSERT IGNORE INTO `admin_menu` (`id`, `parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
            (1, 0, 1, 'Dashboard', 'feather icon-bar-chart-2', '/', '', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
            (8, 0, 2, 'Seo', 'fa-globe', '', '', 1, '2024-05-05 09:33:11', '2024-05-05 09:33:59'),
            (9, 8, 3, '配置', 'fa-circle-thin', 'seo/config', '', 1, '2024-05-05 10:27:08', '2024-05-08 13:19:15'),
            (10, 8, 4, '网站', 'fa-circle-thin', 'seo/site', '', 1, '2024-05-05 12:45:59', '2024-05-08 13:19:15'),
            (11, 8, 5, '缓存', 'fa-circle-thin', 'seo/cache', '', 1, '2024-05-05 13:27:44', '2024-05-08 13:19:15'),
            (14, 8, 14, '统计', 'fa-circle-thin', 'seo/total', '', 1, '2024-07-18 13:56:54', '2024-07-18 13:56:54'),
            (15, 8, 15, '规则模板', 'fa-circle-thin', 'seo/moban', '', 1, '2024-07-19 08:52:29', '2024-07-19 08:52:29'),
            (17, 0, 15, '网站管理', 'fa-sitemap', '', '', 1, '2025-07-05 17:21:48', '2025-07-05 17:21:48'),
            (18, 17, 1, '全部网站', 'fa-globe', 'seo/website-management', '', 1, '2025-07-05 17:21:48', '2025-07-05 17:21:48'),
            (19, 17, 2, '🇧🇷 巴西', 'fa-circle-thin', 'seo/website-management?region=br', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
            (20, 17, 3, '🌍 默认地区', 'fa-circle-thin', 'seo/website-management?region=default', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
            (21, 17, 4, '🇮🇳 印度', 'fa-circle-thin', 'seo/website-management?region=in', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
            (22, 17, 5, '🇵🇰 巴基斯坦', 'fa-circle-thin', 'seo/website-management?region=pk', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
            (23, 0, 3, '地区管理', 'fa-globe', '', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
            (24, 23, 1, '地区配置', 'fa-cog', 'seo/regions', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
            (25, 23, 2, '地区统计', 'fa-bar-chart', 'seo/regions-stats', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
            (26, 8, 16, '蜘蛛日志', 'fa-bug', 'seo/spider-log', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
            (27, 17, 6, '🇺🇸 美国', 'fa-circle-thin', 'seo/website-management?region=us', '', 1, '2025-07-07 11:18:24', '2025-07-07 21:31:10'),
            (28, 17, 7, '🌍 其他', 'fa-circle-thin', 'seo/website-management?region=other', '', 1, '2025-07-07 21:31:10', '2025-07-07 21:31:10')"
        )
        .execute(pool)
        .await?;
        
        info!("admin_menu表创建成功");
    } else {
        // 表存在，检查是否有完整的菜单数据
        let menu_count = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM admin_menu")
            .fetch_one(pool)
            .await?;

        // 检查是否有SEO菜单（ID=8），如果没有说明菜单数据不完整
        let seo_menu_exists = sqlx::query_scalar::<_, i64>("SELECT COUNT(*) FROM admin_menu WHERE id = 8")
            .fetch_one(pool)
            .await?;

        if menu_count == 0 || seo_menu_exists == 0 {
            if menu_count > 0 && seo_menu_exists == 0 {
                info!("admin_menu表数据不完整，清空并重新添加完整菜单数据...");
                sqlx::query("DELETE FROM admin_menu").execute(pool).await?;
            } else {
                info!("admin_menu表为空，添加默认菜单数据...");
            }

            // 添加完整的菜单数据（基于guo.sql）
            sqlx::query(
                "INSERT INTO `admin_menu` (`id`, `parent_id`, `order`, `title`, `icon`, `uri`, `extension`, `show`, `created_at`, `updated_at`) VALUES
                (1, 0, 1, 'Dashboard', 'feather icon-bar-chart-2', '/', '', 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00'),
                (8, 0, 2, 'Seo', 'fa-globe', '', '', 1, '2024-05-05 09:33:11', '2024-05-05 09:33:59'),
                (9, 8, 3, '配置', 'fa-circle-thin', 'seo/config', '', 1, '2024-05-05 10:27:08', '2024-05-08 13:19:15'),
                (10, 8, 4, '网站', 'fa-circle-thin', 'seo/site', '', 1, '2024-05-05 12:45:59', '2024-05-08 13:19:15'),
                (11, 8, 5, '缓存', 'fa-circle-thin', 'seo/cache', '', 1, '2024-05-05 13:27:44', '2024-05-08 13:19:15'),
                (14, 8, 14, '统计', 'fa-circle-thin', 'seo/total', '', 1, '2024-07-18 13:56:54', '2024-07-18 13:56:54'),
                (15, 8, 15, '规则模板', 'fa-circle-thin', 'seo/moban', '', 1, '2024-07-19 08:52:29', '2024-07-19 08:52:29'),
                (17, 0, 15, '网站管理', 'fa-sitemap', '', '', 1, '2025-07-05 17:21:48', '2025-07-05 17:21:48'),
                (18, 17, 1, '全部网站', 'fa-globe', 'seo/website-management', '', 1, '2025-07-05 17:21:48', '2025-07-05 17:21:48'),
                (19, 17, 2, '🇧🇷 巴西', 'fa-circle-thin', 'seo/website-management?region=br', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
                (20, 17, 3, '🌍 默认地区', 'fa-circle-thin', 'seo/website-management?region=default', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
                (21, 17, 4, '🇮🇳 印度', 'fa-circle-thin', 'seo/website-management?region=in', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
                (22, 17, 5, '🇵🇰 巴基斯坦', 'fa-circle-thin', 'seo/website-management?region=pk', '', 1, '2025-07-05 17:21:48', '2025-07-07 21:31:10'),
                (23, 0, 3, '地区管理', 'fa-globe', '', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
                (24, 23, 1, '地区配置', 'fa-cog', 'seo/regions', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
                (25, 23, 2, '地区统计', 'fa-bar-chart', 'seo/regions-stats', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
                (26, 8, 16, '蜘蛛日志', 'fa-bug', 'seo/spider-log', '', 1, '2025-07-06 11:32:34', '2025-07-06 11:32:34'),
                (27, 17, 6, '🇺🇸 美国', 'fa-circle-thin', 'seo/website-management?region=us', '', 1, '2025-07-07 11:18:24', '2025-07-07 21:31:10'),
                (28, 17, 7, '🌍 其他', 'fa-circle-thin', 'seo/website-management?region=other', '', 1, '2025-07-07 21:31:10', '2025-07-07 21:31:10')"
            )
            .execute(pool)
            .await?;
            info!("完整菜单数据添加成功");
        } else {
            info!("admin_menu表已有完整数据，跳过菜单初始化");
        }
    }

    Ok(())
}

// 检查admin_role_users表
async fn check_admin_role_users_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_role_users").await?;
    
    if !table_exists {
        info!("创建admin_role_users表...");
        sqlx::query(
            "CREATE TABLE `admin_role_users` (
                `role_id` bigint(20) UNSIGNED NOT NULL,
                `user_id` bigint(20) UNSIGNED NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                INDEX `admin_role_users_role_id_user_id_index`(`role_id`, `user_id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        // 添加默认角色用户关联
        sqlx::query(
            "INSERT INTO `admin_role_users` (`role_id`, `user_id`, `created_at`, `updated_at`) 
            VALUES (1, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;
        
        info!("admin_role_users表创建成功");
    }
    
    Ok(())
}

// 检查admin_role_permissions表
async fn check_admin_role_permissions_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_role_permissions").await?;
    
    if !table_exists {
        info!("创建admin_role_permissions表...");
        sqlx::query(
            "CREATE TABLE `admin_role_permissions` (
                `role_id` bigint(20) UNSIGNED NOT NULL,
                `permission_id` bigint(20) UNSIGNED NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                INDEX `admin_role_permissions_role_id_permission_id_index`(`role_id`, `permission_id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        // 添加默认角色权限关联
        sqlx::query(
            "INSERT INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`) 
            VALUES (1, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
        )
        .execute(pool)
        .await?;
        
        info!("admin_role_permissions表创建成功");
    } else {
        // 表存在，检查管理员角色是否有全部权限
        let admin_permission_count = sqlx::query_scalar::<_, i64>(
            "SELECT COUNT(*) FROM admin_role_permissions WHERE role_id = 1 AND permission_id = 1"
        )
        .fetch_one(pool)
        .await?;

        if admin_permission_count == 0 {
            info!("添加管理员角色的全部权限...");
            sqlx::query(
                "INSERT IGNORE INTO `admin_role_permissions` (`role_id`, `permission_id`, `created_at`, `updated_at`)
                VALUES (1, 1, '2024-01-01 00:00:00', '2024-01-01 00:00:00')"
            )
            .execute(pool)
            .await?;
            info!("管理员权限关联添加成功");
        }
    }

    Ok(())
}

// 检查admin_permission_menu表
async fn check_admin_permission_menu_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_permission_menu").await?;
    
    if !table_exists {
        info!("创建admin_permission_menu表...");
        sqlx::query(
            "CREATE TABLE `admin_permission_menu` (
                `permission_id` bigint(20) UNSIGNED NOT NULL,
                `menu_id` bigint(20) UNSIGNED NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                INDEX `admin_permission_menu_permission_id_menu_id_index`(`permission_id`, `menu_id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        info!("admin_permission_menu表创建成功");
    }
    
    Ok(())
}

// 检查admin_role_menu表
async fn check_admin_role_menu_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_role_menu").await?;
    
    if !table_exists {
        info!("创建admin_role_menu表...");
        sqlx::query(
            "CREATE TABLE `admin_role_menu` (
                `role_id` bigint(20) UNSIGNED NOT NULL,
                `menu_id` bigint(20) UNSIGNED NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                INDEX `admin_role_menu_role_id_menu_id_index`(`role_id`, `menu_id`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        info!("admin_role_menu表创建成功");
    }
    
    Ok(())
}

// 检查admin_settings表
async fn check_admin_settings_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_settings").await?;
    
    if !table_exists {
        info!("创建admin_settings表...");
        sqlx::query(
            "CREATE TABLE `admin_settings` (
                `slug` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`slug`) USING BTREE
            ) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        info!("admin_settings表创建成功");
    }
    
    Ok(())
}

// 检查admin_extensions表
async fn check_admin_extensions_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_extensions").await?;
    
    if !table_exists {
        info!("创建admin_extensions表...");
        sqlx::query(
            "CREATE TABLE `admin_extensions` (
                `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
                `is_enabled` tinyint(4) NOT NULL DEFAULT 0,
                `options` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                UNIQUE INDEX `admin_extensions_name_unique`(`name`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        info!("admin_extensions表创建成功");
    }
    
    Ok(())
}

// 检查admin_extension_histories表
async fn check_admin_extension_histories_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    // 检查表是否存在
    let table_exists = check_table_exists(pool, "admin_extension_histories").await?;
    
    if !table_exists {
        info!("创建admin_extension_histories表...");
        sqlx::query(
            "CREATE TABLE `admin_extension_histories` (
                `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
                `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL,
                `type` tinyint(4) NOT NULL DEFAULT 1,
                `version` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0',
                `detail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL,
                `created_at` timestamp NULL DEFAULT NULL,
                `updated_at` timestamp NULL DEFAULT NULL,
                PRIMARY KEY (`id`) USING BTREE,
                INDEX `admin_extension_histories_name_index`(`name`) USING BTREE
            ) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = Dynamic;"
        )
        .execute(pool)
        .await?;
        
        info!("admin_extension_histories表创建成功");
    }
    
    Ok(())
}

// 检查表是否存在
async fn check_table_exists(pool: &MySqlPool, table_name: &str) -> Result<bool, Box<dyn Error>> {
    let result = sqlx::query(
        "SELECT COUNT(*) as count FROM information_schema.TABLES 
         WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?"
    )
    .bind(table_name)
    .fetch_one(pool)
    .await?;
    
    let count: i64 = result.try_get("count")?;
    Ok(count > 0)
}

// 检查字段是否存在
async fn check_column_exists(pool: &MySqlPool, table_name: &str, column_name: &str) -> Result<bool, Box<dyn Error>> {
    let result = sqlx::query(
        "SELECT COUNT(*) as count FROM information_schema.COLUMNS 
         WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?"
    )
    .bind(table_name)
    .bind(column_name)
    .fetch_one(pool)
    .await?;
    
    let count: i64 = result.try_get("count")?;
    Ok(count > 0)
}

// 检查spider_logs表
async fn check_spider_logs_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    info!("🕷️ 检查spider_logs表...");

    // 检查表是否存在
    if !table_exists(pool, "spider_logs").await? {
        info!("📝 spider_logs表不存在，开始创建...");
        create_spider_logs_table(pool).await?;
        info!("✅ spider_logs表创建成功");
    } else {
        info!("✅ spider_logs表已存在");

        // 检查表结构是否完整
        let required_columns = vec![
            ("id", "bigint unsigned"),
            ("site", "varchar(255)"),
            ("url", "varchar(500)"),
            ("user_agent", "varchar(500)"),
            ("ip", "varchar(45)"),
            ("created_at", "timestamp"),
        ];

        let mut missing_columns = Vec::new();
        for (column_name, _column_type) in &required_columns {
            if !column_exists(pool, "spider_logs", column_name).await? {
                missing_columns.push(*column_name);
            }
        }

        if !missing_columns.is_empty() {
            info!("⚠️ spider_logs表结构不完整，缺少字段: {:?}", missing_columns);
            info!("🔧 正在修复表结构...");
            fix_spider_logs_table_structure(pool, &missing_columns).await?;
            info!("✅ spider_logs表结构修复完成");
        }

        // 检查索引
        ensure_spider_logs_indexes(pool).await?;
    }

    Ok(())
}

// 创建spider_logs表
async fn create_spider_logs_table(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    let create_sql = r#"
        CREATE TABLE `spider_logs` (
            `id` bigint unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
            `site` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '网站域名',
            `url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '/' COMMENT '访问URL',
            `user_agent` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'Googlebot' COMMENT '用户代理',
            `ip` varchar(45) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'IP地址',
            `created_at` timestamp NOT NULL COMMENT '创建时间',
            PRIMARY KEY (`id`),
            KEY `idx_site_time` (`site`,`created_at`),
            KEY `idx_created_at` (`created_at`),
            KEY `idx_site` (`site`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='蜘蛛爬行日志表'
    "#;

    sqlx::query(create_sql).execute(pool).await?;
    Ok(())
}

// 修复spider_logs表结构
async fn fix_spider_logs_table_structure(pool: &MySqlPool, missing_columns: &[&str]) -> Result<(), Box<dyn Error>> {
    for column_name in missing_columns {
        let alter_sql = match *column_name {
            "url" => "ALTER TABLE spider_logs ADD COLUMN `url` varchar(500) NOT NULL DEFAULT '/' COMMENT '访问URL'",
            "user_agent" => "ALTER TABLE spider_logs ADD COLUMN `user_agent` varchar(500) NOT NULL DEFAULT 'Googlebot' COMMENT '用户代理'",
            "ip" => "ALTER TABLE spider_logs ADD COLUMN `ip` varchar(45) NOT NULL DEFAULT '' COMMENT 'IP地址'",
            _ => continue,
        };

        match sqlx::query(alter_sql).execute(pool).await {
            Ok(_) => info!("✅ 添加字段: {}", column_name),
            Err(e) => {
                // 如果字段已存在，忽略错误
                if e.to_string().contains("Duplicate column name") {
                    info!("⚠️ 字段已存在: {}", column_name);
                } else {
                    return Err(e.into());
                }
            }
        }
    }
    Ok(())
}

// 确保spider_logs表的索引存在
async fn ensure_spider_logs_indexes(pool: &MySqlPool) -> Result<(), Box<dyn Error>> {
    let indexes = vec![
        ("idx_site_time", "CREATE INDEX `idx_site_time` ON `spider_logs` (`site`, `created_at`)"),
        ("idx_created_at", "CREATE INDEX `idx_created_at` ON `spider_logs` (`created_at`)"),
        ("idx_site", "CREATE INDEX `idx_site` ON `spider_logs` (`site`)"),
    ];

    for (index_name, create_sql) in indexes {
        // 检查索引是否存在
        let index_exists = sqlx::query(
            "SELECT COUNT(*) as count FROM information_schema.STATISTICS
             WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'spider_logs' AND INDEX_NAME = ?"
        )
        .bind(index_name)
        .fetch_one(pool)
        .await?;

        let count: i64 = index_exists.try_get("count")?;
        if count == 0 {
            match sqlx::query(create_sql).execute(pool).await {
                Ok(_) => info!("✅ 创建索引: {}", index_name),
                Err(e) => {
                    // 如果索引已存在，忽略错误
                    if e.to_string().contains("Duplicate key name") {
                        info!("⚠️ 索引已存在: {}", index_name);
                    } else {
                        eprintln!("❌ 创建索引失败 {}: {}", index_name, e);
                    }
                }
            }
        }
    }

    Ok(())
}

// 检查表是否存在
async fn table_exists(pool: &MySqlPool, table_name: &str) -> Result<bool, Box<dyn Error>> {
    let result = sqlx::query(
        "SELECT COUNT(*) as count FROM information_schema.TABLES
         WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?"
    )
    .bind(table_name)
    .fetch_one(pool)
    .await?;

    let count: i64 = result.try_get("count")?;
    Ok(count > 0)
}

// 检查列是否存在
async fn column_exists(pool: &MySqlPool, table_name: &str, column_name: &str) -> Result<bool, Box<dyn Error>> {
    let result = sqlx::query(
        "SELECT COUNT(*) as count FROM information_schema.COLUMNS
         WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?"
    )
    .bind(table_name)
    .bind(column_name)
    .fetch_one(pool)
    .await?;

    let count: i64 = result.try_get("count")?;
    Ok(count > 0)
}