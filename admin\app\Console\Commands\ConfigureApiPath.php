<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\ApiConfigService;

class ConfigureApiPath extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'config:api-path 
                            {path? : API项目相对路径}
                            {--check : 检查当前配置}
                            {--info : 显示API项目信息}
                            {--test : 测试API连接}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '配置和管理API项目路径';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        if ($this->option('check')) {
            return $this->checkConfiguration();
        }
        
        if ($this->option('info')) {
            return $this->showApiInfo();
        }
        
        if ($this->option('test')) {
            return $this->testApiConnection();
        }
        
        $path = $this->argument('path');
        
        if ($path) {
            return $this->setApiPath($path);
        }
        
        return $this->interactiveConfiguration();
    }
    
    /**
     * 交互式配置
     */
    protected function interactiveConfiguration()
    {
        $this->info('=== API项目路径配置 ===');
        
        $currentPath = ApiConfigService::getApiProjectPath();
        $this->line("当前配置: {$currentPath}");
        
        $newPath = $this->ask('请输入新的API项目相对路径', $currentPath);
        
        if ($newPath !== $currentPath) {
            return $this->setApiPath($newPath);
        }
        
        $this->info('配置未更改');
        return 0;
    }
    
    /**
     * 设置API路径
     */
    protected function setApiPath(string $path)
    {
        $this->info("设置API项目路径: {$path}");
        
        // 验证路径
        $oldPath = ApiConfigService::getApiProjectPath();
        ApiConfigService::setApiProjectPath($path);
        
        $validation = ApiConfigService::validateApiPath();
        
        if (!$validation['valid']) {
            // 恢复旧路径
            ApiConfigService::setApiProjectPath($oldPath);
            
            $this->error('路径验证失败:');
            foreach ($validation['errors'] as $error) {
                $this->error("  - {$error}");
            }
            return 1;
        }
        
        if (!empty($validation['warnings'])) {
            $this->warn('警告:');
            foreach ($validation['warnings'] as $warning) {
                $this->warn("  - {$warning}");
            }
        }
        
        $this->info('✅ API项目路径配置成功');
        $this->showApiInfo();
        
        return 0;
    }
    
    /**
     * 检查配置
     */
    protected function checkConfiguration()
    {
        $this->info('=== 检查API项目配置 ===');
        
        $validation = ApiConfigService::validateApiPath();
        
        if ($validation['valid']) {
            $this->info('✅ 配置验证通过');
        } else {
            $this->error('❌ 配置验证失败');
            foreach ($validation['errors'] as $error) {
                $this->error("  - {$error}");
            }
        }
        
        if (!empty($validation['warnings'])) {
            $this->warn('⚠️ 警告:');
            foreach ($validation['warnings'] as $warning) {
                $this->warn("  - {$warning}");
            }
        }
        
        return $validation['valid'] ? 0 : 1;
    }
    
    /**
     * 显示API信息
     */
    protected function showApiInfo()
    {
        $this->info('=== API项目信息 ===');
        
        $info = ApiConfigService::getApiInfo();
        
        $this->table(
            ['配置项', '值'],
            [
                ['项目路径', $info['project_path']],
                ['完整路径', $info['full_path']],
                ['环境文件', $info['env_path']],
                ['环境文件存在', $info['env_exists'] ? '是' : '否'],
                ['绑定地址', $info['bind_addr']],
                ['基础URL', $info['base_url']],
                ['服务状态', $info['status'] ? '运行中' : '未运行'],
            ]
        );
        
        return 0;
    }
    
    /**
     * 测试API连接
     */
    protected function testApiConnection()
    {
        $this->info('=== 测试API连接 ===');
        
        try {
            $this->line('检查API服务状态...');
            $status = ApiConfigService::checkApiStatus();
            
            if ($status) {
                $this->info('✅ API服务运行正常');
                
                $this->line('测试缓存刷新接口...');
                $result = ApiConfigService::refreshApiCache();
                
                if ($result === 'ok') {
                    $this->info('✅ 缓存刷新接口正常');
                } else {
                    $this->warn("⚠️ 缓存刷新返回: {$result}");
                }
            } else {
                $this->error('❌ API服务无法访问');
                
                $info = ApiConfigService::getApiInfo();
                $this->line("请检查:");
                $this->line("1. API服务是否启动");
                $this->line("2. 绑定地址是否正确: {$info['bind_addr']}");
                $this->line("3. 防火墙设置");
            }
            
        } catch (\Exception $e) {
            $this->error('❌ 连接测试失败: ' . $e->getMessage());
            return 1;
        }
        
        return 0;
    }
}
