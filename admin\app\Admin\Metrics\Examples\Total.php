<?php

namespace App\Admin\Metrics\Examples;

use App\Models\SeoTotal;
use Dcat\Admin\Widgets\Metrics\Line;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;


class Total extends Line
{
    /**
     * 初始化卡片内容
     *
     * @return void
     */
    protected function init()
    {
        parent::init();

        $this->title('统计');
        $this->chartHeight(300);
        $this->dropdown([
            '0' => '本月',
            '1' => '上一月',
        ]);
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return mixed|void
     */
    public function handle(Request $request)
    {
        $option = $request->get('option');
        $year = date('Y');
        $month = date('m');
        if ($option == 1) {
            $year = date('Y', strtotime('-'.$option.' month'));
            $month = date('m', strtotime('-'.$option.' month'));
        }
        
        $dayNum = $dayNum = date('t', mktime(0, 0, 0, $month, 1, $year));
        $data = $this->getData($year.$month);
        $jumpList =  [];
        $spiderList =  [];
        $dayList = [];
        $spiderCount = 0;
        $jumpCount = 0;
        for ($i = 1; $i <=$dayNum; $i++) {
            $dayList[] = sprintf('%d号', $i);
            $jumpList[] = 0;
            $spiderList[] = 0;
        }
        foreach ($data as $key => $value) {
            $day = (int) substr($value['date'],-2) -1;
            $jumpList[$day] = (int)$value['jump'];
            $spiderList[$day] =(int) $value['spider'];
            $spiderCount += (int)$value['spider'];
            $jumpCount += (int)$value['jump'];
        }
        // dump($jumpList);
        // $this->chartLabels($dayList);
        $this->chart([
            'chart' => [
                'type' => 'line',
                'sparkline' => [
                    'enabled' => false,
                ],
                'toolbar' => ['show' => false],

            ],
            'series' => [
                [
                    'name' => '蜘蛛量',
                    'data' => $spiderList
                ],
                [
                    'name' => '跳转量',
                    'data' => $jumpList
                ]
            ],
            'stroke' => [
                'curve' => 'smooth',
                'width' => 2,
            ],
            'xaxis' => [
                'categories' =>  $dayList,
            ],
            
            'yaxis' => [ // 添加 yaxis 选项，定义多个 Y 轴
                [
                    'title' => [
                        'text' => '蜘蛛量', // 第一个 Y 轴标题
                    ],
                ],
                [
                    'opposite' => true, // 将第二个 Y 轴放在右侧
                    'title' => [
                        'text' => '跳转量', // 第二个 Y 轴标题
                    ],
                ],
            ],
            'fill' => [
                'type' => 'solid',
                'opacity' => 1,
            ],
            'colors' => ['#77B6EA', '#545454'],
        ]);
        $this->withContent(sprintf('%d月跳转量:%d, 总蜘蛛量:%d',$month, $jumpCount,$spiderCount));
    }

    public function getData(string $month){
        // echo $month;
        //select `date`,count(jump) as jump, count(spider) as spider from seo_total where `date` like '202407__' GROUP BY `date`
        $list = SeoTotal::where('date','like',$month.'__')->groupBy('date')->orderBy('date')->select('date',DB::raw('sum(jump) as jump'),DB::raw('sum(spider) as spider'))->get();
        $data = [];
        foreach ($list as $key => $value) {
            $data[] = [
                'date' => $value->date,
                'jump' => $value->jump,
                'spider' => $value->spider,
            ];
        }

        return $data;
    }

    /**
     * 设置图表数据.
     *
     * @param array $data
     *
     * @return $this
     */
    public function withChart(array $data, $title)
    {
        return $this->chart([
            'chart' => [
                'type' => 'line',
                // 'height' => 350,
                'sparkline' => [
                    'enabled' => false,
                ],            
            ],
            
            'series' => [
                [
                    'name'=> 'sales',
                    'data'=> [30,40,35,50,49,60,70,91,125]
                ]
            ],
            // 'stroke' => [
            //     'curve' => 'smooth',
            // ],
            // 'dataLabels' => [
            //     'enabled' => true,
            // ],
            // 'xaxis' => [
            //     'categories' => [1991,1992,1993,1994,1995,1996,1997, 1998,1999],
            // ]
        ]);
    }

    /**
     * 设置卡片内容.
     *
     * @param string $content
     *
     * @return $this
     */
    public function withContent($content)
    {
        return $this->content(
            <<<HTML
<div class="d-flex justify-content-between align-items-center mt-1" style="margin-bottom: 2px">
    <h2 class="ml-1 font-lg-1">{$content}</h2>
    <span class="mb-0 mr-1 text-80">{$this->title}</span>
</div>
HTML
        );
    }
}
