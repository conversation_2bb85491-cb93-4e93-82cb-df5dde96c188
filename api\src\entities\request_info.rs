use crate::routes::default::QueryParams;
use actix_web::HttpRequest;

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, De<PERSON>ult)]
pub struct RequestInfo {
    pub host: String,
    pub uri: String,
    pub user_agent: String,
    pub ip: String,
    pub referer: String,
    pub protocol: String,
    // pub top_domain: String,
    pub port: String,
    pub cache_name: String,
    // pub hash_str: String,
    // pub page_path: String,
    // pub theme_path: String,
}

impl RequestInfo {
    // 验证域名格式是否有效
    fn is_valid_domain(host: &str) -> bool {
        // 空域名
        if host.is_empty() {
            return false;
        }

        // 单个词（没有点号），除了localhost
        if !host.contains('.') && host != "localhost" {
            return false;
        }

        // 包含无效字符（允许字母、数字、点号、连字符、下划线）
        if host.chars().any(|c| !c.is_ascii_alphanumeric() && c != '.' && c != '-' && c != '_') {
            return false;
        }

        // 以点开头或结尾
        if host.starts_with('.') || host.ends_with('.') {
            return false;
        }

        // 连续的点
        if host.contains("..") {
            return false;
        }

        // 过长的域名
        if host.len() > 253 {
            return false;
        }

        // 检查是否是明显的内部服务名（只检查完全匹配的单词）
        let invalid_patterns = [
            "apps", "intra", "spool", "corporate", "web", "gamma",
            "release", "pre", "prerelease", "local", "int", "dev",
            "test", "staging", "admin", "api", "www", "mail", "ftp",
            "console", "development"
        ];

        if invalid_patterns.contains(&host) {
            return false;
        }

        // 基本的域名格式检查：至少包含一个点，且各部分不为空
        let parts: Vec<&str> = host.split('.').collect();
        if parts.len() < 2 {
            return false;
        }

        // 检查每个部分是否有效
        for part in parts {
            if part.is_empty() || part.len() > 63 {
                return false;
            }
            // 每个部分不能以连字符开头或结尾（但可以包含连字符和下划线）
            if part.starts_with('-') || part.ends_with('-') {
                return false;
            }
            // 检查是否只包含有效字符
            if part.chars().any(|c| !c.is_ascii_alphanumeric() && c != '-' && c != '_') {
                return false;
            }
        }

        // 🔧 检查重复域名模式（如 www.google.co.th.www.google.co.th）
        if Self::has_duplicate_domain_pattern(host) {
            return false;
        }

        // 🔧 检查是否包含多个顶级域名（如 example.com.example.org）
        if Self::has_multiple_tlds(host) {
            return false;
        }

        true
    }

    // 检查是否有重复的域名模式
    fn has_duplicate_domain_pattern(host: &str) -> bool {
        let parts: Vec<&str> = host.split('.').collect();
        let len = parts.len();

        // 如果部分数量是偶数，检查前半部分是否等于后半部分
        if len >= 4 && len % 2 == 0 {
            let mid = len / 2;
            let first_half = &parts[0..mid];
            let second_half = &parts[mid..];

            if first_half == second_half {
                return true;
            }
        }

        // 检查是否有明显的重复模式（如 abc.com.abc.com）
        let domain_str = host.to_lowercase();

        // 常见的顶级域名
        let tlds = ["com", "org", "net", "edu", "gov", "mil", "int", "co", "pk", "th", "br", "in", "us"];

        for tld in tlds {
            let pattern = format!(".{}", tld);
            let positions: Vec<_> = domain_str.match_indices(&pattern).collect();

            // 如果同一个TLD出现多次，可能是重复域名
            if positions.len() > 1 {
                // 检查是否是真正的重复（而不是合法的子域名）
                for i in 0..positions.len()-1 {
                    let first_end = positions[i].0 + pattern.len();
                    let second_start = positions[i+1].0;

                    // 如果两个TLD之间的部分看起来像是重复的域名
                    if second_start > first_end {
                        let between = &domain_str[first_end..second_start];
                        if between.starts_with('.') && between.len() > 1 {
                            return true;
                        }
                    }
                }
            }
        }

        false
    }

    // 检查是否包含多个顶级域名
    fn has_multiple_tlds(host: &str) -> bool {
        let domain_str = host.to_lowercase();

        // 🔧 只检查核心地区的复合TLD（性能优化）
        let valid_compound_tlds = [
            // 核心地区 - 巴基斯坦
            ".gov.pk", ".edu.pk", ".org.pk", ".com.pk",

            // 核心地区 - 印度
            ".gov.in", ".edu.in", ".org.in", ".co.in",

            // 核心地区 - 泰国
            ".gov.th", ".edu.th", ".org.th", ".co.th",

            // 核心地区 - 巴西
            ".gov.br", ".edu.br", ".org.br", ".com.br",

            // 常见复合TLD
            ".gov.us", ".edu.us", ".co.uk", ".gov.uk",
            ".com.au", ".gov.au", ".co.za", ".gov.za",
        ];

        // 如果包含合法的复合TLD，不认为是多个TLD
        for compound_tld in valid_compound_tlds {
            if domain_str.ends_with(compound_tld) {
                return false;
            }
        }

        // 检查是否有真正的多个独立TLD（如 example.com.another.org）
        let tld_patterns = [".com.", ".org.", ".net.", ".info.", ".biz."];

        for pattern in tld_patterns {
            if domain_str.contains(pattern) {
                // 检查这个TLD后面是否还有其他TLD
                if let Some(pos) = domain_str.find(pattern) {
                    let after_tld = pos + pattern.len();
                    if after_tld < domain_str.len() {
                        let remaining = &domain_str[after_tld..];
                        // 如果后面还有明显的TLD模式，才认为是多个TLD
                        for check_pattern in tld_patterns {
                            if remaining.contains(&check_pattern[1..check_pattern.len()-1]) {
                                return true;
                            }
                        }
                    }
                }
            }
        }

        false
    }

    pub async fn by_api(query: QueryParams) -> Option<Self> {
        let uri = query.path.unwrap_or_default();
        let user_agent = query.spider.unwrap_or_default();
        let ip = query.ip.unwrap_or_else(|| "127.0.0.1".to_string()); // 默认IP地址
        let referer = query.referer.unwrap_or_default();
        let domain = query.domain.unwrap_or_default();

        // 如果关键字段为空，返回None
        if domain.is_empty() {
            return None;
        }

        let (host, protocol) = Self::extract_host_and_protocol(&domain);

        // 🔧 验证域名格式
        if !Self::is_valid_domain(&host) {
            return None;
        }

        // let top_domain = host.clone();
        let hash_str = md5::compute(&uri);

        // let hash_str = format!("{:x}", hash_str);
        let cache_name = format!("{}:{:x}", host, hash_str);
        // let page_path = format!("app/cache/html/{}/{}.html", &top_domain, &hash_str);
        // let theme_path = format!("app/cache/html/{}/theme.txt", &top_domain);
        
        // 原始URI保持不变，不要处理或分割路径
        let request_info = Self {
            host,
            uri,
            user_agent,
            ip,
            referer,
            protocol,
            // top_domain,
            cache_name,
            // page_path,
            // theme_path,
            port: "".to_string(),
        };
        
        if request_info.verify().is_some() {
            Some(request_info)
        } else {
            None
        }
    }
    fn verify(&self) -> Option<()> {
        // 只验证关键字段，IP可以为默认值
        if self.host.is_empty() {
            None
        } else {
            Some(())
        }
    }
    fn extract_host_and_protocol(domain: &str) -> (String, String) {
        // 🔧 修复：正确处理域名和协议提取
        if domain.contains("://") {
            // 完整URL格式：protocol://host
            let mut parts = domain.splitn(2, "://");
            match (parts.next(), parts.next()) {
                (Some(protocol), Some(host)) => {
                    // 移除host中可能的端口号和路径
                    let clean_host = host.split('/').next().unwrap_or(host)
                                        .split(':').next().unwrap_or(host);
                    (clean_host.to_owned(), protocol.to_owned())
                },
                _ => ("".to_owned(), "http".to_owned()),
            }
        } else {
            // 只有域名，没有协议：直接返回域名和默认协议
            let clean_host = domain.split('/').next().unwrap_or(domain)
                                  .split(':').next().unwrap_or(domain);

            // 🔧 验证域名格式：必须包含点号或者是localhost
            if clean_host.contains('.') || clean_host == "localhost" {
                (clean_host.to_owned(), "http".to_owned())
            } else {
                // 无效域名，返回空
                ("".to_owned(), "".to_owned())
            }
        }
    }

    pub fn get_current_url(&self) -> String {
        format!("{}://{}{}", self.protocol, self.host, self.uri)
    }

    /// 从HttpRequest创建RequestInfo
    pub fn by_web(req: &HttpRequest) -> Option<Self> {
        // 获取主机名
        let host = req.connection_info().host().to_string();
        let host = if let Some(pos) = host.find(':') {
            host[..pos].to_string()
        } else {
            host
        };

        // 🔧 验证域名格式
        if !Self::is_valid_domain(&host) {
            return None;
        }

        // 获取URI
        let uri = req.uri().path_and_query()
            .map(|pq| pq.as_str())
            .unwrap_or("/")
            .to_string();

        // 获取User-Agent
        let user_agent = req.headers()
            .get("user-agent")
            .and_then(|v| v.to_str().ok())
            .unwrap_or("")
            .to_string();

        // 获取IP地址
        let ip = req.connection_info()
            .realip_remote_addr()
            .unwrap_or("127.0.0.1")
            .to_string();

        // 获取Referer
        let referer = req.headers()
            .get("referer")
            .and_then(|v| v.to_str().ok())
            .unwrap_or("")
            .to_string();

        // 获取协议
        let protocol = req.connection_info().scheme().to_string();

        // 获取端口
        let port = if let Some(port) = req.connection_info().host().split(':').nth(1) {
            format!(":{}", port)
        } else {
            "".to_string()
        };

        // 生成缓存名称
        let hash_str = md5::compute(&uri);
        let cache_name = format!("{}:{:x}", host, hash_str);

        let request_info = Self {
            host,
            uri,
            user_agent,
            ip,
            referer,
            protocol,
            port,
            cache_name,
        };

        if request_info.verify().is_some() {
            Some(request_info)
        } else {
            None
        }
    }
}
