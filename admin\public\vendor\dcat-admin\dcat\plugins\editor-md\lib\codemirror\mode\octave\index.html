<!doctype html>

<title>CodeMirror: Octave mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="octave.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Octave</a>
  </ul>
</div>

<article>
<h2>Octave mode</h2>

    <div><textarea id="code" name="code">
%numbers
[1234 1234i 1234j]
[.234 .234j 2.23i]
[23e2 12E1j 123D-4 0x234]

%strings
'asda''a'
"asda""a"

%identifiers
a + as123 - __asd__

%operators
-
+
=
==
>
<
>=
<=
&
~
...
break zeros default margin round ones rand
ceil floor size clear zeros eye mean std cov
error eval function
abs acos atan asin cos cosh exp log prod sum
log10 max min sign sin sinh sqrt tan reshape
return
case switch
else elseif end if otherwise
do for while
try catch
classdef properties events methods
global persistent

%one line comment
%{ multi 
line commment %}

    </textarea></div>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: {name: "octave",
               version: 2,
               singleLineStringErrors: false},
        lineNumbers: true,
        indentUnit: 4,
        matchBrackets: true
      });
    </script>

    <p><strong>MIME types defined:</strong> <code>text/x-octave</code>.</p>
</article>
