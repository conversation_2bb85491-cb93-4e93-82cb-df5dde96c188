<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 添加jump_script字段到seo_regions表
        if (Schema::hasTable('seo_regions') && !Schema::hasColumn('seo_regions', 'jump_script')) {
            Schema::table('seo_regions', function (Blueprint $table) {
                $table->text('jump_script')->nullable()->after('status')->comment('跳转脚本');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 移除jump_script字段
        if (Schema::hasTable('seo_regions') && Schema::hasColumn('seo_regions', 'jump_script')) {
            Schema::table('seo_regions', function (Blueprint $table) {
                $table->dropColumn('jump_script');
            });
        }
    }
};
