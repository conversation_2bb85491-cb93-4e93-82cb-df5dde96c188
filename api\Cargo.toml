[package]
name = "api"
version = "0.1.0"
edition = "2021"

# 依赖列表
[dependencies]
actix-web = {version = "4", features = ["compress-gzip"]}
serde = { version = "1", features = ["derive"] }
serde_json = "1"
once_cell = "1"
rand = "0.8"
regex = "1"
chrono =  { version = "0.4", features = ["serde"] }
chrono-tz = "0.10"
# 保留derive宏但移除查询宏，避免编译时数据库连接
sqlx = { version = "0.7", features = ["runtime-tokio", "mysql", "macros"], default-features = false }
dotenvy = "0.15"
redis = { version = "0.26", features = ["tokio-comp"]}
deadpool-redis = "0.16"
tokio = { version = "1", features = ["full"] }
lazy_static = "1"
md5 = "0.7"
urlencoding = "2"
dashmap = "6"
mongodb = "3"
ipnet = "2"
futures = "0.3"
reqwest = { version = "0.12", features = ["json"] }
rayon = "1"
maxminddb = "0.24"
lru = "0.12"
log = "0.4"