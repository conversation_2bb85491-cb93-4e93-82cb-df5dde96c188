<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Model;

class SeoSite extends Model
{
    use HasDateTimeFormatter;

    protected $table = 'seo_site';
    public $timestamps = false;

    protected $fillable = [
        'host',
        'https',
        'open_cache',
        'open_page',
        'open_home',
        'open_link',
        'state',
        'link_rules',
        'jump_rules',
        'region_code',
        'last_time',
        'last_sync'
    ];

    protected $casts = [
        'https' => 'boolean',
        'open_cache' => 'boolean',
        'open_page' => 'boolean',
        'open_home' => 'boolean',
        'open_link' => 'boolean',
        'state' => 'boolean'
    ];

    /**
     * 忽略不存在的属性（如已移除的flood字段）
     */
    public function setAttribute($key, $value)
    {
        // 如果是flood字段，直接忽略（向后兼容）
        if ($key === 'flood') {
            return $this;
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * 关联地区
     */
    public function region()
    {
        return $this->belongsTo(SeoRegion::class, 'region_code', 'code');
    }

    /**
     * 获取指定地区的网站
     */
    public static function getByRegion($regionCode)
    {
        return self::where('region_code', $regionCode)->get();
    }

    /**
     * 获取启用的网站（按地区）
     */
    public static function getActiveByRegion($regionCode)
    {
        return self::where('region_code', $regionCode)
                   ->where('state', 1)
                   ->get();
    }

    /**
     * 批量更新地区
     */
    public static function batchUpdateRegion($siteIds, $regionCode)
    {
        return self::whereIn('id', $siteIds)
                   ->update(['region_code' => $regionCode]);
    }

    /**
     * 获取地区统计
     */
    public static function getRegionStats()
    {
        return self::select('region_code')
                   ->selectRaw('COUNT(*) as total')
                   ->selectRaw('SUM(CASE WHEN state = 1 THEN 1 ELSE 0 END) as active')
                   ->groupBy('region_code')
                   ->get();
    }

    /**
     * 添加debug方法
     */
    public static function debug()
    {
        // 输出state为0的记录数量
        $count = self::where('state', 0)->count();
        \Log::info('未通过网站数量: ' . $count);

        // 输出最近添加的10条记录
        $recent = self::orderBy('id', 'desc')->limit(10)->get(['id', 'host', 'state', 'last_time']);
        \Log::info('最近添加的网站: ' . $recent->toJson());

        return $count;
    }

    /**
     * 获取地区选项
     */
    public static function getRegionOptions()
    {
        return SeoRegion::getRegionOptions();
    }

    /**
     * 检查地区是否有效
     */
    public function hasValidRegion()
    {
        return $this->region && $this->region->isValid();
    }

    /**
     * 获取有效的地区代码
     */
    public function getValidRegionCode()
    {
        if ($this->hasValidRegion()) {
            return $this->region_code;
        }

        // 如果当前地区无效，返回默认地区
        return 'default';
    }
}
