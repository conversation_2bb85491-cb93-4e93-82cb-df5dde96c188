<?php

return [
    /*
    |--------------------------------------------------------------------------
    | API项目配置
    |--------------------------------------------------------------------------
    |
    | 这里配置与API项目对接相关的设置
    |
    */

    /*
    |--------------------------------------------------------------------------
    | API项目路径
    |--------------------------------------------------------------------------
    |
    | API项目相对于admin项目的路径
    | 只需要在这里修改一次即可
    |
    */
    'project_path' => env('API_PATH'),

    /*
    |--------------------------------------------------------------------------
    | API连接配置
    |--------------------------------------------------------------------------
    |
    | API服务连接相关配置
    |
    */
    'connection' => [
        'timeout' => env('API_CONNECTION_TIMEOUT', 30),
        'retry_times' => env('API_RETRY_TIMES', 3),
        'retry_delay' => env('API_RETRY_DELAY', 1), // 秒
    ],

    /*
    |--------------------------------------------------------------------------
    | 缓存配置
    |--------------------------------------------------------------------------
    |
    | API配置缓存相关设置
    |
    */
    'cache' => [
        'enabled' => env('API_CONFIG_CACHE_ENABLED', true),
        'ttl' => env('API_CONFIG_CACHE_TTL', 300), // 5分钟
        'key_prefix' => 'api_config_',
    ],

    /*
    |--------------------------------------------------------------------------
    | 健康检查
    |--------------------------------------------------------------------------
    |
    | API服务健康检查配置
    |
    */
    'health_check' => [
        'enabled' => env('API_HEALTH_CHECK_ENABLED', true),
        'endpoint' => env('API_HEALTH_ENDPOINT', 'health'),
        'interval' => env('API_HEALTH_CHECK_INTERVAL', 60), // 秒
    ],

    /*
    |--------------------------------------------------------------------------
    | 日志配置
    |--------------------------------------------------------------------------
    |
    | API相关日志配置
    |
    */
    'logging' => [
        'enabled' => env('API_LOGGING_ENABLED', true),
        'level' => env('API_LOG_LEVEL', 'info'),
        'channel' => env('API_LOG_CHANNEL', 'daily'),
    ],
];
