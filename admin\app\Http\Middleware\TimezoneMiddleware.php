<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Carbon\Carbon;

class TimezoneMiddleware
{
    /**
     * 处理传入的请求，设置正确的时区
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 获取配置的应用时区
        $timezone = config('app.timezone');
        
        // 设置PHP默认时区
        date_default_timezone_set($timezone);

        // 设置Carbon全局时区
        Carbon::setLocale(config('app.locale'));

        // 调试日志已移除，避免在每次请求时输出
        
        return $next($request);
    }
} 