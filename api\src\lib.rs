pub mod app;
pub mod boot;
pub mod database;
pub mod entities;
pub mod routes;
pub mod util;

use crate::entities::{GlobalConfig, SiteConfig, Resource, ResourceManager};
use crate::util::geoip::GEOIP_CHECKER;
// 移除标准库的RwLock
// use std::sync::RwLock;
// 使用tokio的RwLock
use tokio::sync::RwLock;

use once_cell::sync::Lazy;


static GLOBAL_CONFIG: Lazy<GlobalConfig> = Lazy::new(|| GlobalConfig::new());

// 全局资源管理器 - 启动时加载所有地区资源到内存
pub static RESOURCE_MANAGER: Lazy<ResourceManager> = Lazy::new(|| {
    ResourceManager::new()
});

// 保持向后兼容的全局RESOURCE（指向默认地区）
pub static RESOURCE: Lazy<&'static Resource> = Lazy::new(|| {
    RESOURCE_MANAGER.get_resource("default")
});

// 不再需要初始化GoogleIP检查器
// static GOOGLEIP: Lazy<GoogleIPChecker> = Lazy::new(|| GoogleIPChecker::new());
// static _DATA_LIST: Lazy<DataList> = Lazy::new(|| DataList::new());

// 存储从数据库加载的域名配置模板
pub static TEMPLATE_CONFIG: Lazy<RwLock<Option<SiteConfig>>> = Lazy::new(|| RwLock::new(None));
