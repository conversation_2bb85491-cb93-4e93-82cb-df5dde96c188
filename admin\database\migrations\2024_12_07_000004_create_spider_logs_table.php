<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 创建蜘蛛日志表
        if (!Schema::hasTable('spider_logs')) {
            Schema::create('spider_logs', function (Blueprint $table) {
                $table->id();
                $table->string('site', 255)->comment('网站域名');
                $table->string('url', 500)->default('/')->comment('访问URL');
                $table->string('user_agent', 500)->default('Googlebot')->comment('用户代理');
                $table->string('ip', 45)->default('')->comment('IP地址');
                $table->timestamp('created_at')->comment('创建时间');
                
                // 添加索引
                $table->index(['site', 'created_at'], 'idx_site_time');
                $table->index('created_at', 'idx_created_at');
                $table->index('site', 'idx_site');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('spider_logs');
    }
};
