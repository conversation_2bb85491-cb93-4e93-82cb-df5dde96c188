<?php

namespace App\Admin\Services;

use App\Models\SeoRegion;
use Illuminate\Support\Facades\Cache;

class RegionMenuService
{
    /**
     * 获取地区网站菜单
     */
    public static function getRegionWebsiteMenus()
    {
        // 缓存5分钟，避免频繁查询数据库
        return Cache::remember('region_website_menus', 300, function () {
            $regions = SeoRegion::where('status', 1)
                              ->orderBy('priority')
                              ->orderBy('code')
                              ->get();
            
            $menus = [];
            
            // 添加全部网站菜单
            $menus[] = [
                'id' => 'all_websites',
                'title' => '全部网站',
                'icon' => 'fa-globe',
                'uri' => 'seo/site',
                'order' => 1,
                'show' => 1
            ];
            
            // 为每个地区创建菜单项
            foreach ($regions as $index => $region) {
                $menus[] = [
                    'id' => "region_{$region->code}",
                    'title' => self::getRegionDisplayName($region),
                    'icon' => 'fa-circle-thin',
                    'uri' => "seo/site?region={$region->code}",
                    'order' => $index + 2,
                    'show' => 1,
                    'region_code' => $region->code,
                    'region_name' => $region->name
                ];
            }
            
            return $menus;
        });
    }
    
    /**
     * 获取地区显示名称（带国旗emoji）
     */
    private static function getRegionDisplayName($region)
    {
        $flags = [
            'br' => '🇧🇷',
            'pk' => '🇵🇰', 
            'in' => '🇮🇳',
            'us' => '🇺🇸',
            'id' => '🇮🇩',
            'th' => '🇹🇭',
            'mx' => '🇲🇽',
            'ng' => '🇳🇬',
            'bd' => '🇧🇩',
            'ph' => '🇵🇭',
            'vn' => '🇻🇳',
            'jp' => '🇯🇵',
            'default' => '🌍'
        ];
        
        $flag = $flags[$region->code] ?? '🌍';
        return "{$flag} {$region->name}";
    }
    
    /**
     * 清除菜单缓存
     */
    public static function clearMenuCache()
    {
        Cache::forget('region_website_menus');
    }
    
    /**
     * 获取地区网站统计
     */
    public static function getRegionWebsiteStats()
    {
        return Cache::remember('region_website_stats', 300, function () {
            $regions = SeoRegion::select('seo_regions.*')
                              ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code) as site_count')
                              ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code AND seo_site.state = 1) as active_site_count')
                              ->where('status', 1)
                              ->orderBy('priority')
                              ->get();
            
            return $regions;
        });
    }
}
