<!doctype html>

<title>CodeMirror: Jade Templating Mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="../javascript/javascript.js"></script>
<script src="../css/css.js"></script>
<script src="../xml/xml.js"></script>
<script src="../htmlmixed/htmlmixed.js"></script>
<script src="jade.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Jade Templating Mode</a>
  </ul>
</div>

<article>
<h2>Jade Templating Mode</h2>
<form><textarea id="code" name="code">
doctype html
  html
    head
      title= "Jade Templating CodeMirror Mode Example"
      link(rel='stylesheet', href='/css/bootstrap.min.css')
      link(rel='stylesheet', href='/css/index.css')
      script(type='text/javascript', src='/js/jquery-1.9.1.min.js')
      script(type='text/javascript', src='/js/bootstrap.min.js')
    body
      div.header
        h1 Welcome to this Example
      div.spots
        if locals.spots
          each spot in spots
            div.spot.well
         div
           if spot.logo
             img.img-rounded.logo(src=spot.logo)
           else
             img.img-rounded.logo(src="img/placeholder.png")
         h3
           a(href=spot.hash) ##{spot.hash}
           if spot.title
             span.title #{spot.title}
           if spot.desc
             div #{spot.desc}
        else
          h3 There are no spots currently available.
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        mode: {name: "jade", alignCDATA: true},
        lineNumbers: true
      });
    </script>
    <h3>The Jade Templating Mode</h3>
      <p> Created by Forbes Lindesay. Managed as part of a Brackets extension at <a href="https://github.com/ForbesLindesay/jade-brackets">https://github.com/ForbesLindesay/jade-brackets</a>.</p>
    <p><strong>MIME type defined:</strong> <code>text/x-jade</code>.</p>
  </article>
