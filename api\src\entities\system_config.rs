use sqlx::MySqlPool;
use sqlx::Row;
#[derive(<PERSON><PERSON>, Debug, sqlx::FromRow)]
pub struct SystemConfig {
    pub jump_script: String,
    pub link_fixed: String,
    pub link_list: Vec<String>,
    pub link_num: i64,
    pub link_total: i64,
    pub sitemap_num: i64,
    pub link_site_num: i64,
}

impl SystemConfig {
    pub async fn init(pool: &MySqlPool) -> Self {
        match sqlx::query("SELECT * FROM seo_config ORDER BY id LIMIT 1")
            .fetch_one(pool)
            .await
        {
            Ok(row) => {
                let jump_script: String =
                    row.try_get("jump_script").unwrap_or_else(|_| String::new());
                let link_fixed: String =
                    row.try_get("link_fixed").unwrap_or_else(|_| String::new());
                let link_list_str: String =
                    row.try_get("link_list").unwrap_or_else(|_| String::new());

                // 处理link_list，按行分割并过滤空行
                let link_list: Vec<String> = link_list_str
                    .split('\n')
                    .map(|s| s.trim().to_string())
                    .filter(|s| !s.is_empty())
                    .collect();

                let link_num: i64 = row.try_get("link_num").unwrap_or_else(|_| 0);
                let link_total: i64 = row.try_get("link_total").unwrap_or_else(|_| 30);
                let sitemap_num: i64 = row.try_get("sitemap_num").unwrap_or_else(|_| 1000);
                let link_site_num: i64 = row.try_get("link_site_num").unwrap_or_else(|_| 30);

                SystemConfig {
                    jump_script,
                    link_fixed,
                    link_list,
                    link_num,
                    link_total,
                    sitemap_num,
                    link_site_num,
                }
            }
            Err(e) => {
                println!("警告: seo_config表不存在或无法读取: {}", e);
                println!("使用默认系统配置");
                // 返回默认配置
                SystemConfig {
                    jump_script: String::new(),
                    link_fixed: String::new(),
                    link_list: Vec::new(),
                    link_num: 0,
                    link_total: 30,
                    sitemap_num: 1000,
                    link_site_num: 30,
                }
            }
        }
    }
}
