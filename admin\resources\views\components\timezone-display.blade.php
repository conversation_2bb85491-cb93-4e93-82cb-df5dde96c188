@php
    $timezone = config('app.timezone');
    $dbTimezone = config('database.connections.mysql.timezone');
    
    // 获取时区名称
    $timezoneMap = [
        // 美洲
        'America/Sao_Paulo' => '巴西',
        'America/New_York' => '美国东部',
        'America/Chicago' => '美国中部',
        'America/Denver' => '美国山地',
        'America/Los_Angeles' => '美国西部',
        'America/Mexico_City' => '墨西哥',
        'America/Toronto' => '加拿大东部',
        'America/Vancouver' => '加拿大西部',
        'America/Argentina/Buenos_Aires' => '阿根廷',
        
        // 欧洲
        'Europe/London' => '英国',
        'Europe/Paris' => '法国',
        'Europe/Berlin' => '德国',
        'Europe/Madrid' => '西班牙',
        'Europe/Rome' => '意大利',
        'Europe/Moscow' => '俄罗斯',
        
        // 亚洲
        'Asia/Shanghai' => '中国',
        'Asia/Hong_Kong' => '香港',
        'Asia/Taipei' => '台湾',
        'Asia/Tokyo' => '日本',
        'Asia/Seoul' => '韩国',
        'Asia/Singapore' => '新加坡',
        'Asia/Dubai' => '迪拜',
        'Asia/Bangkok' => '泰国',
        'Asia/Kolkata' => '印度',
        
        // 大洋洲
        'Australia/Sydney' => '澳大利亚东部',
        'Australia/Perth' => '澳大利亚西部',
        'Pacific/Auckland' => '新西兰',
        
        // 非洲
        'Africa/Cairo' => '埃及',
        'Africa/Johannesburg' => '南非',
        
        // 其他
        'UTC' => '世界协调时间(UTC)',
    ];
    
    $timezoneName = $timezoneMap[$timezone] ?? $timezone;
    
    // 获取当前时区的时间
    $currentTime = now()->format('H:i');
@endphp

<div class="timezone-display" style="background-color: #f8f9fa; padding: 5px 15px; text-align: center; border-bottom: 1px solid #ddd;">
    <span>当前程序时区：{{ $timezoneName }}</span>
    <span style="margin-left: 15px;">当前时间：{{ $currentTime }}</span>
</div> 