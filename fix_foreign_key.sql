-- 彻底修复seo_site表的外键约束问题

USE guosql;

-- 1. 显示当前问题
SELECT '=== 当前seo_regions表中的有效地区代码 ===' as info;
SELECT code, name FROM seo_regions ORDER BY code;

SELECT '=== 当前seo_site表中的无效region_code ===' as info;
SELECT region_code, COUNT(*) as count 
FROM seo_site 
WHERE region_code NOT IN (SELECT code FROM seo_regions) 
   OR region_code IS NULL 
   OR region_code = ''
GROUP BY region_code;

-- 2. 临时禁用外键检查
SET FOREIGN_KEY_CHECKS = 0;

-- 3. 删除现有的外键约束（如果存在）
ALTER TABLE seo_site DROP FOREIGN KEY IF EXISTS seo_site_ibfk_1;

-- 4. 修复所有无效的region_code
UPDATE seo_site 
SET region_code = 'default' 
WHERE region_code NOT IN (SELECT code FROM seo_regions) 
   OR region_code IS NULL 
   OR region_code = '';

-- 5. 确保default地区存在
INSERT IGNORE INTO seo_regions (code, name, language, timezone, config, status, priority, created_at, updated_at) 
VALUES ('default', '默认地区', 'en', 'UTC', '{"currency": "USD", "domain_suffix": ".com", "country_code": "US"}', 1, 1, NOW(), NOW());

-- 6. 再次修复任何可能遗漏的无效region_code
UPDATE seo_site 
SET region_code = 'default' 
WHERE region_code NOT IN (SELECT code FROM seo_regions) 
   OR region_code IS NULL 
   OR region_code = '';

-- 7. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 8. 重新添加外键约束
ALTER TABLE seo_site 
ADD CONSTRAINT seo_site_ibfk_1 
FOREIGN KEY (region_code) REFERENCES seo_regions(code) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- 9. 验证修复结果
SELECT '=== 修复后验证 ===' as info;
SELECT COUNT(*) as total_sites FROM seo_site;
SELECT COUNT(*) as invalid_sites 
FROM seo_site 
WHERE region_code NOT IN (SELECT code FROM seo_regions);

SELECT '=== 各地区网站统计 ===' as info;
SELECT r.code, r.name, COUNT(s.id) as site_count
FROM seo_regions r
LEFT JOIN seo_site s ON r.code = s.region_code
GROUP BY r.code, r.name
ORDER BY r.priority;

SELECT '=== 外键约束状态 ===' as info;
SELECT 
    CONSTRAINT_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'guosql' 
  AND TABLE_NAME = 'seo_site' 
  AND REFERENCED_TABLE_NAME IS NOT NULL;
