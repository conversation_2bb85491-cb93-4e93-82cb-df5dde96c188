<?php

namespace App\Console\Commands;

use App\Models\SeoSite;
use Illuminate\Console\Command;

class RefreshNonPassedSites extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'refresh:non-passed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '刷新未通过的站点状态';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始检查未通过站点...');
        
        try {
            // 检查数据库中状态为0的记录
            $nonPassedSites = SeoSite::where('state', 0)->get();
            $this->info('找到 ' . count($nonPassedSites) . ' 个未通过站点');
            
            foreach ($nonPassedSites as $site) {
                $this->info('站点ID: ' . $site->id . ', 域名: ' . $site->host);
            }
            
            // 删除测试域名（以test-开头的域名）
            $testSites = SeoSite::where('host', 'like', 'test-%')->get();
            if (count($testSites) > 0) {
                $this->info('找到 ' . count($testSites) . ' 个测试站点，准备删除...');
                foreach ($testSites as $site) {
                    $this->info('删除测试站点: ' . $site->host);
                    $site->delete();
                }
                $this->info('测试站点已全部删除。');
            }
            
            $this->info('检查完成。');
            return 0;
        } catch (\Exception $e) {
            $this->error('检查失败: ' . $e->getMessage());
            return 1;
        }
    }
} 