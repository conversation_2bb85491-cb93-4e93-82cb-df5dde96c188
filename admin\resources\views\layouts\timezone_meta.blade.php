{{-- 
    将此文件包含在主布局中的<head>部分
    例如: @include('layouts.timezone_meta')
--}}
<meta name="app-timezone" content="{{ config('app.timezone') }}">
<script>
    // 添加全局变量
    window.APP_CONFIG = window.APP_CONFIG || {};
    window.APP_CONFIG.timezone = '{{ config('app.timezone') }}';
</script>

{{-- 如果需要moment.js库，可以在此处引入 --}}
@if(!isset($no_moment))
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment-timezone/0.5.43/moment-timezone-with-data.min.js"></script>
@endif

{{-- 引入我们创建的时区JS文件 --}}
<script src="{{ asset('js/timezone.js') }}"></script> 