<div class="row">
    <!-- 总览统计卡片 -->
    <div class="col-md-12 mb-4">
        <div class="row">
            <div class="col-md-3">
                <div class="card bg-primary text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $totalSites }}</h4>
                                <p class="mb-0">总网站数</p>
                            </div>
                            <div class="align-self-center">
                                <i class="feather icon-globe font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-success text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $totalActiveSites }}</h4>
                                <p class="mb-0">启用网站</p>
                            </div>
                            <div class="align-self-center">
                                <i class="feather icon-check-circle font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-warning text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $totalSites - $totalActiveSites }}</h4>
                                <p class="mb-0">禁用网站</p>
                            </div>
                            <div class="align-self-center">
                                <i class="feather icon-x-circle font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card bg-info text-white">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h4 class="mb-0">{{ $regions->count() }}</h4>
                                <p class="mb-0">活跃地区</p>
                            </div>
                            <div class="align-self-center">
                                <i class="feather icon-map font-size-24"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- 地区网站分布 -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">地区网站分布</h4>
                <div class="card-header-right">
                    <a href="{{ admin_url('seo/site') }}" class="btn btn-primary btn-sm">
                        <i class="feather icon-eye"></i> 查看全部网站
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>地区</th>
                                <th>总网站数</th>
                                <th>启用网站</th>
                                <th>禁用网站</th>
                                <th>使用率</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($regions as $region)
                            <tr>
                                <td>
                                    @php
                                        $flags = [
                                            'br' => '🇧🇷', 'pk' => '🇵🇰', 'in' => '🇮🇳', 'us' => '🇺🇸',
                                            'id' => '🇮🇩', 'th' => '🇹🇭', 'mx' => '🇲🇽', 'ng' => '🇳🇬',
                                            'bd' => '🇧🇩', 'ph' => '🇵🇭', 'vn' => '🇻🇳', 'jp' => '🇯🇵',
                                            'default' => '🌍'
                                        ];
                                        $flag = $flags[$region->code] ?? '🌍';
                                    @endphp
                                    <span class="badge badge-primary">{{ $region->code }}</span>
                                    <strong>{{ $flag }} {{ $region->name }}</strong>
                                </td>
                                <td>
                                    <span class="badge badge-info">{{ $region->site_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-success">{{ $region->active_site_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-warning">{{ $region->site_count - $region->active_site_count }}</span>
                                </td>
                                <td>
                                    @if($region->site_count > 0)
                                        @php $usage = round(($region->active_site_count / $region->site_count) * 100, 1) @endphp
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar bg-success" role="progressbar" 
                                                 style="width: {{ $usage }}%" 
                                                 aria-valuenow="{{ $usage }}" 
                                                 aria-valuemin="0" 
                                                 aria-valuemax="100">
                                                {{ $usage }}%
                                            </div>
                                        </div>
                                    @else
                                        <span class="text-muted">0%</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ admin_url('seo/website-management?region=' . $region->code) }}" 
                                           class="btn btn-outline-primary" 
                                           title="管理{{ $region->name }}网站">
                                            <i class="feather icon-settings"></i> 管理
                                        </a>
                                        <a href="{{ admin_url('seo/site/create?region=' . $region->code) }}" 
                                           class="btn btn-outline-success" 
                                           title="添加{{ $region->name }}网站">
                                            <i class="feather icon-plus"></i> 添加
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h4 class="card-title">快速操作</h4>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    <a href="{{ admin_url('seo/site/create') }}" class="list-group-item list-group-item-action">
                        <i class="feather icon-plus text-success"></i>
                        <strong>添加新网站</strong>
                        <small class="text-muted d-block">创建新的网站配置</small>
                    </a>
                    <a href="{{ admin_url('seo/regions') }}" class="list-group-item list-group-item-action">
                        <i class="feather icon-map text-primary"></i>
                        <strong>地区管理</strong>
                        <small class="text-muted d-block">管理地区配置</small>
                    </a>
                    <a href="{{ admin_url('seo/regions-stats') }}" class="list-group-item list-group-item-action">
                        <i class="feather icon-bar-chart-2 text-info"></i>
                        <strong>地区统计</strong>
                        <small class="text-muted d-block">查看详细统计</small>
                    </a>
                    <a href="{{ admin_url('seo/cache') }}" class="list-group-item list-group-item-action">
                        <i class="feather icon-database text-warning"></i>
                        <strong>缓存管理</strong>
                        <small class="text-muted d-block">管理网站缓存</small>
                    </a>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="card mt-3">
            <div class="card-header">
                <h4 class="card-title">系统状态</h4>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>总体健康度</span>
                    @php $health = $totalSites > 0 ? round(($totalActiveSites / $totalSites) * 100) : 0 @endphp
                    <span class="badge badge-{{ $health >= 80 ? 'success' : ($health >= 60 ? 'warning' : 'danger') }}">
                        {{ $health }}%
                    </span>
                </div>
                <div class="progress mb-3" style="height: 8px;">
                    <div class="progress-bar bg-{{ $health >= 80 ? 'success' : ($health >= 60 ? 'warning' : 'danger') }}" 
                         style="width: {{ $health }}%"></div>
                </div>
                
                <small class="text-muted">
                    @if($health >= 80)
                        <i class="feather icon-check-circle text-success"></i> 系统运行良好
                    @elseif($health >= 60)
                        <i class="feather icon-alert-triangle text-warning"></i> 需要关注
                    @else
                        <i class="feather icon-alert-circle text-danger"></i> 需要优化
                    @endif
                </small>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    box-shadow: 0 0 20px rgba(0,0,0,0.1);
    border-radius: 10px;
}

.progress {
    border-radius: 10px;
}

.badge {
    font-size: 0.875rem;
}

.list-group-item {
    border: none;
    padding: 1rem;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}
</style>
