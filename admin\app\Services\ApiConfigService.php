<?php

namespace App\Services;

use Dotenv\Dotenv;
use Illuminate\Support\Facades\Cache;

class ApiConfigService
{
    /**
     * API项目相对路径配置
     * 从配置文件读取，支持环境变量覆盖
     */
    private static $apiProjectPath = null;
    
    /**
     * 缓存键名
     */
    private const CACHE_KEY = 'api_config_cache';
    private const CACHE_TTL = 300; // 5分钟缓存
    
    /**
     * 获取API项目路径
     */
    public static function getApiProjectPath(): string
    {
        if (self::$apiProjectPath === null) {
            self::$apiProjectPath = env('API_PATH');

            if (empty(self::$apiProjectPath)) {
                throw new \Exception('API_PATH未配置，请在admin/.env文件中设置API_PATH指向API项目目录');
            }
        }
        return self::$apiProjectPath;
    }
    
    /**
     * 设置API项目路径
     */
    public static function setApiProjectPath(string $path): void
    {
        self::$apiProjectPath = $path;
        // 清除缓存
        Cache::forget(self::CACHE_KEY);
    }
    
    /**
     * 获取API项目的完整路径
     */
    public static function getApiProjectFullPath(): string
    {
        $apiProjectPath = self::getApiProjectPath();

        // 从public目录开始计算路径，因为web应用实际运行在public目录下
        $publicPath = public_path();
        $calculatedPath = realpath($publicPath . '/' . $apiProjectPath);



        // 如果从public目录计算成功，直接返回
        if ($calculatedPath && is_dir($calculatedPath)) {
            return $calculatedPath;
        }

        // 如果从public目录计算失败，回退到从项目根目录计算
        $rootPath = base_path();
        $fallbackPath = realpath($rootPath . '/' . $apiProjectPath);

        if ($fallbackPath && is_dir($fallbackPath)) {
            return $fallbackPath;
        }

        // 如果realpath都失败，尝试直接拼接路径
        $directPath = $publicPath . '/' . $apiProjectPath;
        if (is_dir($directPath)) {
            return $directPath;
        }

        $directRootPath = $rootPath . '/' . $apiProjectPath;
        if (is_dir($directRootPath)) {
            return $directRootPath;
        }

        // 如果都失败，抛出异常而不是返回错误路径
        throw new \Exception("无法找到API项目目录，检查的路径：\n1. {$publicPath}/{$apiProjectPath}\n2. {$rootPath}/{$apiProjectPath}");
    }
    
    /**
     * 获取API项目的.env文件路径
     */
    public static function getApiEnvPath(): string
    {
        return self::getApiProjectFullPath() . '/.env';
    }
    
    /**
     * 加载API项目的环境变量
     */
    public static function loadApiEnv(): array
    {
        return Cache::remember(self::CACHE_KEY, self::CACHE_TTL, function () {
            $envFilePath = self::getApiEnvPath();

            if (!file_exists($envFilePath)) {
                throw new \Exception("API项目.env文件不存在: " . $envFilePath);
            }

            // 直接解析.env文件内容，避免影响当前应用的环境变量
            $envContent = file_get_contents($envFilePath);
            $envVars = [];

            foreach (explode("\n", $envContent) as $line) {
                $line = trim($line);

                // 跳过空行和注释
                if (empty($line) || strpos($line, '#') === 0) {
                    continue;
                }

                // 解析 KEY=VALUE 格式
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value);

                    // 移除行内注释（# 后面的内容）
                    if (strpos($value, '#') !== false) {
                        $value = trim(explode('#', $value)[0]);
                    }

                    // 移除引号
                    $value = trim($value, '"\'');

                    $envVars[$key] = $value;
                }
            }

            return $envVars;
        });
    }
    
    /**
     * 获取API绑定地址
     */
    public static function getApiBindAddr(): string
    {
        $config = self::loadApiEnv();

        if (!isset($config['BIND_ADDR'])) {
            throw new \Exception("无法从API项目.env文件中读取BIND_ADDR配置");
        }

        return $config['BIND_ADDR'];
    }
    
    /**
     * 获取API基础URL
     */
    public static function getApiBaseUrl(): string
    {
        try {
            // 从API项目的.env文件读取BIND_ADDR
            $bindAddr = self::getApiBindAddr();
            return "http://{$bindAddr}";
        } catch (\Exception $e) {
            throw new \Exception("无法获取API基础URL: " . $e->getMessage() .
                "\n请检查：\n1. API_PATH配置是否正确\n2. API项目.env文件是否存在\n3. BIND_ADDR是否已配置");
        }
    }
    
    /**
     * 调用API接口
     */
    public static function callApi(string $endpoint, array $params = []): string
    {
        $baseUrl = self::getApiBaseUrl();
        $url = $baseUrl . '/' . ltrim($endpoint, '/');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        $context = stream_context_create([
            'http' => [
                'timeout' => 30,
                'method' => 'GET',
            ]
        ]);
        
        $result = file_get_contents($url, false, $context);
        
        if ($result === false) {
            throw new \Exception("API调用失败: {$url}");
        }
        
        return $result;
    }
    
    /**
     * 刷新API缓存
     */
    public static function refreshApiCache(): string
    {
        try {
            return self::callApi('refresh');
        } catch (\Exception $e) {
            throw new \Exception("刷新API缓存失败: " . $e->getMessage());
        }
    }

    /**
     * 清除API同步缓存
     */
    public static function clearApiSyncCache(): string
    {
        try {
            return self::callApi('clear_sync_cache');
        } catch (\Exception $e) {
            throw new \Exception("清除API同步缓存失败: " . $e->getMessage());
        }
    }

    /**
     * 完整的缓存同步操作
     */
    public static function fullCacheSync(): array
    {
        $results = [];

        try {
            // 清除本地配置缓存，确保重新读取API项目配置
            self::clearCache();

            // 清除Laravel配置缓存，确保重新读取.env文件
            \Illuminate\Support\Facades\Cache::forget('api_config_cache');

            // 重置静态变量，强制重新计算路径
            self::$apiProjectPath = null;

            // 1. 刷新API内存缓存（域名配置、地区配置、模板配置）
            $results['refresh'] = self::refreshApiCache();

            // 2. 清除API同步缓存（强制重新从数据库加载）
            $results['clear_sync'] = self::clearApiSyncCache();

            // 3. 验证API状态
            $results['status_check'] = self::checkApiStatus() ? 'ok' : 'failed';

            return $results;

        } catch (\Exception $e) {
            throw new \Exception("完整缓存同步失败: " . $e->getMessage());
        }
    }
    
    /**
     * 检查API服务状态
     */
    public static function checkApiStatus(): bool
    {
        try {
            $response = self::callApi('health');
            return $response === 'ok' || strpos($response, 'ok') !== false;
        } catch (\Exception $e) {
            return false;
        }
    }
    
    /**
     * 获取API项目信息
     */
    public static function getApiInfo(): array
    {
        return [
            'project_path' => self::getApiProjectPath(),
            'full_path' => self::getApiProjectFullPath(),
            'env_path' => self::getApiEnvPath(),
            'env_exists' => file_exists(self::getApiEnvPath()),
            'bind_addr' => self::getApiBindAddr(),
            'base_url' => self::getApiBaseUrl(),
            'status' => self::checkApiStatus(),
        ];
    }
    
    /**
     * 清除配置缓存
     */
    public static function clearCache(): void
    {
        Cache::forget(self::CACHE_KEY);
    }

    /**
     * 验证API配置完整性
     */
    public static function validateApiConfig(): array
    {
        $issues = [];

        try {
            // 检查API_PATH配置
            $apiPath = self::getApiProjectPath();
            if (empty($apiPath)) {
                $issues[] = 'API_PATH未配置';
            }

            // 检查API项目目录
            $fullPath = self::getApiProjectFullPath();
            if (!is_dir($fullPath)) {
                $issues[] = "API项目目录不存在: {$fullPath}";
            }

            // 检查.env文件
            $envPath = self::getApiEnvPath();
            if (!file_exists($envPath)) {
                $issues[] = "API项目.env文件不存在: {$envPath}";
            }

            // 检查BIND_ADDR配置
            try {
                $bindAddr = self::getApiBindAddr();
                if (empty($bindAddr)) {
                    $issues[] = 'API项目.env文件中BIND_ADDR为空';
                }
            } catch (\Exception $e) {
                $issues[] = 'BIND_ADDR配置错误: ' . $e->getMessage();
            }

            // 检查API连接
            try {
                $status = self::checkApiStatus();
                if (!$status) {
                    $issues[] = 'API服务无法连接';
                }
            } catch (\Exception $e) {
                $issues[] = 'API连接测试失败: ' . $e->getMessage();
            }

        } catch (\Exception $e) {
            $issues[] = '配置验证异常: ' . $e->getMessage();
        }

        return $issues;
    }
    
    /**
     * 验证API项目路径
     */
    public static function validateApiPath(): array
    {
        $errors = [];
        $warnings = [];
        
        $fullPath = self::getApiProjectFullPath();
        $envPath = self::getApiEnvPath();
        
        // 检查项目目录
        if (!is_dir($fullPath)) {
            $errors[] = "API项目目录不存在: {$fullPath}";
        }
        
        // 检查.env文件
        if (!file_exists($envPath)) {
            $errors[] = ".env文件不存在: {$envPath}";
        } else {
            // 检查.env文件是否可读
            if (!is_readable($envPath)) {
                $errors[] = ".env文件不可读: {$envPath}";
            }
        }
        
        // 检查API服务状态
        if (empty($errors)) {
            if (!self::checkApiStatus()) {
                $warnings[] = "API服务可能未启动或无法访问";
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'warnings' => $warnings,
            'info' => self::getApiInfo(),
        ];
    }
}
