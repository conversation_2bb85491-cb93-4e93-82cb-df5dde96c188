# 数据库配置
DATABASE_URL=mysql://username:password@localhost:3306/database_name

# Redis配置
REDIS_URL=redis://localhost:6379

# MongoDB配置
MONGODB_URL=mongodb://localhost:27017/database_name

# 应用配置
BIND_ADDR=0.0.0.0:8080
# 注意: 新路径结构为 app/{region_code}/data 和 app/{region_code}/template
# 不再需要 APP_DATA 和 APP_TEMPLATE 环境变量

# 地区配置
DEFAULT_REGION=default
MAX_REGIONS_IN_MEMORY=10
REGION_RELOAD_INTERVAL=300
ENABLE_REGION_FALLBACK=true

# 性能配置
CACHE_TTL=3600
REGION_CACHE_TTL=3600
MAX_CACHE_SIZE=1000000

# 功能开关
ENABLE_CACHE=true
ENABLE_GEOIP=true
ENABLE_SPIDER_LOG=true
AUTO_CLEAN_INACTIVE_DOMAINS=true

# 日志配置
LOG_LEVEL=info
ENABLE_DEBUG_LOG=false

# 安全配置
API_RATE_LIMIT=1000
MAX_REQUEST_SIZE=10485760

# 允许的蜘蛛配置（逗号分隔，不区分大小写）
# 支持的蜘蛛名称：google, baidu, bing, yandex, sogou, 360spider, bytespider等
ALLOWED_SPIDERS=google,baidu,bing

# GeoIP配置
GEOIP_DATABASE_PATH=GeoLite2-Country.mmdb
GEOIP_UPDATE_INTERVAL=86400

# 监控配置
ENABLE_METRICS=true
METRICS_PORT=9090
