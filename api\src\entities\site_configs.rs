use dashmap::DashMap;
use rand::prelude::SliceRandom;
use rand::rngs::ThreadRng;
// use rand::Rng;
use regex::Regex;
use sqlx::mysql::MySqlRow;
use sqlx::MySqlPool;
use sqlx::Row;
use std::sync::Arc;
use tokio::sync::RwLock;

// use crate::util::file;

// use crate::GLOBAL_CONFIG;
// use crate::RESOURCE;

#[derive(Clone)]
pub struct SiteConfigs {
    list: Arc<RwLock<DashMap<String, SiteConfig>>>,
}

#[derive(Debug, Clone)]
pub struct SiteConfig {
    pub domain: String,
    pub generate_rules: Vec<String>, // uri规则
    pub https: bool,                 // 是否https
    pub cache: bool,                 // 是否缓存
    pub page: bool,                  // 是否页面
    pub home: bool,                  // 是否首页
    pub link: bool,                  // 是否链接
    pub region_code: String,         // 地区代码
    pub state: bool,                 // 是否启用
    pub uri_rules: Vec<Regex>,
    pub jump_rules: Vec<Regex>,
}

// 从数据库行创建 DomainConfig 的辅助方法
impl SiteConfig {
    pub async fn from_row(row: &MySqlRow) -> Self {
        // let rng = &mut rand::thread_rng();
        let domain: String = row.get("host");
        let generate_rules: Vec<String> = row
            .try_get::<String, _>("link_rules")
            .unwrap_or_else(|_| String::new())
            .split('\n')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();
        let jump_rules_str: Vec<String> = row
            .try_get::<String, _>("jump_rules")
            .unwrap_or_else(|_| String::new())
            .split('\n')
            .map(|s| s.trim().to_string())
            .filter(|s| !s.is_empty())
            .collect();
        let https: bool = row.try_get("https").unwrap_or_else(|_| false);
        let cache: bool = row.try_get("open_cache").unwrap_or_else(|_| false);
        let page: bool = row.try_get("open_page").unwrap_or_else(|_| false);
        let home: bool = row.try_get("open_home").unwrap_or_else(|_| false);
        let link: bool = row.try_get("open_link").unwrap_or_else(|_| false);
        let state: bool = row.try_get("state").unwrap_or_else(|_| false);
        let region_code: String = row.try_get("region_code").unwrap_or_else(|_| "default".to_string());
        let mut uri_rules = Vec::new();
        for uri_rule in &generate_rules {
            let uri_rule = crate::util::string::url::replace_url_tags(uri_rule);
            let re = Regex::new(&uri_rule).unwrap();
            uri_rules.push(re);
        }

        let mut jump_rules = Vec::new();
        for uri_rule in &jump_rules_str {
            let uri_rule = crate::util::string::url::replace_url_tags(uri_rule);
            let re = Regex::new(&uri_rule).unwrap();
            jump_rules.push(re);
        }

        Self {
            domain,
            generate_rules,
            https,
            cache,
            page,
            home,
            link,
            region_code,
            state,
            uri_rules,
            jump_rules,
        }
    }

    pub fn get_random_uri_rule(&self, rng: &mut ThreadRng) -> String {
        match self.generate_rules.len() {
            0 => {
                // 如果没有规则，生成一个默认的路径
                format!("/{}", crate::util::random::number(rng, 3))
            },
            1 => self.generate_rules[0].clone(),
            _ => self
                .generate_rules
                .choose(rng)
                .unwrap_or_else(|| &self.generate_rules[0])
                .clone(),
        }
    }
}

impl SiteConfigs {
    pub async fn new(mysql: &MySqlPool) -> Result<Self, sqlx::Error> {
        let domains = SiteConfigs {
            list: Arc::new(RwLock::new(DashMap::new())),
        };
        domains.refresh(mysql).await?;
        Ok(domains)
    }

    pub async fn refresh(&self, mysql: &MySqlPool) -> Result<(), sqlx::Error> {
        let rows = sqlx::query("SELECT * FROM seo_site")
            .fetch_all(mysql)
            .await?;

        let new_domain_list = DashMap::with_capacity(rows.len());

        for row in rows {
            let config = SiteConfig::from_row(&row).await;
            new_domain_list.insert(config.domain.clone(), config);
        }

        let mut domain_list = self.list.write().await;
        *domain_list = new_domain_list;

        Ok(())
    }

    pub async fn get(&self, domain: &str) -> Option<SiteConfig> {
        self.list.read().await.get(domain).map(|v| v.clone())
    }

    /// 更新内存中的域名配置
    pub async fn update(&self, domain: &str, config: SiteConfig) {
        let list = self.list.write().await;  // ✅ 修复：使用写锁而不是读锁
        list.insert(domain.to_string(), config);
    }

    pub async fn get_random_domain(&self, rng: &mut ThreadRng) -> String {
        let list = self.list.read().await;
        let mut random_list = Vec::new();
        for domain in list.iter() {
            if domain.value().state {
                random_list.push(domain.value().clone());
            }
        }
        
        match random_list.choose(rng) {
            Some(domain) => {
                // 构建基础URL（协议+域名）
                let base_url = if domain.https {
                    format!("https://{}", domain.domain)
                } else {
                    format!("http://{}", domain.domain)
                };
                
                // 如果有generate_rules且page为true，随机生成一个URL路径
                if !domain.generate_rules.is_empty() && domain.page {
                    // 随机选择一个规则
                    if let Some(rule) = domain.generate_rules.choose(rng) {
                        // 使用目标网站自己的地区资源替换占位符，生成实际路径
                        let target_region_resource = crate::RESOURCE_MANAGER.get_resource(&domain.region_code);
                        let path = crate::util::string::url::replace_with_resource(rule, rng, target_region_resource);
                        format!("{}{}", base_url, path)
                    } else {
                        base_url // 如果无法选择规则，返回基础URL
                    }
                } else {
                    base_url // 如果没有规则或page为false，返回基础URL
                }
            }
            None => String::new(),
        }
    }

    pub async fn get_all(&self) -> Vec<SiteConfig> {
        self.list
            .read()
            .await
            .iter()
            .map(|v| v.value().clone())
            .collect()
    }

    /// 插入新域名配置（仅内存，不写数据库）
    /// 注意：这个方法只用于临时内存配置，实际的数据库写入在批处理中完成
    pub async fn insert(&self, domain: &str) -> SiteConfig {
        let config = SiteConfig {
            domain: domain.to_string(),
            generate_rules: Vec::new(),
            https: false,
            cache: false,
            page: false,
            home: false,
            link: false,
            region_code: "default".to_string(), // 默认地区
            state: false,
            uri_rules: Vec::new(),
            jump_rules: Vec::new(),
        };
        self.list.write().await.insert(domain.to_string(), config.clone());
        config
    }
}
