<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 添加flood字段到seo_site表
        if (Schema::hasTable('seo_site') && !Schema::hasColumn('seo_site', 'flood')) {
            Schema::table('seo_site', function (Blueprint $table) {
                $table->tinyInteger('flood')->default(0)->after('state')->comment('泛域名模式');
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 移除flood字段
        if (Schema::hasTable('seo_site') && Schema::hasColumn('seo_site', 'flood')) {
            Schema::table('seo_site', function (Blueprint $table) {
                $table->dropColumn('flood');
            });
        }
    }
};
