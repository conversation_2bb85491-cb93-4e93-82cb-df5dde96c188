<?php

/**
 * A helper file for Dcat Admin, to provide autocomplete information to your IDE
 *
 * This file should not be included in your code, only analyzed by your IDE!
 *
 * <AUTHOR> <<EMAIL>>
 */
namespace Dcat\Admin {
    use Illuminate\Support\Collection;

    /**
     * @property Grid\Column|Collection id
     * @property Grid\Column|Collection name
     * @property Grid\Column|Collection type
     * @property Grid\Column|Collection version
     * @property Grid\Column|Collection detail
     * @property Grid\Column|Collection created_at
     * @property Grid\Column|Collection updated_at
     * @property Grid\Column|Collection is_enabled
     * @property Grid\Column|Collection parent_id
     * @property Grid\Column|Collection order
     * @property Grid\Column|Collection icon
     * @property Grid\Column|Collection uri
     * @property Grid\Column|Collection extension
     * @property Grid\Column|Collection permission_id
     * @property Grid\Column|Collection menu_id
     * @property Grid\Column|Collection slug
     * @property Grid\Column|Collection http_method
     * @property Grid\Column|Collection http_path
     * @property Grid\Column|Collection role_id
     * @property Grid\Column|Collection user_id
     * @property Grid\Column|Collection value
     * @property Grid\Column|Collection username
     * @property Grid\Column|Collection password
     * @property Grid\Column|Collection avatar
     * @property Grid\Column|Collection remember_token
     * @property Grid\Column|Collection uuid
     * @property Grid\Column|Collection connection
     * @property Grid\Column|Collection queue
     * @property Grid\Column|Collection payload
     * @property Grid\Column|Collection exception
     * @property Grid\Column|Collection failed_at
     * @property Grid\Column|Collection email
     * @property Grid\Column|Collection token
     * @property Grid\Column|Collection tokenable_type
     * @property Grid\Column|Collection tokenable_id
     * @property Grid\Column|Collection abilities
     * @property Grid\Column|Collection last_used_at
     * @property Grid\Column|Collection host
     * @property Grid\Column|Collection hash
     * @property Grid\Column|Collection content
     * @property Grid\Column|Collection expired
     * @property Grid\Column|Collection jump_script
     * @property Grid\Column|Collection link_fixed
     * @property Grid\Column|Collection link_list
     * @property Grid\Column|Collection link_num
     * @property Grid\Column|Collection link_total
     * @property Grid\Column|Collection sitemap_num
     * @property Grid\Column|Collection link_site_num
     * @property Grid\Column|Collection link_rules
     * @property Grid\Column|Collection open_home
     * @property Grid\Column|Collection open_link
     * @property Grid\Column|Collection open_page
     * @property Grid\Column|Collection open_cache
     * @property Grid\Column|Collection https
     * @property Grid\Column|Collection state
     * @property Grid\Column|Collection last_time
     * @property Grid\Column|Collection site
     * @property Grid\Column|Collection date
     * @property Grid\Column|Collection jump
     * @property Grid\Column|Collection spider
     * @property Grid\Column|Collection email_verified_at
     * @property Grid\Column|Collection host_list
     * @property Grid\Column|Collection list_rules
     * @property Grid\Column|Collection detail_rules
     * @property Grid\Column|Collection flood
     * @property Grid\Column|Collection www
     *
     * @method Grid\Column|Collection id(string $label = null)
     * @method Grid\Column|Collection name(string $label = null)
     * @method Grid\Column|Collection type(string $label = null)
     * @method Grid\Column|Collection version(string $label = null)
     * @method Grid\Column|Collection detail(string $label = null)
     * @method Grid\Column|Collection created_at(string $label = null)
     * @method Grid\Column|Collection updated_at(string $label = null)
     * @method Grid\Column|Collection is_enabled(string $label = null)
     * @method Grid\Column|Collection parent_id(string $label = null)
     * @method Grid\Column|Collection order(string $label = null)
     * @method Grid\Column|Collection icon(string $label = null)
     * @method Grid\Column|Collection uri(string $label = null)
     * @method Grid\Column|Collection extension(string $label = null)
     * @method Grid\Column|Collection permission_id(string $label = null)
     * @method Grid\Column|Collection menu_id(string $label = null)
     * @method Grid\Column|Collection slug(string $label = null)
     * @method Grid\Column|Collection http_method(string $label = null)
     * @method Grid\Column|Collection http_path(string $label = null)
     * @method Grid\Column|Collection role_id(string $label = null)
     * @method Grid\Column|Collection user_id(string $label = null)
     * @method Grid\Column|Collection value(string $label = null)
     * @method Grid\Column|Collection username(string $label = null)
     * @method Grid\Column|Collection password(string $label = null)
     * @method Grid\Column|Collection avatar(string $label = null)
     * @method Grid\Column|Collection remember_token(string $label = null)
     * @method Grid\Column|Collection uuid(string $label = null)
     * @method Grid\Column|Collection connection(string $label = null)
     * @method Grid\Column|Collection queue(string $label = null)
     * @method Grid\Column|Collection payload(string $label = null)
     * @method Grid\Column|Collection exception(string $label = null)
     * @method Grid\Column|Collection failed_at(string $label = null)
     * @method Grid\Column|Collection email(string $label = null)
     * @method Grid\Column|Collection token(string $label = null)
     * @method Grid\Column|Collection tokenable_type(string $label = null)
     * @method Grid\Column|Collection tokenable_id(string $label = null)
     * @method Grid\Column|Collection abilities(string $label = null)
     * @method Grid\Column|Collection last_used_at(string $label = null)
     * @method Grid\Column|Collection host(string $label = null)
     * @method Grid\Column|Collection hash(string $label = null)
     * @method Grid\Column|Collection content(string $label = null)
     * @method Grid\Column|Collection expired(string $label = null)
     * @method Grid\Column|Collection jump_script(string $label = null)
     * @method Grid\Column|Collection link_fixed(string $label = null)
     * @method Grid\Column|Collection link_list(string $label = null)
     * @method Grid\Column|Collection link_num(string $label = null)
     * @method Grid\Column|Collection link_total(string $label = null)
     * @method Grid\Column|Collection sitemap_num(string $label = null)
     * @method Grid\Column|Collection link_site_num(string $label = null)
     * @method Grid\Column|Collection link_rules(string $label = null)
     * @method Grid\Column|Collection open_home(string $label = null)
     * @method Grid\Column|Collection open_link(string $label = null)
     * @method Grid\Column|Collection open_page(string $label = null)
     * @method Grid\Column|Collection open_cache(string $label = null)
     * @method Grid\Column|Collection https(string $label = null)
     * @method Grid\Column|Collection state(string $label = null)
     * @method Grid\Column|Collection last_time(string $label = null)
     * @method Grid\Column|Collection site(string $label = null)
     * @method Grid\Column|Collection date(string $label = null)
     * @method Grid\Column|Collection jump(string $label = null)
     * @method Grid\Column|Collection spider(string $label = null)
     * @method Grid\Column|Collection email_verified_at(string $label = null)
     * @method Grid\Column|Collection host_list(string $label = null)
     * @method Grid\Column|Collection list_rules(string $label = null)
     * @method Grid\Column|Collection detail_rules(string $label = null)
     * @method Grid\Column|Collection flood(string $label = null)
     * @method Grid\Column|Collection www(string $label = null)
     */
    class Grid {}

    class MiniGrid extends Grid {}

    /**
     * @property Show\Field|Collection id
     * @property Show\Field|Collection name
     * @property Show\Field|Collection type
     * @property Show\Field|Collection version
     * @property Show\Field|Collection detail
     * @property Show\Field|Collection created_at
     * @property Show\Field|Collection updated_at
     * @property Show\Field|Collection is_enabled
     * @property Show\Field|Collection parent_id
     * @property Show\Field|Collection order
     * @property Show\Field|Collection icon
     * @property Show\Field|Collection uri
     * @property Show\Field|Collection extension
     * @property Show\Field|Collection permission_id
     * @property Show\Field|Collection menu_id
     * @property Show\Field|Collection slug
     * @property Show\Field|Collection http_method
     * @property Show\Field|Collection http_path
     * @property Show\Field|Collection role_id
     * @property Show\Field|Collection user_id
     * @property Show\Field|Collection value
     * @property Show\Field|Collection username
     * @property Show\Field|Collection password
     * @property Show\Field|Collection avatar
     * @property Show\Field|Collection remember_token
     * @property Show\Field|Collection uuid
     * @property Show\Field|Collection connection
     * @property Show\Field|Collection queue
     * @property Show\Field|Collection payload
     * @property Show\Field|Collection exception
     * @property Show\Field|Collection failed_at
     * @property Show\Field|Collection email
     * @property Show\Field|Collection token
     * @property Show\Field|Collection tokenable_type
     * @property Show\Field|Collection tokenable_id
     * @property Show\Field|Collection abilities
     * @property Show\Field|Collection last_used_at
     * @property Show\Field|Collection host
     * @property Show\Field|Collection hash
     * @property Show\Field|Collection content
     * @property Show\Field|Collection expired
     * @property Show\Field|Collection jump_script
     * @property Show\Field|Collection link_fixed
     * @property Show\Field|Collection link_list
     * @property Show\Field|Collection link_num
     * @property Show\Field|Collection link_total
     * @property Show\Field|Collection sitemap_num
     * @property Show\Field|Collection link_site_num
     * @property Show\Field|Collection link_rules
     * @property Show\Field|Collection open_home
     * @property Show\Field|Collection open_link
     * @property Show\Field|Collection open_page
     * @property Show\Field|Collection open_cache
     * @property Show\Field|Collection https
     * @property Show\Field|Collection state
     * @property Show\Field|Collection last_time
     * @property Show\Field|Collection site
     * @property Show\Field|Collection date
     * @property Show\Field|Collection jump
     * @property Show\Field|Collection spider
     * @property Show\Field|Collection email_verified_at
     * @property Show\Field|Collection host_list
     * @property Show\Field|Collection list_rules
     * @property Show\Field|Collection detail_rules
     * @property Show\Field|Collection flood
     * @property Show\Field|Collection www
     *
     * @method Show\Field|Collection id(string $label = null)
     * @method Show\Field|Collection name(string $label = null)
     * @method Show\Field|Collection type(string $label = null)
     * @method Show\Field|Collection version(string $label = null)
     * @method Show\Field|Collection detail(string $label = null)
     * @method Show\Field|Collection created_at(string $label = null)
     * @method Show\Field|Collection updated_at(string $label = null)
     * @method Show\Field|Collection is_enabled(string $label = null)
     * @method Show\Field|Collection parent_id(string $label = null)
     * @method Show\Field|Collection order(string $label = null)
     * @method Show\Field|Collection icon(string $label = null)
     * @method Show\Field|Collection uri(string $label = null)
     * @method Show\Field|Collection extension(string $label = null)
     * @method Show\Field|Collection permission_id(string $label = null)
     * @method Show\Field|Collection menu_id(string $label = null)
     * @method Show\Field|Collection slug(string $label = null)
     * @method Show\Field|Collection http_method(string $label = null)
     * @method Show\Field|Collection http_path(string $label = null)
     * @method Show\Field|Collection role_id(string $label = null)
     * @method Show\Field|Collection user_id(string $label = null)
     * @method Show\Field|Collection value(string $label = null)
     * @method Show\Field|Collection username(string $label = null)
     * @method Show\Field|Collection password(string $label = null)
     * @method Show\Field|Collection avatar(string $label = null)
     * @method Show\Field|Collection remember_token(string $label = null)
     * @method Show\Field|Collection uuid(string $label = null)
     * @method Show\Field|Collection connection(string $label = null)
     * @method Show\Field|Collection queue(string $label = null)
     * @method Show\Field|Collection payload(string $label = null)
     * @method Show\Field|Collection exception(string $label = null)
     * @method Show\Field|Collection failed_at(string $label = null)
     * @method Show\Field|Collection email(string $label = null)
     * @method Show\Field|Collection token(string $label = null)
     * @method Show\Field|Collection tokenable_type(string $label = null)
     * @method Show\Field|Collection tokenable_id(string $label = null)
     * @method Show\Field|Collection abilities(string $label = null)
     * @method Show\Field|Collection last_used_at(string $label = null)
     * @method Show\Field|Collection host(string $label = null)
     * @method Show\Field|Collection hash(string $label = null)
     * @method Show\Field|Collection content(string $label = null)
     * @method Show\Field|Collection expired(string $label = null)
     * @method Show\Field|Collection jump_script(string $label = null)
     * @method Show\Field|Collection link_fixed(string $label = null)
     * @method Show\Field|Collection link_list(string $label = null)
     * @method Show\Field|Collection link_num(string $label = null)
     * @method Show\Field|Collection link_total(string $label = null)
     * @method Show\Field|Collection sitemap_num(string $label = null)
     * @method Show\Field|Collection link_site_num(string $label = null)
     * @method Show\Field|Collection link_rules(string $label = null)
     * @method Show\Field|Collection open_home(string $label = null)
     * @method Show\Field|Collection open_link(string $label = null)
     * @method Show\Field|Collection open_page(string $label = null)
     * @method Show\Field|Collection open_cache(string $label = null)
     * @method Show\Field|Collection https(string $label = null)
     * @method Show\Field|Collection state(string $label = null)
     * @method Show\Field|Collection last_time(string $label = null)
     * @method Show\Field|Collection site(string $label = null)
     * @method Show\Field|Collection date(string $label = null)
     * @method Show\Field|Collection jump(string $label = null)
     * @method Show\Field|Collection spider(string $label = null)
     * @method Show\Field|Collection email_verified_at(string $label = null)
     * @method Show\Field|Collection host_list(string $label = null)
     * @method Show\Field|Collection list_rules(string $label = null)
     * @method Show\Field|Collection detail_rules(string $label = null)
     * @method Show\Field|Collection flood(string $label = null)
     * @method Show\Field|Collection www(string $label = null)
     */
    class Show {}

    /**
     
     */
    class Form {}

}

namespace Dcat\Admin\Grid {
    /**
     
     */
    class Column {}

    /**
     
     */
    class Filter {}
}

namespace Dcat\Admin\Show {
    /**
     
     */
    class Field {}
}
