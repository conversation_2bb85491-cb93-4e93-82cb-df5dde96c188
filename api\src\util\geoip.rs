use std::collections::HashSet;
use std::net::IpAddr;
use std::str::FromStr;
use std::sync::RwLock;
use maxminddb::geoip2::Country;
use std::sync::Arc;
use once_cell::sync::Lazy;
use std::time::Instant;
use std::num::NonZeroUsize;
use ipnet::IpNet;

// GeoIP检查器，用于检查IP是否属于目标国家
pub struct GeoIPChecker {
    // GeoIP数据库读取器
    readers: Vec<maxminddb::Reader<Vec<u8>>>,
    // 目标国家列表
    target_countries: HashSet<String>,
    // 上次刷新时间
    last_refresh: RwLock<Instant>,
    // IP缓存，用于减少频繁查询
    ip_cache: RwLock<lru::LruCache<String, (bool, Instant)>>,
    // 预加载的目标国家IP网段
    preloaded_networks: RwLock<Vec<(IpNet, bool)>>,
    // 缓存TTL（秒）
    cache_ttl: u64,
}

impl GeoIPChecker {
    // 创建新的GeoIP检查器
    pub fn new() -> Self {
        // 从环境变量中读取配置
        let db_paths = std::env::var("GEOIP_DB_PATHS").unwrap_or_default();
        let target_countries = std::env::var("TARGET_COUNTRIES").unwrap_or_default();
        let preload_target = std::env::var("GEOIP_PRELOAD_TARGET")
            .unwrap_or_else(|_| "false".to_string())
            .to_lowercase() == "true";
        let cache_ttl = std::env::var("GEOIP_CACHE_TTL_SECS")
            .unwrap_or_else(|_| "7200".to_string())
            .parse::<u64>()
            .unwrap_or(7200);

        // 解析目标国家列表
        let countries: HashSet<String> = target_countries
            .split(',')
            .map(|s| s.trim().to_uppercase())
            .filter(|s| !s.is_empty())
            .collect();

        // 加载GeoIP数据库文件
        let mut readers = Vec::new();
        for path in db_paths.split(',').map(|s| s.trim()).filter(|s| !s.is_empty()) {
            match maxminddb::Reader::open_readfile(path) {
                Ok(reader) => {
                    println!("成功加载GeoIP数据库: {}", path);
                    readers.push(reader);
                }
                Err(e) => {
                    eprintln!("加载GeoIP数据库失败 {}: {}", path, e);
                }
            }
        }

        // 创建IP缓存
        let cache_size = NonZeroUsize::new(10000).unwrap();
        let ip_cache = RwLock::new(lru::LruCache::new(cache_size));
        
        // 创建预加载网段存储
        let preloaded_networks = RwLock::new(Vec::new());

        let checker = Self {
            readers,
            target_countries: countries,
            last_refresh: RwLock::new(Instant::now()),
            ip_cache,
            preloaded_networks,
            cache_ttl,
        };

        // 如果启用了预加载功能，预加载目标国家的IP网段
        if preload_target && !checker.readers.is_empty() && !checker.target_countries.is_empty() {
            println!("正在预加载目标国家IP网段到内存缓存...");
            checker.preload_target_countries();
        }

        checker
    }

    // 预加载目标国家的IP网段到内存
    fn preload_target_countries(&self) {
        if self.readers.is_empty() || self.target_countries.is_empty() {
            return;
        }

        // 创建一个临时存储
        let mut networks = Vec::new();

        // 目前这只是一个示例实现，实际预加载逻辑需要根据你的GeoIP数据库格式来定制
        // 因为maxminddb库不直接支持遍历所有网段，这里只能模拟一个简单实现

        // 可以采样一些常见的IP段进行预加载
        // 这里仅作示例，实际应该根据你的业务需求来优化
        for &country_code in &["TR", "CY", "XK", "MK", "AL", "DE", "FR", "NL"] {
            // 检查是否是目标国家
            let is_target = self.target_countries.contains(country_code);
            
            // 这里应该有真实的IP范围加载逻辑
            // 假设我们已经知道了一些IP网段
            // 实际应用中这些数据可能来自外部文件或数据库
            if country_code == "TR" {
                if let Ok(net) = "**********/16".parse::<IpNet>() {
                    networks.push((net, is_target));
                }
            } else if country_code == "DE" {
                if let Ok(net) = "*********/8".parse::<IpNet>() {
                    networks.push((net, is_target));
                }
            }
            // ... 为其他国家添加网段
        }

        // 简单打印一下目标国家
        println!("目标国家列表: {:?}", self.target_countries);
        println!("已预加载 {} 个IP网段", networks.len());

        // 将结果存储到预加载缓存中
        let mut preloaded = self.preloaded_networks.write().unwrap();
        *preloaded = networks;
        println!("预加载完成");
    }

    // 检查IP是否属于目标国家
    pub fn is_target_country(&self, ip: &str) -> bool {
        // 如果没有配置GeoIP数据库或目标国家，返回true
        if self.readers.is_empty() || self.target_countries.is_empty() {
            return true;
        }

        // 获取当前时间
        let now = Instant::now();

        // 检查IP缓存
        {
            let cache = self.ip_cache.read().unwrap();
            if let Some(&(is_target, timestamp)) = cache.peek(ip) {
                // 检查缓存是否过期
                if now.duration_since(timestamp).as_secs() < self.cache_ttl {
                    return is_target;
                }
                // 如果缓存已过期，继续查询
            }
        }

        // 如果IP无法解析，默认返回true
        let ip_addr = match IpAddr::from_str(ip) {
            Ok(addr) => addr,
            Err(_) => return true,
        };

        // 检查预加载的网段
        {
            let preloaded = self.preloaded_networks.read().unwrap();
            if !preloaded.is_empty() {
                for (network, is_target) in preloaded.iter() {
                    if network.contains(&ip_addr) {
                        // 将结果添加到缓存
                        {
                            let mut cache = self.ip_cache.write().unwrap();
                            cache.put(ip.to_string(), (*is_target, now));
                        }
                        
                        return *is_target;
                    }
                }
            }
        }

        // 查询所有GeoIP数据库
        for reader in &self.readers {
            if let Ok(country) = reader.lookup::<Country>(ip_addr) {
                if let Some(country) = country.country {
                    if let Some(iso_code) = country.iso_code {
                        let is_target = self.target_countries.contains(&iso_code.to_uppercase());
                        
                        // 将结果添加到缓存，带上时间戳
                        {
                            let mut cache = self.ip_cache.write().unwrap();
                            cache.put(ip.to_string(), (is_target, now));
                        }
                        
                        return is_target;
                    }
                }
            }
        }

        // 默认返回true，当无法判断时
        // 也将此结果添加到缓存
        {
            let mut cache = self.ip_cache.write().unwrap();
            cache.put(ip.to_string(), (true, now));
        }
        
        true
    }
    
    // 清除缓存
    pub fn clear_cache(&self) {
        // 清除IP缓存
        {
            let mut cache = self.ip_cache.write().unwrap();
            cache.clear();
        }
        
        // 更新刷新时间
        *self.last_refresh.write().unwrap() = Instant::now();
        
        // 如果需要也可以重新预加载目标国家
        let preload_target = std::env::var("GEOIP_PRELOAD_TARGET")
            .unwrap_or_else(|_| "false".to_string())
            .to_lowercase() == "true";
            
        if preload_target {
            self.preload_target_countries();
        }
    }
    
    // 清理过期缓存
    pub fn clean_expired_cache(&self) {
        let now = Instant::now();
        let mut expired_count = 0;
        
        // 清理过期的IP缓存
        {
            let mut cache = self.ip_cache.write().unwrap();
            let mut expired_keys = Vec::new();
            
            // 先收集过期的键
            for (key, (_, timestamp)) in cache.iter() {
                if now.duration_since(*timestamp).as_secs() >= self.cache_ttl {
                    expired_keys.push(key.clone());
                    expired_count += 1;
                }
            }
            
            // 然后删除它们
            for key in expired_keys {
                cache.pop(&key);
            }
        }
        
        if expired_count > 0 {
            println!("已清理 {} 个过期的IP缓存项", expired_count);
        }
    }
}

// 全局GeoIP检查器实例
pub static GEOIP_CHECKER: Lazy<Arc<GeoIPChecker>> = Lazy::new(|| {
    Arc::new(GeoIPChecker::new())
}); 