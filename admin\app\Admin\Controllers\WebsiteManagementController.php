<?php

namespace App\Admin\Controllers;

use App\Admin\Services\RegionMenuService;
use App\Admin\Actions\Grid\BatchEnableWebsite;
use App\Admin\Actions\Grid\BatchDisableWebsite;
use App\Admin\Actions\Grid\BatchDeleteWebsite;
use App\Admin\Actions\Grid\BatchEditWebsite;
use App\Models\SeoRegion;
use App\Models\SeoTotal;
use App\Models\SeoSite;
use App\Models\SeoMoban;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Illuminate\Http\Request;

class WebsiteManagementController extends AdminController
{
    protected $title = '网站管理';
    
    /**
     * 网站管理主页面
     */
    public function index(Content $content)
    {
        $regionCode = request('region');
        
        if ($regionCode) {
            // 显示特定地区的网站
            return $this->regionWebsites($content, $regionCode);
        } else {
            // 显示网站管理总览
            return $this->overview($content);
        }
    }
    
    /**
     * 网站管理总览
     */
    protected function overview(Content $content)
    {
        $stats = RegionMenuService::getRegionWebsiteStats();
        
        return $content
            ->title('网站管理总览')
            ->description('多地区网站统一管理')
            ->body(view('admin.website-overview', [
                'regions' => $stats,
                'totalSites' => $stats->sum('site_count'),
                'totalActiveSites' => $stats->sum('active_site_count')
            ]));
    }
    
    /**
     * 特定地区的网站管理
     */
    protected function regionWebsites(Content $content, $regionCode)
    {
        $region = SeoRegion::where('code', $regionCode)->first();

        if (!$region) {
            abort(404, "地区 {$regionCode} 不存在");
        }

        // 获取地区统计数据
        $regionStats = SeoTotal::getRegionStats($regionCode);

        // 获取网站数量统计
        $siteStats = [
            'total_sites' => \App\Models\SeoSite::where('region_code', $regionCode)->count(),
            'active_sites' => \App\Models\SeoSite::where('region_code', $regionCode)->where('state', 1)->count(),
            'https_sites' => \App\Models\SeoSite::where('region_code', $regionCode)->where('https', 1)->count(),
        ];

        return $content
            ->title("网站管理 - {$region->flag} {$region->name}")
            ->description("管理 {$region->name} 地区的网站")
            ->body(function (Row $row) use ($region, $regionCode, $regionStats, $siteStats) {
                // 添加头部信息
                $row->column(12, view('admin.region-website-header', [
                    'region' => $region,
                    'regionCode' => $regionCode,
                    'regionStats' => $regionStats,
                    'siteStats' => $siteStats
                ]));

                // 添加网站列表
                $row->column(12, $this->grid($regionCode));
            });
    }
    
    /**
     * Make a grid builder.
     */
    protected function grid($regionCode = null)
    {
        return Grid::make(\App\Models\SeoSite::class, function (Grid $grid) use ($regionCode) {
            // 如果指定了地区，则过滤
            if ($regionCode) {
                $grid->model()->where('region_code', $regionCode);
            }

            // 基本列配置
            $grid->column('id', 'ID')->sortable();
            $grid->column('host', '网站域名')->sortable();
            $grid->column('region_code', '地区');

            $grid->column('link_rules', 'URL规则')->display(function ($linkRules) {
                if (empty($linkRules)) {
                    return "<span class='text-muted'>未设置</span>";
                }

                // 限制显示长度，避免过长
                $maxLength = 50;
                if (strlen($linkRules) > $maxLength) {
                    $truncated = mb_substr($linkRules, 0, $maxLength) . '...';
                    return "<span title='" . htmlspecialchars($linkRules) . "'>{$truncated}</span>";
                }

                return "<span class='text-info'>{$linkRules}</span>";
            })->width(200);

            $grid->column('https', 'HTTPS')->display(function ($value) {
                return $value ? '<span class="badge badge-success">是</span>' : '<span class="badge badge-secondary">否</span>';
            });

            $grid->column('state', '状态')->display(function ($value) {
                return $value ? '<span class="badge badge-success">启用</span>' : '<span class="badge badge-secondary">禁用</span>';
            });

            $grid->column('last_time', '最后爬行时间')->display(function () {
                // 获取Redis中的最后爬取时间
                $keyName = 'lasttime:'.$this->host;

                // 从Redis获取数据
                $redisVal = null;
                try {
                    $redisVal = \Illuminate\Support\Facades\Redis::get($keyName);
                } catch (\Exception $e) {
                    // Redis获取失败，忽略错误
                }

                // 从Redis获取的值
                if (!empty($redisVal)) {
                    // 根据网站的地区代码获取对应时区
                    $region = \App\Models\SeoRegion::where('code', $this->region_code)->first();
                    $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

                    try {
                        $date = new \DateTime();
                        $date->setTimestamp($redisVal);
                        $date->setTimezone(new \DateTimeZone($timezone));
                        return '<span class="text-success" title="Redis缓存时间">' . $date->format('Y-m-d H:i:s') . '</span>';
                    } catch (\Exception $e) {
                        return '<span class="text-warning" title="时区转换失败">' . date('Y-m-d H:i:s', $redisVal) . '</span>';
                    }
                }

                // 从数据库获取的值
                if (!empty($this->last_time)) {
                    // 根据网站的地区代码获取对应时区
                    $region = \App\Models\SeoRegion::where('code', $this->region_code)->first();
                    $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

                    try {
                        $date = new \DateTime();
                        $date->setTimestamp($this->last_time);
                        $date->setTimezone(new \DateTimeZone($timezone));
                        return '<span class="text-info" title="数据库时间">' . $date->format('Y-m-d H:i:s') . '</span>';
                    } catch (\Exception $e) {
                        return '<span class="text-warning" title="时区转换失败">' . date('Y-m-d H:i:s', $this->last_time) . '</span>';
                    }
                }

                return '<span class="text-muted">未爬取</span>';
            })->sortable();

            // 设置分页
            $grid->paginate(50);

            // 添加过滤器
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('host', '域名')->width(3);
                $filter->equal('state', '状态')->select([1 => '启用', 0 => '禁用'])->width(3);
                $filter->equal('https', 'HTTPS')->select([1 => '开启', 0 => '关闭'])->width(3);
            });

            // 添加操作列
            $grid->actions(function (Grid\Displayers\Actions $actions) use ($regionCode) {
                $id = $actions->getKey();
                $host = $actions->row->host;

                // 清除默认操作
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();

                // 添加编辑按钮
                $editUrl = admin_url("seo/site/{$id}/edit");
                $params = [];

                if ($regionCode) {
                    $params['return_region'] = $regionCode;
                }

                // 添加当前页面URL作为return_url
                $currentUrl = request()->fullUrl();
                $params['return_url'] = $currentUrl;

                if (!empty($params)) {
                    $editUrl .= '?' . http_build_query($params);
                }

                $actions->append("<a href='{$editUrl}' class='btn btn-primary btn-xs'><i class='fa fa-edit'></i> 编辑</a>");

                // 添加删除按钮
                $deleteUrl = admin_url("seo/site/{$id}");
                $actions->append("<a href='javascript:void(0)' class='btn btn-danger btn-xs website-delete-btn' data-url='{$deleteUrl}' data-id='{$id}' data-host='{$host}' title='删除网站'><i class='fa fa-trash'></i> 删除</a>");
            });

            // 添加批量操作
            $grid->batchActions([
                new BatchEnableWebsite(),
                new BatchDisableWebsite(),
                new BatchEditWebsite(),
                new BatchDeleteWebsite(),
            ]);

            // 禁用一些功能避免冲突
            $grid->disableCreateButton();
        });

        // 删除功能由全局delete-refresh.js处理
    }
    
    /**
     * Make a form builder.
     */
    protected function form()
    {
        return Form::make(\App\Models\SeoSite::class, function (Form $form) {
            $form->display('id');
            $form->text('host', '网站域名')->required();
            
            $form->select('region_code', '地区')
                ->options(SeoRegion::where('status', 1)->pluck('name', 'code'))
                ->default('default')
                ->required()
                ->help('选择网站所属的地区');
                
            $form->switch('https', 'HTTPS')->default(0);
            $form->switch('state', '状态')->default(1);
            $form->switch('open_cache', '开启缓存')->default(0);
            $form->switch('open_page', '开启内页')->default(0);
            $form->switch('open_home', '开启首页')->default(0);
            $form->switch('open_link', '开启链接')->default(0);
            
            $form->textarea('link_rules', '链接规则')
                ->help("标签：{数字}{字母}{大写字母}{大小写字母}{大写字母数字}{数字字母}{随机字符}{日期}{年}{月}{日}{时}{分}{秒}{关键词}");
                
            $form->textarea('jump_rules', '跳转规则')
                ->help('默认包含生成规则,标签：{任意字符}');
        });
    }

    /**
     * 同步地区菜单
     */
    public function syncMenus(Request $request)
    {
        try {
            $result = \App\Admin\Services\MenuSyncService::syncRegionMenus();

            if ($result) {
                return response()->json([
                    'status' => true,
                    'message' => '地区菜单同步成功'
                ]);
            } else {
                return response()->json([
                    'status' => false,
                    'message' => '地区菜单同步失败'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '同步失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取网站统计数据
     */
    public function getStats(Request $request)
    {
        try {
            $regionCode = $request->get('region', 'all');

            // 构建查询
            $query = \App\Models\SeoSite::query();

            if ($regionCode !== 'all') {
                $query->where('region_code', $regionCode);
            }

            // 获取网站统计数据
            $total = $query->count();
            $active = $query->where('state', 1)->count();
            $inactive = $query->where('state', 0)->count();
            $https = $query->where('https', 1)->count();

            // 获取流量统计数据
            $trafficStats = SeoTotal::getRegionStats($regionCode);

            return response()->json([
                'status' => true,
                'data' => [
                    'total' => $total,
                    'active' => $active,
                    'inactive' => $inactive,
                    'https' => $https,
                    'today_jump' => $trafficStats['today_jump'],
                    'today_spider' => $trafficStats['today_spider'],
                    'total_jump' => $trafficStats['total_jump'],
                    'total_spider' => $trafficStats['total_spider'],
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '获取统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取详细统计数据
     */
    public function getDetailStats(Request $request)
    {
        try {
            $regionCode = $request->get('region', 'all');
            $days = $request->get('days', 30);

            // 获取地区信息
            $region = null;
            if ($regionCode !== 'all') {
                $region = SeoRegion::where('code', $regionCode)->first();
            }

            // 获取详细统计数据
            $trafficStats = SeoTotal::getRegionStats($regionCode, $days);

            // 获取每日统计数据用于图表
            $dailyStats = $trafficStats['daily_stats'];

            // 🔧 获取站点级别的详细统计数据
            $siteStats = $this->getSiteDetailStats($regionCode);

            return view('admin.website-detail-stats', [
                'region' => $region,
                'regionCode' => $regionCode,
                'trafficStats' => $trafficStats,
                'dailyStats' => $dailyStats,
                'siteStats' => $siteStats,
                'days' => $days
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => '获取详细统计数据失败: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取站点级别的详细统计数据
     */
    private function getSiteDetailStats($regionCode)
    {
        // 获取地区信息以确定时区
        $region = SeoRegion::where('code', $regionCode)->first();
        $timezone = $region ? $region->timezone : config('app.timezone', 'UTC');

        // 使用地区时区计算今天和昨天的日期
        try {
            $regionDate = new \DateTime('now', new \DateTimeZone($timezone));
            $today = intval($regionDate->format('Ymd'));

            $yesterdayDate = clone $regionDate;
            $yesterdayDate->modify('-1 day');
            $yesterday = intval($yesterdayDate->format('Ymd'));
        } catch (\Exception $e) {
            // 时区转换失败时回退到服务器时区
            $today = intval(date('Ymd'));
            $yesterday = intval(date('Ymd', strtotime('-1 day')));
        }

        // 获取该地区的所有网站
        $sites = \App\Models\SeoSite::where('region_code', $regionCode)
                                   ->select('id', 'host', 'state', 'https', 'last_time')
                                   ->get();

        $siteStats = [];

        foreach ($sites as $site) {
            // 获取今天的统计数据
            $todayStats = \App\Models\SeoTotal::where('site', $site->host)
                                             ->where('date', $today)
                                             ->first();

            // 获取昨天的统计数据
            $yesterdayStats = \App\Models\SeoTotal::where('site', $site->host)
                                                 ->where('date', $yesterday)
                                                 ->first();

            $todayJump = $todayStats ? $todayStats->jump : 0;
            $todaySpider = $todayStats ? $todayStats->spider : 0;
            $yesterdayJump = $yesterdayStats ? $yesterdayStats->jump : 0;
            $yesterdaySpider = $yesterdayStats ? $yesterdayStats->spider : 0;

            // 计算变化
            $jumpChange = $todayJump - $yesterdayJump;
            $spiderChange = $todaySpider - $yesterdaySpider;
            $jumpChangePercent = $yesterdayJump > 0 ? round(($jumpChange / $yesterdayJump) * 100, 1) : 0;
            $spiderChangePercent = $yesterdaySpider > 0 ? round(($spiderChange / $yesterdaySpider) * 100, 1) : 0;

            $siteStats[] = [
                'site' => $site,
                'today_jump' => $todayJump,
                'today_spider' => $todaySpider,
                'yesterday_jump' => $yesterdayJump,
                'yesterday_spider' => $yesterdaySpider,
                'jump_change' => $jumpChange,
                'spider_change' => $spiderChange,
                'jump_change_percent' => $jumpChangePercent,
                'spider_change_percent' => $spiderChangePercent,
                'total_today' => $todayJump + $todaySpider,
            ];
        }

        // 🔧 优化排序逻辑：先按跳转量排序，跳转量为0的再按蜘蛛量排序
        usort($siteStats, function($a, $b) {
            // 如果两个都有跳转量，按跳转量降序排序
            if ($a['today_jump'] > 0 && $b['today_jump'] > 0) {
                return $b['today_jump'] - $a['today_jump'];
            }

            // 如果只有一个有跳转量，有跳转量的排在前面
            if ($a['today_jump'] > 0 && $b['today_jump'] == 0) {
                return -1;
            }
            if ($a['today_jump'] == 0 && $b['today_jump'] > 0) {
                return 1;
            }

            // 如果两个都没有跳转量，按蜘蛛量降序排序
            return $b['today_spider'] - $a['today_spider'];
        });

        return $siteStats;
    }

    /**
     * 批量编辑页面
     */
    public function batchEdit(Request $request)
    {
        $ids = explode(',', $request->get('ids', ''));
        $ids = array_filter($ids);

        if (empty($ids)) {
            return redirect()->back()->with('error', '没有选择要编辑的网站');
        }

        // 获取选中的网站信息
        $websites = SeoSite::whereIn('id', $ids)->get(['id', 'host']);

        if ($websites->isEmpty()) {
            return redirect()->back()->with('error', '选中的网站不存在');
        }

        // 处理表单提交
        if ($request->isMethod('post')) {
            return $this->processBatchEdit($request, $ids);
        }

        // 使用Content布局渲染批量编辑页面
        return Content::make()
            ->title('批量编辑网站')
            ->description('共' . count($websites) . '个网站')
            ->body($this->batchEditForm($websites, $ids));
    }

    /**
     * 批量编辑表单 - 完全复制普通编辑页面的结构
     */
    protected function batchEditForm($websites, $ids)
    {
        return Form::make(\App\Models\SeoSite::class, function (Form $form) use ($websites, $ids) {
            // 设置表单提交地址
            $form->action(admin_url('seo/website-management/batch-edit'));
            $form->method('POST');

            // 隐藏字段
            $form->hidden('ids')->value(implode(',', $ids));

            // 批量编辑保存回调（实际不会被调用，因为我们有自定义处理）
            $form->saved(function (Form $form, $result) {
                return $result;
            });

            // === 完全复制普通编辑页面的结构 ===

            // ID字段 - 批量编辑特殊处理
            $displayIds = $websites->take(3)->pluck('id')->implode(', ');
            if (count($websites) > 3) {
                $displayIds .= ' 等';
            }
            $form->display('id')->value($displayIds);

            // 缓存统计 - 批量编辑特殊处理
            $form->html(function() use ($websites) {
                $totalCount = count($websites);
                return "<div id='cache-stats'>批量编辑: 共选中 {$totalCount} 个网站</div>";
            }, '缓存数');

            // 域名字段 - 批量编辑特殊处理
            $displayHosts = $websites->take(3)->pluck('host')->implode(', ');
            if (count($websites) > 3) {
                $displayHosts .= ' 等';
            }
            $form->text('host', '域名')
                ->value($displayHosts)
                ->readonly()
                ->help('批量编辑模式：显示选中网站的域名（只读）');

            // 地区选择 - 添加"不修改"选项
            $form->select('region_code', '地区')
                ->options([''] + SeoRegion::getRegionOptions())
                ->placeholder('-- 不修改 --')
                ->help('选择后将统一设置所有选中网站的地区，留空表示不修改');

            // HTTPS设置 - 按要求跳过批量编辑

            // 模板按钮 - 完全复制普通编辑页面
            $form->html(function () {
                $html = '';
                // 获取模板数据
                $templates = SeoMoban::all();
                foreach ($templates as $template) {
                    $jsonData = htmlspecialchars($template->toJson());
                    $html .= <<<HTML
                        <button type="button" class="btn btn-primary btn-sm template-button" data-template-data='{$jsonData}'>
                            {$template->name}
                        </button>
                    HTML;
                }

                $html .= <<<HTML
                    <script>
                        function setRadioState(radioName, targetValue) {
                            var radios = $('input[name="' + radioName + '"]');
                            radios.filter('[value="' + targetValue + '"]').prop('checked', true);
                            radios.filter('[value!="' + targetValue + '"]').prop('checked', false);
                        }
                        $('.template-button').on('click', function() {
                            var templateData = $(this).data('template-data');
                            $('textarea[name="link_rules"]').val(templateData.link_rules);
                            setRadioState('open_home', templateData.open_home);
                            setRadioState('open_link', templateData.open_link);
                            setRadioState('open_page', templateData.open_page);
                            setRadioState('open_cache', templateData.open_cache);
                            setRadioState('state', '1');
                        });
                    </script>
                HTML;

                return $html;
            });

            // 链接规则 - 完全复制普通编辑页面
            $form->textarea('link_rules', '链接规则')
                ->rows(5)
                ->help("
                    <strong>URL生成规则（每行一个）：</strong><br>
                    • 标签：{数字}{字母}{大写字母}{大小写字母}{大写字母数字}{数字字母}{随机字符}{日期}{年}{月}{日}{时}{分}{秒}{关键词}<br>
                    • 默认长度6，可自定义：{数字4} {数字2_6} {数字2-6}<br>
                    • 示例：/{关键词}/{数字4} 或 /{字母6}/{数字3}<br>
                    • <span class='text-info'>关键词中的空格会自动转换为短横线(-)</span><br>
                    • <strong class='text-warning'>批量编辑：填写后将覆盖所有选中网站的链接规则，留空表示不修改</strong>
                ");

            // 跳转规则 - 完全复制普通编辑页面
            $form->textarea('jump_rules', '跳转规则')
                ->rows(3)
                ->help('
                    <strong>跳转规则配置：</strong><br>
                    • 默认包含生成规则<br>
                    • 标签：{任意字符}<br>
                    • 用于配置页面跳转逻辑<br>
                    • <strong class="text-warning">批量编辑：填写后将覆盖所有选中网站的跳转规则，留空表示不修改</strong>
                ');

            // 开关设置 - 完全复制普通编辑页面，但添加"不修改"选项
            $form->radio('open_home', '开启首页')
                ->options(['' => '不修改', '1' => '开启', '0' => '关闭'])
                ->default('')
                ->help('开启后允许首页访问，批量编辑：选择后将统一设置所有选中网站');

            $form->radio('open_link', '开启轮链')
                ->options(['' => '不修改', '1' => '开启', '0' => '关闭'])
                ->default('')
                ->help('开启后显示轮链内容，批量编辑：选择后将统一设置所有选中网站');

            $form->radio('open_page', '开启页面')
                ->options(['' => '不修改', '1' => '开启', '0' => '关闭'])
                ->default('')
                ->help('开启后允许内页访问，批量编辑：选择后将统一设置所有选中网站');

            $form->radio('open_cache', '开启缓存')
                ->options(['' => '不修改', '1' => '开启', '0' => '关闭'])
                ->default('')
                ->help('开启后缓存页面内容，批量编辑：选择后将统一设置所有选中网站');

            $form->radio('state', '网站状态')
                ->options(['' => '不修改', '1' => '启用', '0' => '禁用'])
                ->default('')
                ->help('只有启用的网站才能正常访问，批量编辑：选择后将统一设置所有选中网站');

            // 禁用一些不需要的功能
            $form->disableReset();
            $form->disableViewCheck();
            $form->disableEditingCheck();
            $form->disableCreatingCheck();
        });
    }

    /**
     * 处理批量编辑
     */
    protected function processBatchEdit(Request $request, $ids)
    {
        // 如果传入的是字符串，转换为数组
        if (is_string($ids)) {
            $ids = explode(',', $request->input('ids', ''));
            $ids = array_filter($ids);
        }

        if (empty($ids)) {
            return $this->response()->error('没有选择要编辑的网站');
        }

        $updateData = [];

        // 只更新非空且不为默认值的字段
        if ($request->filled('region_code') && $request->input('region_code') !== '') {
            $updateData['region_code'] = $request->input('region_code');
        }

        if ($request->filled('state') && $request->input('state') !== '') {
            $updateData['state'] = (int)$request->input('state');
        }

        if ($request->filled('open_cache') && $request->input('open_cache') !== '') {
            $updateData['open_cache'] = (int)$request->input('open_cache');
        }

        if ($request->filled('open_page') && $request->input('open_page') !== '') {
            $updateData['open_page'] = (int)$request->input('open_page');
        }

        if ($request->filled('open_home') && $request->input('open_home') !== '') {
            $updateData['open_home'] = (int)$request->input('open_home');
        }

        if ($request->filled('open_link') && $request->input('open_link') !== '') {
            $updateData['open_link'] = (int)$request->input('open_link');
        }

        if ($request->filled('link_rules') && trim($request->input('link_rules')) !== '') {
            $updateData['link_rules'] = $request->input('link_rules');
        }

        if ($request->filled('jump_rules') && trim($request->input('jump_rules')) !== '') {
            $updateData['jump_rules'] = $request->input('jump_rules');
        }

        if (empty($updateData)) {
            return $this->response()->error('没有要更新的数据');
        }

        try {
            $count = SeoSite::whereIn('id', $ids)->update($updateData);

            // 刷新API缓存
            try {
                \App\Services\ApiConfigService::fullCacheSync();
            } catch (\Exception $e) {
                \Log::warning('批量编辑后API缓存刷新失败: ' . $e->getMessage());
            }

            // 确定跳转URL - 优先返回到referer页面
            $referer = request()->header('referer');
            $redirectUrl = admin_url('seo/website-management'); // 默认跳转

            if ($referer && strpos($referer, admin_url('')) === 0) {
                // 如果referer是admin内部页面，跳转回referer
                $redirectUrl = $referer;
            }

            return $this->response()->success("成功更新 {$count} 个网站，API缓存已同步")->redirect($redirectUrl);

        } catch (\Exception $e) {
            return $this->response()->error('更新失败: ' . $e->getMessage());
        }
    }
}
