<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class RefreshDataAction extends AbstractTool
{
    /**
     * @return string
     */
	protected $title = '全面刷新数据';

    /**
     * Handle the action request.
     *
     * @param Request $request
     *
     * @return Response
     */
    public function handle(Request $request)
    {
        try {
            // 记录日志，以便调试
            \Log::info('开始全面刷新数据');
            
            // 检查Redis配置
            $this->runCommand('redis:check');
            
            // 刷新最后爬取时间
            $this->runCommand('fix:last-time');
            
            // 刷新统计数据
            $this->runCommand('refresh:total');
            
            // 检查未通过站点
            $this->runCommand('refresh:non-passed');
            
            return $this->response()
                ->success('全面数据刷新成功')
                ->refresh();
        } catch (\Exception $e) {
            \Log::error('全面刷新数据异常: ' . $e->getMessage());
            return $this->response()->error('刷新异常: ' . $e->getMessage());
        }
    }
    
    /**
     * 运行命令并记录输出
     */
    protected function runCommand($command)
    {
        \Log::info("运行命令: $command");
        $exitCode = Artisan::call($command);
        $output = Artisan::output();
        \Log::info("命令 $command 结果: $exitCode");
        \Log::info("命令 $command 输出: \n$output");
        return $output;
    }

    /**
     * @return string|void
     */
    public function href()
    {
        // return admin_url('auth/users');
    }

    /**
	 * @return string|array|void
	 */
	public function confirm()
	{
		return ['确认全面刷新', '这将执行多个刷新命令，包括检查Redis配置、修复最后爬取时间、刷新统计数据和检查未通过站点。确定要执行吗？'];
	}

    /**
     * @param Model|Authenticatable|HasPermissions|null $user
     *
     * @return bool
     */
    protected function authorize($user): bool
    {
        return true;
    }

    /**
     * @return array
     */
    protected function parameters()
    {
        return [];
    }
} 