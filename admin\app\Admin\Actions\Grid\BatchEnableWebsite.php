<?php

namespace App\Admin\Actions\Grid;

use Dcat\Admin\Grid\BatchAction;
use Dcat\Admin\Traits\HasPermissions;
use Illuminate\Http\Request;

class BatchEnableWebsite extends BatchAction
{
    use HasPermissions;

    /**
     * @return string
     */
    public function title()
    {
        return '批量启用';
    }

    /**
     * 处理批量启用操作
     */
    public function handle(Request $request)
    {
        $keys = $this->getKey();

        if (empty($keys)) {
            return $this->response()->error('请选择要启用的网站');
        }

        try {
            $count = \App\Models\SeoSite::whereIn('id', $keys)->update(['state' => 1]);

            return $this->response()->success("成功启用 {$count} 个网站")->refresh();
        } catch (\Exception $e) {
            return $this->response()->error('启用失败: ' . $e->getMessage());
        }
    }

    /**
     * 确认对话框
     */
    public function confirm()
    {
        return ['确定要启用选中的网站吗？', '启用后网站将开始正常运行'];
    }

    /**
     * 权限检查
     */
    protected function authorize($user): bool
    {
        return true; // 根据需要调整权限
    }
}
