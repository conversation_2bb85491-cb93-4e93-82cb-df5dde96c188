<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 创建地区表
        if (!Schema::hasTable('seo_regions')) {
            Schema::create('seo_regions', function (Blueprint $table) {
                $table->id();
                $table->string('code', 10)->unique()->comment('地区代码');
                $table->string('name', 100)->comment('地区名称');
                $table->string('language', 10)->default('en')->comment('语言');
                $table->string('timezone', 50)->default('UTC')->comment('时区');
                $table->json('config')->nullable()->comment('地区配置');
                $table->string('data_version', 50)->default('1.0')->comment('数据版本');
                $table->string('fallback_region', 10)->default('default')->comment('回退地区');
                $table->integer('priority')->default(100)->comment('优先级');
                $table->boolean('status')->default(true)->comment('状态');
                $table->timestamps();
                
                $table->index(['status', 'priority']);
                $table->index('code');
            });
        }
        
        // 添加region_code字段到seo_site表
        if (Schema::hasTable('seo_site') && !Schema::hasColumn('seo_site', 'region_code')) {
            Schema::table('seo_site', function (Blueprint $table) {
                $table->string('region_code', 10)->default('default')->after('jump_rules')->comment('地区代码');
                $table->index(['region_code', 'state']);
            });
        }
        
        // 插入默认地区数据
        $this->insertDefaultRegions();
        
        // 更新现有网站数据
        $this->updateExistingSites();
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        // 移除seo_site表的region_code字段
        if (Schema::hasTable('seo_site') && Schema::hasColumn('seo_site', 'region_code')) {
            Schema::table('seo_site', function (Blueprint $table) {
                $table->dropIndex(['region_code', 'state']);
                $table->dropColumn('region_code');
            });
        }
        
        // 删除地区表
        Schema::dropIfExists('seo_regions');
    }
    
    /**
     * 插入默认地区数据
     */
    private function insertDefaultRegions()
    {
        $regions = [
            [
                'code' => 'default',
                'name' => '默认地区',
                'language' => 'en',
                'timezone' => 'UTC',
                'config' => json_encode([
                    'currency' => 'USD',
                    'domain_suffix' => '.com',
                    'country_code' => 'US',
                    'gmt_offset' => 0
                ]),
                'data_version' => '1.0',
                'fallback_region' => 'default',
                'priority' => 1,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => 'br',
                'name' => '巴西',
                'language' => 'pt',
                'timezone' => 'America/Sao_Paulo',
                'config' => json_encode([
                    'currency' => 'BRL',
                    'domain_suffix' => '.com.br',
                    'country_code' => 'BR',
                    'gmt_offset' => -3
                ]),
                'data_version' => '1.0',
                'fallback_region' => 'default',
                'priority' => 10,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => 'in',
                'name' => '印度',
                'language' => 'hi',
                'timezone' => 'Asia/Kolkata',
                'config' => json_encode([
                    'currency' => 'INR',
                    'domain_suffix' => '.in',
                    'country_code' => 'IN',
                    'gmt_offset' => 5.5
                ]),
                'data_version' => '1.0',
                'fallback_region' => 'default',
                'priority' => 20,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'code' => 'us',
                'name' => '美国',
                'language' => 'en',
                'timezone' => 'America/New_York',
                'config' => json_encode([
                    'currency' => 'USD',
                    'domain_suffix' => '.com',
                    'country_code' => 'US',
                    'gmt_offset' => -5
                ]),
                'data_version' => '1.0',
                'fallback_region' => 'default',
                'priority' => 30,
                'status' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ];
        
        foreach ($regions as $region) {
            DB::table('seo_regions')->updateOrInsert(
                ['code' => $region['code']],
                $region
            );
        }
    }
    
    /**
     * 更新现有网站数据
     */
    private function updateExistingSites()
    {
        // 将所有现有网站设置为默认地区
        if (Schema::hasTable('seo_site')) {
            DB::table('seo_site')
                ->whereNull('region_code')
                ->orWhere('region_code', '')
                ->update(['region_code' => 'default']);
        }
    }
};
