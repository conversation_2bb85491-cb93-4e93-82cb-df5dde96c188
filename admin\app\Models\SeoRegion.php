<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use App\Admin\Services\MenuSyncService;

class SeoRegion extends Model
{
    use HasDateTimeFormatter;
    
    protected $table = 'seo_regions';
    
    protected $fillable = [
        'code',
        'name',
        'language',
        'timezone',
        'config',
        'data_version',
        'fallback_region',
        'priority',
        'status',
        'jump_script'
    ];
    
    protected $casts = [
        'config' => 'array',
        'status' => 'boolean',
        'priority' => 'integer'
    ];

    /**
     * 获取地区国旗
     */
    public function getFlagAttribute()
    {
        $flags = [
            'br' => '🇧🇷', 'pk' => '🇵🇰', 'in' => '🇮🇳', 'us' => '🇺🇸',
            'id' => '🇮🇩', 'th' => '🇹🇭', 'mx' => '🇲🇽', 'ng' => '🇳🇬',
            'bd' => '🇧🇩', 'ph' => '🇵🇭', 'vn' => '🇻🇳', 'jp' => '🇯🇵',
            'default' => '🌍'
        ];

        return $flags[$this->code] ?? '🌍';
    }

    /**
     * 模型事件 - 自动同步菜单
     */
    protected static function boot()
    {
        parent::boot();

        // 地区创建后同步菜单
        static::created(function ($region) {
            MenuSyncService::syncRegionMenus();
        });

        // 地区更新后同步菜单
        static::updated(function ($region) {
            MenuSyncService::syncRegionMenus();
        });

        // 地区删除后同步菜单
        static::deleted(function ($region) {
            MenuSyncService::removeRegionMenu($region->code);
            MenuSyncService::syncRegionMenus();
        });
    }
    
    /**
     * 获取启用的地区
     */
    public static function getActiveRegions()
    {
        return self::where('status', 1)
                   ->orderBy('priority')
                   ->orderBy('code')
                   ->get();
    }
    
    /**
     * 获取地区选项（用于下拉选择）
     */
    public static function getRegionOptions()
    {
        // 确保基础地区数据存在
        self::ensureBasicRegions();

        return self::getActiveRegions()
                   ->pluck('name', 'code')
                   ->toArray();
    }

    /**
     * 确保基础地区数据存在
     */
    public static function ensureBasicRegions()
    {
        $basicRegions = [
            'default' => '默认地区',
            'br' => '巴西',
            'in' => '印度',
            'pk' => '巴基斯坦',
            'other' => '其他'
        ];

        foreach ($basicRegions as $code => $name) {
            if (!self::where('code', $code)->exists()) {
                self::create([
                    'code' => $code,
                    'name' => $name,
                    'language' => $code === 'br' ? 'pt' : ($code === 'in' ? 'hi' : ($code === 'pk' ? 'ur' : 'en')),
                    'timezone' => self::getDefaultTimezone($code),
                    'config' => json_encode(self::getDefaultConfig($code)),
                    'priority' => self::getDefaultPriority($code),
                    'status' => 1
                ]);
            }
        }
    }

    /**
     * 获取默认时区
     */
    private static function getDefaultTimezone($code)
    {
        $timezones = [
            'default' => 'UTC',
            'br' => 'America/Sao_Paulo',
            'in' => 'Asia/Kolkata',
            'pk' => 'Asia/Karachi',
            'other' => 'UTC'
        ];

        return $timezones[$code] ?? 'UTC';
    }

    /**
     * 获取默认配置
     */
    private static function getDefaultConfig($code)
    {
        $configs = [
            'default' => ['currency' => 'USD', 'domain_suffix' => '.com', 'country_code' => 'US'],
            'br' => ['currency' => 'BRL', 'domain_suffix' => '.com.br', 'country_code' => 'BR'],
            'in' => ['currency' => 'INR', 'domain_suffix' => '.co.in', 'country_code' => 'IN'],
            'pk' => ['currency' => 'PKR', 'domain_suffix' => '.com.pk', 'country_code' => 'PK'],
            'other' => ['currency' => 'USD', 'domain_suffix' => '.com', 'country_code' => 'XX']
        ];

        return $configs[$code] ?? $configs['default'];
    }

    /**
     * 获取默认优先级
     */
    private static function getDefaultPriority($code)
    {
        $priorities = [
            'default' => 1,
            'br' => 10,
            'in' => 20,
            'pk' => 30,
            'other' => 999
        ];

        return $priorities[$code] ?? 100;
    }
    
    /**
     * 根据代码获取地区
     */
    public static function getByCode($code)
    {
        return self::where('code', $code)
                   ->where('status', 1)
                   ->first();
    }
    
    /**
     * 获取地区统计信息
     */
    public function getStats()
    {
        $siteCount = DB::table('seo_site')
                       ->where('region_code', $this->code)
                       ->count();
                       
        $activeSiteCount = DB::table('seo_site')
                             ->where('region_code', $this->code)
                             ->where('state', 1)
                             ->count();
        
        return [
            'total_sites' => $siteCount,
            'active_sites' => $activeSiteCount,
            'inactive_sites' => $siteCount - $activeSiteCount
        ];
    }
    
    /**
     * 获取配置值
     */
    public function getConfigValue($key, $default = null)
    {
        return data_get($this->config, $key, $default);
    }
    
    /**
     * 设置配置值
     */
    public function setConfigValue($key, $value)
    {
        $config = $this->config ?? [];
        data_set($config, $key, $value);
        $this->config = $config;
        return $this;
    }
    
    /**
     * 检查地区是否有效
     */
    public function isValid()
    {
        return $this->status && 
               !empty($this->code) && 
               !empty($this->name);
    }
    
    /**
     * 获取guo项目的app路径
     */
    private function getAppBasePath()
    {
        // 从Admin项目定位到guo项目的app目录
        return base_path('../guo/app');
    }

    /**
     * 获取数据路径 - 路径结构: guo/app/{region_code}/data
     */
    public function getDataPath($basePath = null)
    {
        $appPath = $this->getAppBasePath();
        return "{$appPath}/{$this->code}/data";
    }

    /**
     * 获取模板路径 - 路径结构: guo/app/{region_code}/template
     */
    public function getTemplatePath($basePath = null)
    {
        $appPath = $this->getAppBasePath();
        return "{$appPath}/{$this->code}/template";
    }

    /**
     * 获取特定类型的模板路径
     */
    public function getTemplateTypePath($templateType)
    {
        $appPath = $this->getAppBasePath();
        return "{$appPath}/{$this->code}/template/{$templateType}";
    }
    
    /**
     * 检查数据目录是否存在
     */
    public function hasDataDirectory()
    {
        return is_dir($this->getDataPath());
    }
    
    /**
     * 检查模板目录是否存在
     */
    public function hasTemplateDirectory()
    {
        return is_dir($this->getTemplatePath());
    }
    
    /**
     * 创建数据目录
     */
    public function createDataDirectory()
    {
        $path = $this->getDataPath();
        if (!is_dir($path)) {
            return mkdir($path, 0755, true);
        }
        return true;
    }
    
    /**
     * 创建模板目录
     */
    public function createTemplateDirectory()
    {
        $basePath = $this->getTemplatePath();
        $result = true;

        // 创建基础模板目录
        if (!is_dir($basePath)) {
            $result = mkdir($basePath, 0755, true);
        }

        // 创建page和home子目录
        $pageDir = $this->getTemplateTypePath('page');
        $homeDir = $this->getTemplateTypePath('home');

        if (!is_dir($pageDir)) {
            $result = $result && mkdir($pageDir, 0755, true);
        }

        if (!is_dir($homeDir)) {
            $result = $result && mkdir($homeDir, 0755, true);
        }

        return $result;
    }
    
    /**
     * 获取回退地区
     */
    public function getFallbackRegion()
    {
        if ($this->fallback_region && $this->fallback_region !== $this->code) {
            return self::getByCode($this->fallback_region);
        }
        return null;
    }
    
    /**
     * 验证地区代码格式
     */
    public static function validateCode($code)
    {
        return preg_match('/^[a-z]{2,10}$/', $code);
    }
    
    /**
     * 获取支持的语言列表
     */
    public static function getSupportedLanguages()
    {
        return [
            // 主要语言
            'en' => 'English (英语)',
            'zh' => '中文 (Chinese)',
            'es' => 'Español (西班牙语)',
            'pt' => 'Português (葡萄牙语)',
            'hi' => 'हिन्दी (印地语)',
            'ar' => 'العربية (阿拉伯语)',
            'fr' => 'Français (法语)',
            'de' => 'Deutsch (德语)',
            'ja' => '日本語 (日语)',
            'ko' => '한국어 (韩语)',
            'ru' => 'Русский (俄语)',
            'it' => 'Italiano (意大利语)',

            // 南亚语言
            'ur' => 'اردو (乌尔都语)',
            'bn' => 'বাংলা (孟加拉语)',
            'ta' => 'தமிழ் (泰米尔语)',
            'te' => 'తెలుగు (泰卢固语)',
            'mr' => 'मराठी (马拉地语)',
            'gu' => 'ગુજરાતી (古吉拉特语)',
            'kn' => 'ಕನ್ನಡ (卡纳达语)',
            'ml' => 'മലയാളം (马拉雅拉姆语)',
            'pa' => 'ਪੰਜਾਬੀ (旁遮普语)',
            'ne' => 'नेपाली (尼泊尔语)',
            'si' => 'සිංහල (僧伽罗语)',

            // 东南亚语言
            'th' => 'ไทย (泰语)',
            'vi' => 'Tiếng Việt (越南语)',
            'id' => 'Bahasa Indonesia (印尼语)',
            'ms' => 'Bahasa Melayu (马来语)',
            'my' => 'မြန်မာ (缅甸语)',
            'km' => 'ខ្មែរ (高棉语)',
            'lo' => 'ລາວ (老挝语)',
            'tl' => 'Filipino (菲律宾语)',

            // 其他重要语言
            'tr' => 'Türkçe (土耳其语)',
            'fa' => 'فارسی (波斯语)',
            'he' => 'עברית (希伯来语)',
            'sw' => 'Kiswahili (斯瓦希里语)',
            'am' => 'አማርኛ (阿姆哈拉语)',
            'yo' => 'Yorùbá (约鲁巴语)',
            'ig' => 'Igbo (伊博语)',
            'ha' => 'Hausa (豪萨语)',
            'zu' => 'isiZulu (祖鲁语)',
            'af' => 'Afrikaans (南非语)',
            'nl' => 'Nederlands (荷兰语)',
            'sv' => 'Svenska (瑞典语)',
            'no' => 'Norsk (挪威语)',
            'da' => 'Dansk (丹麦语)',
            'fi' => 'Suomi (芬兰语)',
            'pl' => 'Polski (波兰语)',
            'cs' => 'Čeština (捷克语)',
            'hu' => 'Magyar (匈牙利语)',
            'ro' => 'Română (罗马尼亚语)',
            'bg' => 'Български (保加利亚语)',
            'hr' => 'Hrvatski (克罗地亚语)',
            'sr' => 'Српски (塞尔维亚语)',
            'sk' => 'Slovenčina (斯洛伐克语)',
            'sl' => 'Slovenščina (斯洛文尼亚语)',
            'et' => 'Eesti (爱沙尼亚语)',
            'lv' => 'Latviešu (拉脱维亚语)',
            'lt' => 'Lietuvių (立陶宛语)',
            'uk' => 'Українська (乌克兰语)',
            'be' => 'Беларуская (白俄罗斯语)',
            'mk' => 'Македонски (马其顿语)',
            'sq' => 'Shqip (阿尔巴尼亚语)',
            'mt' => 'Malti (马耳他语)',
            'is' => 'Íslenska (冰岛语)',
            'ga' => 'Gaeilge (爱尔兰语)',
            'cy' => 'Cymraeg (威尔士语)',
            'eu' => 'Euskera (巴斯克语)',
            'ca' => 'Català (加泰罗尼亚语)',
            'gl' => 'Galego (加利西亚语)',
        ];
    }
    
    /**
     * 获取支持的时区列表
     */
    public static function getSupportedTimezones()
    {
        return [
            // UTC和通用时区
            'UTC' => 'UTC (世界协调时间)',

            // 美洲时区
            'America/New_York' => 'America/New_York (美国东部时间)',
            'America/Chicago' => 'America/Chicago (美国中部时间)',
            'America/Denver' => 'America/Denver (美国山地时间)',
            'America/Los_Angeles' => 'America/Los_Angeles (美国西部时间)',
            'America/Anchorage' => 'America/Anchorage (阿拉斯加时间)',
            'America/Honolulu' => 'America/Honolulu (夏威夷时间)',
            'America/Sao_Paulo' => 'America/Sao_Paulo (巴西时间)',
            'America/Argentina/Buenos_Aires' => 'America/Argentina/Buenos_Aires (阿根廷时间)',
            'America/Lima' => 'America/Lima (秘鲁时间)',
            'America/Bogota' => 'America/Bogota (哥伦比亚时间)',
            'America/Caracas' => 'America/Caracas (委内瑞拉时间)',
            'America/Santiago' => 'America/Santiago (智利时间)',
            'America/Mexico_City' => 'America/Mexico_City (墨西哥时间)',
            'America/Toronto' => 'America/Toronto (加拿大东部时间)',
            'America/Vancouver' => 'America/Vancouver (加拿大西部时间)',

            // 欧洲时区
            'Europe/London' => 'Europe/London (英国时间)',
            'Europe/Paris' => 'Europe/Paris (法国时间)',
            'Europe/Berlin' => 'Europe/Berlin (德国时间)',
            'Europe/Rome' => 'Europe/Rome (意大利时间)',
            'Europe/Madrid' => 'Europe/Madrid (西班牙时间)',
            'Europe/Amsterdam' => 'Europe/Amsterdam (荷兰时间)',
            'Europe/Brussels' => 'Europe/Brussels (比利时时间)',
            'Europe/Vienna' => 'Europe/Vienna (奥地利时间)',
            'Europe/Zurich' => 'Europe/Zurich (瑞士时间)',
            'Europe/Stockholm' => 'Europe/Stockholm (瑞典时间)',
            'Europe/Oslo' => 'Europe/Oslo (挪威时间)',
            'Europe/Copenhagen' => 'Europe/Copenhagen (丹麦时间)',
            'Europe/Helsinki' => 'Europe/Helsinki (芬兰时间)',
            'Europe/Warsaw' => 'Europe/Warsaw (波兰时间)',
            'Europe/Prague' => 'Europe/Prague (捷克时间)',
            'Europe/Budapest' => 'Europe/Budapest (匈牙利时间)',
            'Europe/Bucharest' => 'Europe/Bucharest (罗马尼亚时间)',
            'Europe/Sofia' => 'Europe/Sofia (保加利亚时间)',
            'Europe/Athens' => 'Europe/Athens (希腊时间)',
            'Europe/Istanbul' => 'Europe/Istanbul (土耳其时间)',
            'Europe/Moscow' => 'Europe/Moscow (俄罗斯莫斯科时间)',
            'Europe/Kiev' => 'Europe/Kiev (乌克兰时间)',

            // 亚洲时区
            'Asia/Shanghai' => 'Asia/Shanghai (中国时间)',
            'Asia/Hong_Kong' => 'Asia/Hong_Kong (香港时间)',
            'Asia/Taipei' => 'Asia/Taipei (台湾时间)',
            'Asia/Tokyo' => 'Asia/Tokyo (日本时间)',
            'Asia/Seoul' => 'Asia/Seoul (韩国时间)',
            'Asia/Singapore' => 'Asia/Singapore (新加坡时间)',
            'Asia/Kuala_Lumpur' => 'Asia/Kuala_Lumpur (马来西亚时间)',
            'Asia/Jakarta' => 'Asia/Jakarta (印尼西部时间)',
            'Asia/Bangkok' => 'Asia/Bangkok (泰国时间)',
            'Asia/Ho_Chi_Minh' => 'Asia/Ho_Chi_Minh (越南时间)',
            'Asia/Manila' => 'Asia/Manila (菲律宾时间)',
            'Asia/Kolkata' => 'Asia/Kolkata (印度时间)',
            'Asia/Dhaka' => 'Asia/Dhaka (孟加拉国时间)',
            'Asia/Karachi' => 'Asia/Karachi (巴基斯坦时间)',
            'Asia/Kathmandu' => 'Asia/Kathmandu (尼泊尔时间)',
            'Asia/Colombo' => 'Asia/Colombo (斯里兰卡时间)',
            'Asia/Dubai' => 'Asia/Dubai (阿联酋时间)',
            'Asia/Qatar' => 'Asia/Qatar (卡塔尔时间)',
            'Asia/Kuwait' => 'Asia/Kuwait (科威特时间)',
            'Asia/Riyadh' => 'Asia/Riyadh (沙特阿拉伯时间)',
            'Asia/Baghdad' => 'Asia/Baghdad (伊拉克时间)',
            'Asia/Tehran' => 'Asia/Tehran (伊朗时间)',
            'Asia/Kabul' => 'Asia/Kabul (阿富汗时间)',
            'Asia/Tashkent' => 'Asia/Tashkent (乌兹别克斯坦时间)',
            'Asia/Almaty' => 'Asia/Almaty (哈萨克斯坦时间)',
            'Asia/Yekaterinburg' => 'Asia/Yekaterinburg (俄罗斯叶卡捷琳堡时间)',
            'Asia/Novosibirsk' => 'Asia/Novosibirsk (俄罗斯新西伯利亚时间)',
            'Asia/Krasnoyarsk' => 'Asia/Krasnoyarsk (俄罗斯克拉斯诺亚尔斯克时间)',
            'Asia/Irkutsk' => 'Asia/Irkutsk (俄罗斯伊尔库茨克时间)',
            'Asia/Yakutsk' => 'Asia/Yakutsk (俄罗斯雅库茨克时间)',
            'Asia/Vladivostok' => 'Asia/Vladivostok (俄罗斯符拉迪沃斯托克时间)',

            // 非洲时区
            'Africa/Cairo' => 'Africa/Cairo (埃及时间)',
            'Africa/Johannesburg' => 'Africa/Johannesburg (南非时间)',
            'Africa/Lagos' => 'Africa/Lagos (尼日利亚时间)',
            'Africa/Nairobi' => 'Africa/Nairobi (肯尼亚时间)',
            'Africa/Addis_Ababa' => 'Africa/Addis_Ababa (埃塞俄比亚时间)',
            'Africa/Casablanca' => 'Africa/Casablanca (摩洛哥时间)',
            'Africa/Tunis' => 'Africa/Tunis (突尼斯时间)',
            'Africa/Algiers' => 'Africa/Algiers (阿尔及利亚时间)',
            'Africa/Tripoli' => 'Africa/Tripoli (利比亚时间)',
            'Africa/Khartoum' => 'Africa/Khartoum (苏丹时间)',
            'Africa/Accra' => 'Africa/Accra (加纳时间)',
            'Africa/Abidjan' => 'Africa/Abidjan (科特迪瓦时间)',
            'Africa/Dakar' => 'Africa/Dakar (塞内加尔时间)',
            'Africa/Bamako' => 'Africa/Bamako (马里时间)',

            // 大洋洲时区
            'Australia/Sydney' => 'Australia/Sydney (澳大利亚东部时间)',
            'Australia/Melbourne' => 'Australia/Melbourne (澳大利亚墨尔本时间)',
            'Australia/Brisbane' => 'Australia/Brisbane (澳大利亚布里斯班时间)',
            'Australia/Perth' => 'Australia/Perth (澳大利亚西部时间)',
            'Australia/Adelaide' => 'Australia/Adelaide (澳大利亚阿德莱德时间)',
            'Australia/Darwin' => 'Australia/Darwin (澳大利亚达尔文时间)',
            'Pacific/Auckland' => 'Pacific/Auckland (新西兰时间)',
            'Pacific/Fiji' => 'Pacific/Fiji (斐济时间)',
            'Pacific/Honolulu' => 'Pacific/Honolulu (夏威夷时间)',
            'Pacific/Guam' => 'Pacific/Guam (关岛时间)',
            'Pacific/Port_Moresby' => 'Pacific/Port_Moresby (巴布亚新几内亚时间)',
            'Pacific/Noumea' => 'Pacific/Noumea (新喀里多尼亚时间)',
            'Pacific/Tahiti' => 'Pacific/Tahiti (塔希提时间)',
            'Pacific/Marquesas' => 'Pacific/Marquesas (马克萨斯群岛时间)',
            'Pacific/Easter' => 'Pacific/Easter (复活节岛时间)',
        ];
    }
    
    /**
     * 批量更新地区状态
     */
    public static function batchUpdateStatus($codes, $status)
    {
        return self::whereIn('code', $codes)
                   ->update(['status' => $status]);
    }
    
    /**
     * 获取地区使用统计
     */
    public static function getUsageStats()
    {
        return self::select('seo_regions.*')
                   ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code) as site_count')
                   ->selectRaw('(SELECT COUNT(*) FROM seo_site WHERE seo_site.region_code = seo_regions.code AND seo_site.state = 1) as active_site_count')
                   ->orderBy('priority')
                   ->orderBy('code')
                   ->get();
    }
}
