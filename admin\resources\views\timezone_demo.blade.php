<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>时区功能演示</title>
    @include('layouts.timezone_meta')
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 800px; margin: 0 auto; }
        .card { border: 1px solid #ddd; border-radius: 4px; padding: 20px; margin-bottom: 20px; }
        .info { background-color: #f0f8ff; padding: 10px; border-radius: 4px; }
        table { width: 100%; border-collapse: collapse; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>时区功能演示</h1>
        
        <div class="card">
            <h2>当前时区信息</h2>
            <div class="info">
                <p><strong>应用时区:</strong> @timezone</p>
                <p><strong>PHP时区:</strong> {{ date_default_timezone_get() }}</p>
                <p><strong>当前时间 (应用时区):</strong> @apptime(now())</p>
                <p><strong>当前时间 (UTC):</strong> {{ now()->setTimezone('UTC')->format('Y-m-d H:i:s') }}</p>
            </div>
        </div>
        
        <div class="card">
            <h2>时区转换示例</h2>
            <table>
                <tr>
                    <th>原始时间 (UTC)</th>
                    <th>转换后 ({{ config('app.timezone') }})</th>
                </tr>
                @php
                    $times = [
                        \Carbon\Carbon::now('UTC'),
                        \Carbon\Carbon::parse('2023-01-01 12:00:00', 'UTC'),
                        \Carbon\Carbon::parse('2023-06-01 12:00:00', 'UTC'),
                    ];
                @endphp
                
                @foreach($times as $time)
                    <tr>
                        <td>{{ $time->format('Y-m-d H:i:s') }}</td>
                        <td>@apptime($time)</td>
                    </tr>
                @endforeach
            </table>
        </div>
        
        <div class="card">
            <h2>前端时区转换</h2>
            <p>以下时间通过JavaScript自动转换:</p>
            <div id="js-times">
                <p data-time-utc="{{ now('UTC')->format('Y-m-d H:i:s') }}">加载中...</p>
                <p data-time-utc="2023-01-01 12:00:00" data-time-format="YYYY年MM月DD日 HH:mm:ss">加载中...</p>
                <p data-time-utc="2023-06-01 12:00:00">加载中...</p>
            </div>
        </div>
        
        <div class="card">
            <h2>测试MySQL时区设置</h2>
            <div class="info">
                @php
                    try {
                        $mysqlTimezone = DB::select("SELECT @@session.time_zone as tz")[0]->tz;
                        $mysqlCurrentTime = DB::select("SELECT NOW() as now")[0]->now;
                        $dbConnectionOk = true;
                    } catch (\Exception $e) {
                        $mysqlTimezone = "无法连接到数据库";
                        $mysqlCurrentTime = "无法连接到数据库";
                        $dbConnectionOk = false;
                    }
                @endphp
                
                <p><strong>MySQL会话时区:</strong> {{ $mysqlTimezone }}</p>
                <p><strong>MySQL当前时间:</strong> {{ $mysqlCurrentTime }}</p>
                
                @if($dbConnectionOk)
                    <p><strong>MySQL时间解析到PHP (应用时区):</strong> @apptime(\Carbon\Carbon::parse($mysqlCurrentTime))</p>
                @endif
            </div>
        </div>
    </div>
</body>
</html> 