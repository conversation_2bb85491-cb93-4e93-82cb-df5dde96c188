<!doctype html>

<title>CodeMirror: Smarty mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="smarty.js"></script>
<style type="text/css">.CodeMirror {border-top: 1px solid black; border-bottom: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">Smarty</a>
  </ul>
</div>

<article>
<h2>Smarty mode</h2>
<form><textarea id="code" name="code">
{extends file="parent.tpl"}
{include file="template.tpl"}

{* some example Smarty content *}
{if isset($name) && $name == 'Blog'}
  This is a {$var}.
  {$integer = 451}, {$array[] = "a"}, {$stringvar = "string"}
  {assign var='bob' value=$var.prop}
{elseif $name == $foo}
  {function name=menu level=0}
    {foreach $data as $entry}
      {if is_array($entry)}
        - {$entry@key}
        {menu data=$entry level=$level+1}
      {else}
        {$entry}
      {/if}
    {/foreach}
  {/function}
{/if}</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {
        lineNumbers: true,
        mode: "smarty"
      });
    </script>

    <br />

	<h3>Smarty 2, custom delimiters</h3>
    <form><textarea id="code2" name="code2">
{--extends file="parent.tpl"--}
{--include file="template.tpl"--}

{--* some example Smarty content *--}
{--if isset($name) && $name == 'Blog'--}
  This is a {--$var--}.
  {--$integer = 451--}, {--$array[] = "a"--}, {--$stringvar = "string"--}
  {--assign var='bob' value=$var.prop--}
{--elseif $name == $foo--}
  {--function name=menu level=0--}
    {--foreach $data as $entry--}
      {--if is_array($entry)--}
        - {--$entry@key--}
        {--menu data=$entry level=$level+1--}
      {--else--}
        {--$entry--}
      {--/if--}
    {--/foreach--}
  {--/function--}
{--/if--}</textarea></form>

    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code2"), {
        lineNumbers: true,
        mode: {
          name: "smarty",
          leftDelimiter: "{--",
          rightDelimiter: "--}"
        }
      });
    </script>

	<br />

	<h3>Smarty 3</h3>

	<textarea id="code3" name="code3">
Nested tags {$foo={counter one=1 two={inception}}+3} are now valid in Smarty 3.

<script>
function test() {
	console.log("Smarty 3 permits single curly braces followed by whitespace to NOT slip into Smarty mode.");
}
</script>

{assign var=foo value=[1,2,3]}
{assign var=foo value=['y'=>'yellow','b'=>'blue']}
{assign var=foo value=[1,[9,8],3]}

{$foo=$bar+2} {* a comment *}
{$foo.bar=1}  {* another comment *}
{$foo = myfunct(($x+$y)*3)}
{$foo = strlen($bar)}
{$foo.bar.baz=1}, {$foo[]=1}

Smarty "dot" syntax (note: embedded {} are used to address ambiguities):

{$foo.a.b.c}      => $foo['a']['b']['c']
{$foo.a.$b.c}     => $foo['a'][$b]['c']
{$foo.a.{$b+4}.c} => $foo['a'][$b+4]['c']
{$foo.a.{$b.c}}   => $foo['a'][$b['c']]

{$object->method1($x)->method2($y)}</textarea>

	<script>
		var editor = CodeMirror.fromTextArea(document.getElementById("code3"), {
			lineNumbers: true,
			mode: "smarty",
			smartyVersion: 3
		});
	</script>


    <p>A plain text/Smarty version 2 or 3 mode, which allows for custom delimiter tags.</p>

    <p><strong>MIME types defined:</strong> <code>text/x-smarty</code></p>
  </article>
