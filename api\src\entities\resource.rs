use crate::util::template_preprocessor::{ProcessedTemplate, preprocess_template};
use dashmap::DashMap;
use rand::prelude::*;
use std::path::PathBuf;
use std::fs;
use std::sync::Arc;
use std::collections::HashMap;




#[derive(Clone)]
pub struct Data {
    titles: Vec<String>,
    juzi: Vec<String>,
    keyword: String,
    pub resource: Option<Arc<Resource>>, // 可选的资源引用
}

impl Default for Data {
    fn default() -> Self {
        Self::new("")
    }
}

impl Data {
    pub fn new(keyword: &str) -> Self {
        // 暂时返回空的Data，实际的关键词搜索在外部进行
        Self {
            titles: Vec::new(),
            juzi: Vec::new(),
            keyword: keyword.to_string(),
            resource: None, // 使用全局资源
        }
    }

    pub fn new_with_keyword_search(keyword: &str, resource: &Resource) -> Self {
        let (titles, juzi) = if keyword.is_empty() {
            // 如果没有关键词，返回空向量
            (Vec::new(), Vec::new())
        } else {
            rayon::join(
                || {
                    // 搜索包含关键词的标题
                    resource
                        .get_vec_by_keyword("title", keyword)
                        .unwrap_or_default()
                },
                || {
                    // 搜索包含关键词的句子
                    resource
                        .get_vec_by_keyword("juzi", keyword)
                        .unwrap_or_default()
                },
            )
        };

        Self {
            titles,
            juzi,
            keyword: keyword.to_string(),
            resource: None, // 使用全局资源
        }
    }

    pub fn new_with_resource(keyword: &str, resource: &Resource) -> Self {
        let (titles, juzi) = if keyword.is_empty() {
            // 如果没有关键词，返回空向量
            (Vec::new(), Vec::new())
        } else {
            rayon::join(
                || {
                    // 搜索包含关键词的标题
                    resource
                        .get_vec_by_keyword("title", keyword)
                        .unwrap_or_default()
                },
                || {
                    // 搜索包含关键词的句子
                    resource
                        .get_vec_by_keyword("juzi", keyword)
                        .unwrap_or_default()
                },
            )
        };

        Self {
            titles,
            juzi,
            keyword: keyword.to_string(),
            resource: Some(Arc::new(resource.clone())), // 使用指定的资源
        }
    }

    pub fn get_title(&mut self, rng: &mut ThreadRng, default_resource: &Resource) -> String {
        if !self.titles.is_empty() {
            // 如果找到了包含关键词的标题，随机选择一个并高亮关键词
            if let Some(v) = self.titles.pop() {
                let icon = if let Some(ref resource) = self.resource {
                    resource.get_random_data(rng, "icon")
                } else {
                    default_resource.get_random_data(rng, "icon")
                };
                return v.replace(
                    &self.keyword,
                    format!("{}{}{}", icon, self.keyword, icon).as_str(),
                );
            }
        }

        // 如果没有找到包含关键词的标题，获取随机标题并插入关键词
        let (title, icon) = if let Some(ref resource) = self.resource {
            (
                resource.get_random_data(rng, "title"),
                resource.get_random_data(rng, "icon")
            )
        } else {
            (
                default_resource.get_random_data(rng, "title"),
                default_resource.get_random_data(rng, "icon")
            )
        };

        if !self.keyword.is_empty() {
            format!("{}{}{}{}", icon, self.keyword, icon, title)
        } else {
            title
        }
    }

    pub fn get_juzi(&mut self, rng: &mut ThreadRng, default_resource: &Resource) -> String {
        if !self.juzi.is_empty() {
            // 如果找到了包含关键词的句子，随机选择一个直接使用
            if let Some(v) = self.juzi.pop() {
                return v;
            }
        }

        // 如果没有找到包含关键词的句子，获取随机句子并插入关键词
        let juzi = if let Some(ref resource) = self.resource {
            resource.get_random_data(rng, "juzi")
        } else {
            default_resource.get_random_data(rng, "juzi")
        };

        if !self.keyword.is_empty() {
            // 在句子中插入关键词
            if juzi.contains("。") {
                juzi.replace("。", &format!("{}。", self.keyword))
            } else {
                format!("{}。{}", self.keyword, juzi)
            }
        } else {
            juzi
        }
    }

    pub fn set_request_data(&mut self, _request_info: &crate::entities::RequestInfo, _site_config: &crate::entities::SiteConfig) {
        // 设置请求相关数据的逻辑
    }

    /// 替换数据标签
    pub fn replace_tags(&self, content: &str) -> String {
        let mut result = content.to_string();

        // 替换关键词标签
        if result.contains("{关键词}") && !self.keyword.is_empty() {
            result = result.replace("{关键词}", &self.keyword);
        }

        result
    }
}

pub struct Resource {
    pub file_data: DashMap<String, Vec<String>>,
    pub app_template: DashMap<String, String>,
    pub app_template_index: Vec<String>,
    pub home_template: DashMap<String, String>,
    pub home_template_index: Vec<String>,
    // 添加预处理的模板结构
    pub processed_app_templates: DashMap<String, ProcessedTemplate>,
    pub processed_home_templates: DashMap<String, ProcessedTemplate>,
}

/// 全局资源管理器 - 管理所有地区的资源
pub struct ResourceManager {
    /// 所有地区的资源映射
    resources: HashMap<String, Resource>,
    /// 默认地区代码
    default_region: String,
}

impl ResourceManager {
    /// 创建新的资源管理器，启动时加载所有地区资源
    pub fn new() -> Self {
        println!("🚀 开始加载所有地区资源到内存...");

        let mut resources = HashMap::new();
        let default_region = "default".to_string();

        // 1. 加载默认地区资源
        println!("📁 加载默认地区资源...");
        let default_resource = Resource::new();
        resources.insert(default_region.clone(), default_resource);

        // 2. 扫描并加载所有地区资源
        if let Ok(entries) = std::fs::read_dir("app") {
            for entry in entries.filter_map(Result::ok) {
                if entry.path().is_dir() {
                    if let Some(region_code) = entry.file_name().to_str() {
                        if region_code != "default" {
                            println!("📁 加载地区资源: {}", region_code);
                            let region_resource = Resource::new_for_region(region_code);

                            // 检查资源是否有效（至少有一些数据）
                            if !region_resource.file_data.is_empty() ||
                               !region_resource.app_template.is_empty() ||
                               !region_resource.home_template.is_empty() {
                                resources.insert(region_code.to_string(), region_resource);
                                println!("✅ 地区资源加载成功: {}", region_code);
                            } else {
                                println!("⚠️  地区资源为空，跳过: {}", region_code);
                            }
                        }
                    }
                }
            }
        } else {
            println!("⚠️  app目录不存在，只加载默认资源");
        }

        println!("🎉 资源管理器初始化完成，共加载 {} 个地区的资源", resources.len());

        Self {
            resources,
            default_region,
        }
    }

    /// 获取指定地区的资源，如果不存在则返回默认地区资源
    pub fn get_resource(&self, region_code: &str) -> &Resource {
        self.resources.get(region_code)
            .or_else(|| self.resources.get(&self.default_region))
            .expect("默认资源必须存在")
    }

    /// 检查地区资源是否存在
    pub fn has_region(&self, region_code: &str) -> bool {
        self.resources.contains_key(region_code)
    }

    /// 获取所有已加载的地区代码
    pub fn get_loaded_regions(&self) -> Vec<String> {
        self.resources.keys().cloned().collect()
    }

    /// 获取资源统计信息
    pub fn get_stats(&self) -> HashMap<String, HashMap<String, usize>> {
        let mut stats = HashMap::new();

        for (region_code, resource) in &self.resources {
            let mut region_stats = HashMap::new();

            // 统计数据文件数量
            for entry in resource.file_data.iter() {
                region_stats.insert(format!("data_{}", entry.key()), entry.value().len());
            }

            // 统计模板数量
            region_stats.insert("app_templates".to_string(), resource.app_template.len());
            region_stats.insert("home_templates".to_string(), resource.home_template.len());

            stats.insert(region_code.clone(), region_stats);
        }

        stats
    }
}

impl Clone for Resource {
    fn clone(&self) -> Self {
        let new_resource = Self {
            file_data: DashMap::new(),
            app_template: DashMap::new(),
            app_template_index: self.app_template_index.clone(),
            home_template: DashMap::new(),
            home_template_index: self.home_template_index.clone(),
            processed_app_templates: DashMap::new(),
            processed_home_templates: DashMap::new(),
        };

        // 复制DashMap中的数据
        for entry in self.file_data.iter() {
            new_resource.file_data.insert(entry.key().clone(), entry.value().clone());
        }
        for entry in self.app_template.iter() {
            new_resource.app_template.insert(entry.key().clone(), entry.value().clone());
        }
        for entry in self.home_template.iter() {
            new_resource.home_template.insert(entry.key().clone(), entry.value().clone());
        }
        for entry in self.processed_app_templates.iter() {
            new_resource.processed_app_templates.insert(entry.key().clone(), entry.value().clone());
        }
        for entry in self.processed_home_templates.iter() {
            new_resource.processed_home_templates.insert(entry.key().clone(), entry.value().clone());
        }

        new_resource
    }
}

impl Resource {
    pub fn new() -> Self {
        let file_data = load_filedata("default");
        let (app_template, app_template_index) = load_app_template("default", "page");
        let (home_template, home_template_index) = load_app_template("default", "home");

        // 预处理所有模板
        let processed_app_templates = DashMap::new();
        let processed_home_templates = DashMap::new();
        
        // 处理应用页面模板
        for entry in app_template.iter() {
            let key = entry.key().clone();
            let template = entry.value().clone();
            let processed = preprocess_template(&template);
            processed_app_templates.insert(key, processed);
        }
        
        // 处理首页模板
        for entry in home_template.iter() {
            let key = entry.key().clone();
            let template = entry.value().clone();
            let processed = preprocess_template(&template);
            processed_home_templates.insert(key, processed);
        }

        Self {
            file_data,
            app_template,
            app_template_index,
            home_template,
            home_template_index,
            processed_app_templates,
            processed_home_templates,
        }
    }

    pub fn new_for_region(region_code: &str) -> Self {
        let file_data = load_filedata(region_code);
        let (app_template, app_template_index) = load_app_template(region_code, "page");
        let (home_template, home_template_index) = load_app_template(region_code, "home");

        // 预处理所有模板
        let processed_app_templates = DashMap::new();
        let processed_home_templates = DashMap::new();

        // 处理应用页面模板
        for entry in app_template.iter() {
            let key = entry.key().clone();
            let template = entry.value().clone();
            let processed = preprocess_template(&template);
            processed_app_templates.insert(key, processed);
        }

        // 处理首页模板
        for entry in home_template.iter() {
            let key = entry.key().clone();
            let template = entry.value().clone();
            let processed = preprocess_template(&template);
            processed_home_templates.insert(key, processed);
        }

        Self {
            file_data,
            app_template,
            app_template_index,
            home_template,
            home_template_index,
            processed_app_templates,
            processed_home_templates,
        }
    }

    pub fn get_vec(&self, key: &str) -> Option<Vec<String>> {
        self.file_data.get(key).map(|v| v.clone())
    }

    pub fn get_random_data(&self, rng: &mut ThreadRng, key: &str) -> String {
        if let Some(data) = self.file_data.get(key) {
            if !data.is_empty() {
                return data.choose(rng).unwrap_or(&String::new()).clone();
            }
        }
        String::new()
    }

    pub fn get_vec_by_keyword(&self, data_type: &str, keyword: &str) -> Option<Vec<String>> {
        if let Some(data_vec) = self.file_data.get(data_type) {
            let filtered: Vec<String> = data_vec
                .iter()
                .filter(|item| item.contains(keyword))
                .cloned()
                .collect();

            if !filtered.is_empty() {
                Some(filtered)
            } else {
                Some(Vec::new()) // 返回空Vec，表示没有匹配的内容
            }
        } else {
            None
        }
    }



    pub fn get_random_template(&self, rng: &mut ThreadRng, template_type: &str) -> String {
        let templates = match template_type {
            "home" => &self.home_template_index,
            _ => &self.app_template_index,
        };
        
        if let Some(template_name) = templates.choose(rng) {
            let template_map = match template_type {
                "home" => &self.home_template,
                _ => &self.app_template,
            };
            
            template_map.get(template_name)
                .map(|v| v.clone())
                .unwrap_or_default()
        } else {
            String::new()
        }
    }

    pub fn get_processed_random_page(&self, rng: &mut ThreadRng) -> Option<ProcessedTemplate> {
        if let Some(template_name) = self.app_template_index.choose(rng) {
            self.processed_app_templates.get(template_name).map(|v| v.clone())
        } else {
            None
        }
    }

    pub fn get_processed_random_home(&self, rng: &mut ThreadRng) -> Option<ProcessedTemplate> {
        if let Some(template_name) = self.home_template_index.choose(rng) {
            self.processed_home_templates.get(template_name).map(|v| v.clone())
        } else {
            None
        }
    }

    pub fn get_random_home(&self, rng: &mut ThreadRng) -> String {
        if let Some(template_name) = self.home_template_index.choose(rng) {
            self.home_template.get(template_name)
                .map(|v| v.clone())
                .unwrap_or_default()
        } else {
            String::new()
        }
    }

    pub fn get_random_page(&self, rng: &mut ThreadRng) -> String {
        if let Some(template_name) = self.app_template_index.choose(rng) {
            self.app_template.get(template_name)
                .map(|v| v.clone())
                .unwrap_or_default()
        } else {
            String::new()
        }
    }
}



pub fn load_filedata(region_code: &str) -> DashMap<String, Vec<String>> {
    // 使用指定地区的数据路径：app/{region_code}/data
    let region_path = format!("app/{}/data", region_code);

    let map = DashMap::new();
    let root_path = PathBuf::from(&region_path);

    if !root_path.exists() || !root_path.is_dir() {
        // 如果地区路径不存在，回退到默认路径
        if region_code != "default" {
            return load_filedata("default");
        } else {
            return map; // 如果默认路径也不存在，返回空map
        }
    }

    // 使用与RegionResource相同的逻辑：读取子目录，合并目录内所有文件
    match fs::read_dir(root_path.clone()) {
        Ok(entries) => {
            for entry in entries.filter_map(Result::ok) {
                let key = entry.file_name().into_string().unwrap_or_default();
                let entry_path = entry.path();

                if entry_path.is_dir() {
                    // 读取目录内的所有文件并合并
                    let mut combined_data = Vec::new();

                    if let Ok(files) = fs::read_dir(&entry_path) {
                        for file_entry in files.filter_map(Result::ok) {
                            let file_path = file_entry.path();
                            if file_path.is_file() {
                                if let Ok(content) = fs::read_to_string(&file_path) {
                                    let lines: Vec<String> = content
                                        .lines()
                                        .map(|line| line.trim().to_string())
                                        .filter(|line| !line.is_empty())
                                        .collect();
                                    combined_data.extend(lines);
                                }
                            }
                        }
                    }

                    if !combined_data.is_empty() {
                        map.insert(key, combined_data);
                    }
                }
            }
        }
        Err(e) => {
            eprintln!("读取地区数据目录失败 {}: {}", region_path, e);
        }
    }

    map
}

pub fn load_app_template(region_code: &str, template_type: &str) -> (DashMap<String, String>, Vec<String>) {
    // 使用指定地区的模板路径：app/{region_code}/template/{template_type}
    let region_path = format!("app/{}/template/{}", region_code, template_type);

    let map = DashMap::new();
    let mut index = Vec::new();
    let root_path = PathBuf::from(&region_path);

    if !root_path.exists() || !root_path.is_dir() {
        // 如果地区路径不存在，回退到默认路径
        if region_code != "default" {
            return load_app_template("default", template_type);
        } else {
            return (map, index); // 如果默认路径也不存在，返回空
        }
    }

    // 遍历目录加载模板文件（与RegionResource相同的逻辑）
    if let Ok(entries) = fs::read_dir(&root_path) {
        for entry in entries.flatten() {
            if let Ok(file_type) = entry.file_type() {
                if file_type.is_file() {
                    if let Some(file_name) = entry.file_name().to_str() {
                        if let Some(name) = file_name.strip_suffix(".html") {
                            if let Ok(content) = fs::read_to_string(entry.path()) {
                                map.insert(name.to_string(), content);
                                index.push(name.to_string());
                            }
                        }
                    }
                }
            }
        }
    }

    (map, index)
}
